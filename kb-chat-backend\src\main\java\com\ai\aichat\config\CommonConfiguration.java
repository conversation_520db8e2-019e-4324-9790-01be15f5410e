package com.ai.aichat.config;

import com.ai.aichat.memory.DatabaseChatMemory;
import com.ai.aichat.service.ChatMessageService;
import com.ai.aichat.service.ConversationService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.openai.OpenAiChatModel;
import com.ai.aichat.util.MilvusVectorStore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommonConfiguration {

    @Bean
    public ChatMemory chatMemory(ChatMessageService chatMessageService, ConversationService conversationService) {
        return new DatabaseChatMemory(chatMessageService, conversationService);
    }

    @Bean
    public ChatClient chatClient(OpenAiChatModel model, ChatMemory chatMemory) {
        return ChatClient
                .builder(model)
                .defaultSystem("你是一个热心的ai助手，你的名字叫豆包，请以豆包的语气与我进行对话。")
                .defaultAdvisors(
                        new SimpleLoggerAdvisor(),
                        new MessageChatMemoryAdvisor(chatMemory)
                ) // 添加一个简单的日志记录器
                .build();
    }

    @Bean
    public ChatClient ragChatClient(OpenAiChatModel openAiChatModel, ChatMemory chatMemory, MilvusVectorStore milvusVectorStore) {
        return ChatClient.builder(openAiChatModel)
                .defaultSystem("你是一个RAG知识库问答机器人，致力于解决用户提出的问题，并给出详细的解决方案")
                .defaultAdvisors(
                        new MyLoggerAdvisor(),
                        new QuestionAnswerAdvisor(milvusVectorStore),
                        new MessageChatMemoryAdvisor(chatMemory))
                .build();
    }
}
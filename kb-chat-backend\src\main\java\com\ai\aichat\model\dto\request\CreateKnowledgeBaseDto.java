package com.ai.aichat.model.dto.request;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 创建知识库请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建知识库请求")
public class CreateKnowledgeBaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 100, message = "知识库名称长度不能超过100个字符")
    @Schema(description = "知识库名称", example = "我的知识库", required = true)
    private String kb_name;

    /**
     * 知识库描述
     */
    @Size(max = 500, message = "知识库描述长度不能超过500个字符")
    @Schema(description = "知识库描述", example = "这是一个测试知识库")
    private String description;
}

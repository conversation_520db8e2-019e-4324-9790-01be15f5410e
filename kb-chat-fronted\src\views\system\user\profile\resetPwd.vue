<template>
   <div class="reset-pwd-form">
      <el-alert
         title="密码安全提示"
         type="info"
         description="为了您的账户安全，请设置复杂度较高的密码，包含字母、数字等字符，长度在6-20位之间。"
         :closable="false"
         show-icon
         class="security-tip" />

      <el-form ref="pwdRef" :model="user" :rules="rules" label-width="100px" label-position="top">
         <el-form-item label="旧密码" prop="oldPassword">
            <el-input
               v-model="user.oldPassword"
               placeholder="请输入当前密码"
               type="password"
               show-password
               prefix-icon="Lock"
               clearable />
         </el-form-item>
         <el-form-item label="新密码" prop="newPassword">
            <el-input
               v-model="user.newPassword"
               placeholder="请输入新密码（6-20位）"
               type="password"
               show-password
               prefix-icon="Key"
               clearable />
         </el-form-item>
         <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
               v-model="user.confirmPassword"
               placeholder="请再次输入新密码"
               type="password"
               show-password
               prefix-icon="Key"
               clearable />
         </el-form-item>
         <el-form-item class="form-actions">
            <el-button type="primary" @click="submit" :loading="loading" size="large">
               <el-icon><Check /></el-icon>
               修改密码
            </el-button>
            <el-button @click="resetForm" size="large">
               <el-icon><Refresh /></el-icon>
               重置
            </el-button>
         </el-form-item>
      </el-form>
   </div>
</template>

<script setup>
import { updateUserPwd } from "@/api/system/user";
import { ElMessage, ElMessageBox } from "element-plus";
import { Lock, Key, Check, Refresh } from "@element-plus/icons-vue";

const { proxy } = getCurrentInstance();
const loading = ref(false);

const user = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const equalToPassword = (rule, value, callback) => {
  if (user.newPassword !== value) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};

const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error("新密码不能为空"));
  } else if (value.length < 6 || value.length > 20) {
    callback(new Error("密码长度在 6 到 20 个字符"));
  } else if (!/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(value)) {
    callback(new Error("密码必须包含字母和数字"));
  } else {
    callback();
  }
};

const rules = ref({
  oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  newPassword: [{ validator: validatePassword, trigger: "blur" }],
  confirmPassword: [
    { required: true, message: "确认密码不能为空", trigger: "blur" },
    { validator: equalToPassword, trigger: "blur" }
  ]
});

/** 提交按钮 */
function submit() {
  proxy.$refs.pwdRef.validate(valid => {
    if (valid) {
      ElMessageBox.confirm('确定要修改密码吗？修改后需要重新登录。', '确认修改', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        loading.value = true;
        updateUserPwd(user.oldPassword, user.newPassword).then(response => {
          if (response.code === 0) {
            ElMessage.success("密码修改成功，请重新登录");
            // 清空表单
            resetForm();
            // 可以在这里跳转到登录页面
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
          } else {
            ElMessage.error(response.message || "密码修改失败");
          }
        }).catch(error => {
          console.error('修改密码失败:', error);
          ElMessage.error("密码修改失败，请检查旧密码是否正确");
        }).finally(() => {
          loading.value = false;
        });
      }).catch(() => {
        // 用户取消
      });
    }
  });
};

/** 重置表单 */
function resetForm() {
  proxy.$refs.pwdRef.resetFields();
  user.oldPassword = '';
  user.newPassword = '';
  user.confirmPassword = '';
};
</script>

<style lang="scss" scoped>
.reset-pwd-form {
  padding: 20px;

  .security-tip {
    margin-bottom: 25px;
    border-radius: 8px;

    :deep(.el-alert__content) {
      .el-alert__title {
        font-weight: 600;
        margin-bottom: 5px;
      }

      .el-alert__description {
        line-height: 1.6;
        color: #606266;
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }

  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }
  }

  .form-actions {
    margin-top: 30px;
    text-align: center;

    .el-button {
      min-width: 120px;
      border-radius: 20px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #f5a3ff 0%, #f7677c 100%);
        }
      }

      + .el-button {
        margin-left: 15px;
      }
    }
  }
}

@media (max-width: 768px) {
  .reset-pwd-form {
    padding: 15px;
  }
}
</style>

package com.ai.aichat.service;

import com.ai.aichat.model.entity.Conversation;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【conversation(会话记录表)】的数据库操作Service
* @createDate 2025-04-29 17:57:18
*/
public interface ConversationService extends IService<Conversation> {

    /**
     * 保存直接会话记录
     * @param chatType
     * @param sessionId
     */
    void saveConversation(String chatType,String sessionId);

    /**
     * 保存知识库会话记录
     * @param type
     * @param chatId
     * @param kbName
     */
    void saveConversation(String type, String chatId, String kbName);
}

//package com.ai.aichat.mcp;
//
//import com.ai.aichat.love.LoveApp;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Assertions;
//import org.springframework.ai.tool.ToolCallbackProvider;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
///**
// * MCP集成验证测试类
// * 验证MCP工具是否正确集成到工具系统中
// */
//@Slf4j
//@SpringBootTest
//@ActiveProfiles("local")
//@DisplayName("MCP集成验证测试")
//class McpIntegrationVerificationTest {
//
//    @Resource
//    private ToolCallbackProvider toolCallbackProvider;
//
//    @Resource
//    private LoveApp loveApp;
//
//    @BeforeEach
//    void setUp() {
//        log.info("开始MCP集成验证测试");
//    }
//
//    @Test
//    @DisplayName("验证MCP工具是否被正确集成")
//    void testMcpToolsIntegration() {
//        var tools = toolCallbackProvider.getToolCallbacks();
//
//        log.info("=== MCP工具集成验证 ===");
//        log.info("工具总数: {}", tools.length);
//
//        // 详细列出所有工具
//        for (int i = 0; i < tools.length; i++) {
//            var tool = tools[i];
//            log.info("工具 {}: 名称={}, 类型={}", i + 1, tool.getName(), tool.getClass().getSimpleName());
//            log.info("  描述: {}", tool.getDescription());
//            log.info("  类全名: {}", tool.getClass().getName());
//
//            // 检查是否是MCP工具
//            boolean isMcpTool = tool.getClass().getName().contains("mcp") ||
//                               tool.getClass().getName().contains("Mcp") ||
//                               tool.getName().toLowerCase().contains("amap") ||
//                               tool.getName().toLowerCase().contains("map") ||
//                               tool.getName().toLowerCase().contains("search_web") ||
//                               tool.getName().toLowerCase().contains("web_search");
//
//            if (isMcpTool) {
//                log.info("  *** 这是一个MCP工具! ***");
//            }
//        }
//
//        // 检查是否有MCP工具
//        boolean hasMcpTool = false;
//        for (var tool : tools) {
//            String className = tool.getClass().getName().toLowerCase();
//            String toolName = tool.getName().toLowerCase();
//
//            if (className.contains("mcp") ||
//                toolName.contains("amap") ||
//                toolName.contains("map") ||
//                toolName.contains("search_web") ||
//                toolName.contains("web_search")) {
//                hasMcpTool = true;
//                break;
//            }
//        }
//
//        log.info("是否包含MCP工具: {}", hasMcpTool);
//
//        if (hasMcpTool) {
//            log.info("✅ MCP工具集成成功！");
//        } else {
//            log.warn("⚠️ 没有发现MCP工具，可能集成失败");
//        }
//
//        Assertions.assertTrue(tools.length > 8, "工具总数应该超过8个（8个本地工具 + MCP工具）");
//    }
//
//    @Test
//    @DisplayName("测试高德地图MCP工具调用")
//    void testAmapMcpToolCall() {
//        String testMessage = "北京天安门附近有什么好的约会地点？请使用地图搜索功能";
//        String testChatId = "amap-test-" + System.currentTimeMillis();
//
//        log.info("=== 测试高德地图MCP工具调用 ===");
//        log.info("测试消息: {}", testMessage);
//        log.info("会话ID: {}", testChatId);
//
//        try {
//            String response = loveApp.doChatWithMcp(testMessage, testChatId);
//
//            log.info("=== 高德地图MCP调用结果 ===");
//            log.info("响应长度: {}", response.length());
//            log.info("响应内容: {}", response.substring(0, Math.min(300, response.length())));
//
//            Assertions.assertNotNull(response, "响应不应为空");
//            Assertions.assertFalse(response.trim().isEmpty(), "响应内容不应为空");
//
//            // 检查响应是否包含地点相关信息
//            boolean containsLocationInfo = response.toLowerCase().contains("北京") ||
//                                         response.toLowerCase().contains("天安门") ||
//                                         response.toLowerCase().contains("约会") ||
//                                         response.toLowerCase().contains("地点") ||
//                                         response.toLowerCase().contains("推荐");
//
//            log.info("响应是否包含地点信息: {}", containsLocationInfo);
//            Assertions.assertTrue(containsLocationInfo, "响应应该包含地点相关信息");
//
//        } catch (Exception e) {
//            log.error("高德地图MCP调用失败", e);
//            Assertions.fail("高德地图MCP调用失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    @DisplayName("对比MCP工具调用前后的差异")
//    void testMcpToolCallComparison() {
//        log.info("=== MCP工具调用对比测试 ===");
//
//        String[] testQueries = {
//            "上海外滩附近的浪漫餐厅推荐",
//            "广州塔周边适合情侣的地方",
//            "深圳有什么好玩的约会地点"
//        };
//
//        for (String query : testQueries) {
//            log.info("测试查询: {}", query);
//
//            try {
//                long startTime = System.currentTimeMillis();
//                String response = loveApp.doChatWithMcp(query, "comparison-test-" + System.currentTimeMillis());
//                long endTime = System.currentTimeMillis();
//
//                log.info("  响应时间: {}ms", endTime - startTime);
//                log.info("  响应长度: {}", response.length());
//                log.info("  响应摘要: {}", response.substring(0, Math.min(150, response.length())));
//
//                // 检查响应质量
//                boolean hasLocationInfo = response.toLowerCase().contains("餐厅") ||
//                                        response.toLowerCase().contains("地方") ||
//                                        response.toLowerCase().contains("地点") ||
//                                        response.toLowerCase().contains("推荐") ||
//                                        response.toLowerCase().contains("约会");
//
//                log.info("  响应质量: {}", hasLocationInfo ? "包含地点信息" : "无地点信息");
//
//                Assertions.assertTrue(hasLocationInfo, "响应应该包含地点相关信息");
//
//            } catch (Exception e) {
//                log.error("查询失败: {}", query, e);
//            }
//        }
//    }
//}

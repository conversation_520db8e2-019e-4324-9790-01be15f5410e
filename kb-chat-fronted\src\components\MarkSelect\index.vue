<template>
  <div style="padding: 0px">
    <el-table
      ref="table"
      :data="markIconsRef"
      :stripe="true"
      size="mini"
      height="400"
      highlight-current-row
      @current-change="currentChange"
      @row-dblclick="dblclick">
      <el-table-column
        label="句柄"
        align="left"
        key="id"
        prop="id">
        <template #default="{ row }">
          <img
            class="mark_img yellow_camp"
            :src="row.imgUrl" />
          <span style="margin-left: 10px">{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="名称"
        align="left"
        key="name"
        prop="name" />
    </el-table>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: right; margin: 5px; padding: 10px 0">
      <el-button
        type="primary"
        @click="handleSelect"
        >选择
      </el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </div>
</template>
<script setup>
  // const markFiles = require.context('@/assets/images/mark/white', false, /.png$/)
  // 批量引入图片
  const markFiles = import.meta.glob(`@/assets/images/mark/white/*.png`, {
    eager: true,
  });

  const markIcons = [];
  for (let i in markFiles) {
    markIcons.push({
      // imgUrl: require('@/assets/images/mark/white/' +
      // 	markFiles.keys()[i].substr(2)),
      imgUrl: i,
      id: i.slice(30, i.length - 4),
      name: i.substr(30),
    });
  }

  // for (let i = 0; i < markFiles.length; i++) {
  //   debugger
  //   markIcons.push({
  //     // imgUrl: require('@/assets/images/mark/white/' +
  // 		// 	markFiles.keys()[i].substr(2)),
  //     imgUrl: new URL('@/assets/images/mark/white/' +
  //    	markFiles.keys()[i].substr(2),import.meta.url).href,
  //     id: markFiles.keys()[i].substr(2, markFiles.keys()[i].indexOf('.png') - 2),
  //     name: markFiles.keys()[i].substr(2)
  //   })
  // }

  import { ref, defineEmits, getCurrentInstance, onMounted } from "vue";
  const { proxy } = getCurrentInstance();

  const markIconsRef = ref(markIcons);
  const currentRow = ref(undefined);
  const emit = defineEmits();

  function handleSelect() {
    emit("select", currentRow.value);
  }
  function dblclick(row) {
    currentRow.value = row;
    emit("select", currentRow.value);
  }
  function currentChange(row) {
    currentRow.value = row;
  }
  function close() {
    emit("close");
  }
  function reset() {
    proxy.$refs.table.setCurrentRow();
    currentRow.value = undefined;
  }
</script>
<style lang="scss" scoped>
  img.mark_img {
    width: 18px;
    height: 18px;
  }
  /**黄色图标 */
  img.mark_img.yellow_camp {
    filter: invert(76%) sepia(163%) saturate(1521%) hue-rotate(403deg)
      brightness(215%) contrast(105%);
    -webkit-filter: invert(76%) sepia(163%) saturate(1521%) hue-rotate(403deg)
      brightness(215%) contrast(105%);
  }
</style>

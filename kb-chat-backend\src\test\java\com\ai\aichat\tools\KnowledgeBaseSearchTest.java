package com.ai.aichat.tools;

import com.ai.aichat.model.vo.response.KnowledgeBaseVo;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class KnowledgeBaseSearchTest {

    @Resource
    private KnowledgeBaseSearch knowledgeBaseSearch;
    @Test
    public void testGetAllKnowledgeBases() {
        List<KnowledgeBaseVo> allKnowledgeBases = knowledgeBaseSearch.searchKnowledgeBaseSearch();
        assertNotNull(allKnowledgeBases);
    }
}
/**
 * 响应数据解析测试工具
 * 用于验证前端能否正确解析后端返回的BaseResponse格式数据
 */

// 模拟您提供的 /get_conversations 接口响应数据
const mockConversationsResponse = {
    "code": 0,
    "data": {
        "conversations": [
            {
                "session_id": "1753027576838-8itfil6q",
                "title": "你是谁",
                "chat_type": "chat",
                "kb_name": "",
                "create_time": "2025-07-20T16:06:21.000+00:00",
                "update_time": "2025-07-20T16:06:24.000+00:00",
                "message_count": 2
            },
            {
                "session_id": "1752930433372-qhte2w2b",
                "title": "西北大学是211吗？",
                "chat_type": "kb_chat",
                "kb_name": "高考招生信息",
                "create_time": "2025-07-19T13:07:26.000+00:00",
                "update_time": "2025-07-19T13:30:29.000+00:00",
                "message_count": 22
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 10,
            "total_records": 67,
            "total_conversations": 67,
            "total_pages": 7,
            "has_more": true
        }
    },
    "message": "ok"
};

/**
 * 测试响应数据解析
 */
export function testResponseParsing() {
    console.log('=== 响应数据解析测试 ===');
    
    // 测试成功响应解析
    console.log('1. 测试成功响应解析:');
    if (mockConversationsResponse.code === 0) {
        console.log('✅ 成功状态码检查通过 (code === 0)');
        
        const { conversations, pagination } = mockConversationsResponse.data;
        console.log('✅ 数据解构成功');
        console.log(`   - 会话数量: ${conversations.length}`);
        console.log(`   - 分页信息: 第${pagination.page}页，共${pagination.total_pages}页`);
        console.log(`   - 总记录数: ${pagination.total_records}`);
        
        // 测试会话数据结构
        const firstConversation = conversations[0];
        console.log('✅ 会话数据结构验证:');
        console.log(`   - 会话ID: ${firstConversation.session_id}`);
        console.log(`   - 标题: ${firstConversation.title}`);
        console.log(`   - 类型: ${firstConversation.chat_type}`);
        console.log(`   - 知识库: ${firstConversation.kb_name || '无'}`);
        console.log(`   - 消息数: ${firstConversation.message_count}`);
        
    } else {
        console.log('❌ 成功状态码检查失败');
    }
    
    // 测试错误响应解析
    console.log('\n2. 测试错误响应解析:');
    const mockErrorResponse = {
        "code": 40000,
        "data": null,
        "message": "参数错误"
    };
    
    if (mockErrorResponse.code !== 0) {
        console.log('✅ 错误状态码检查通过 (code !== 0)');
        console.log(`   - 错误码: ${mockErrorResponse.code}`);
        console.log(`   - 错误信息: ${mockErrorResponse.message}`);
    } else {
        console.log('❌ 错误状态码检查失败');
    }
    
    console.log('\n=== 测试完成 ===');
}

/**
 * 模拟前端request拦截器处理逻辑
 */
export function simulateRequestInterceptor(response) {
    console.log('\n=== 模拟request拦截器处理 ===');
    
    const code = response.code || 200;
    const msg = response.message || response.msg || '默认消息';
    
    console.log(`响应码: ${code}`);
    console.log(`响应消息: ${msg}`);
    
    if (code === 0) {
        console.log('✅ BaseResponse 成功状态码 0 (统一后的成功状态)');
        return Promise.resolve(response);
    } else if (code === 1) {
        console.log('✅ StreamResponseVo 成功状态码 1 (兼容旧的流式响应)');
        return Promise.resolve(response);
    } else if (code !== 200) {
        console.log('❌ 其他错误状态码');
        console.log(`错误信息: ${msg}`);
        return Promise.reject(new Error(msg));
    } else {
        console.log('✅ 传统的 200 成功状态码 (兼容)');
        return Promise.resolve(response);
    }
}

/**
 * 测试ChatHistory组件的数据处理逻辑
 */
export function testChatHistoryDataProcessing() {
    console.log('\n=== ChatHistory组件数据处理测试 ===');
    
    const response = mockConversationsResponse;
    
    if (response.code === 0) {
        console.log('✅ 响应码检查通过');
        
        const { conversations, pagination } = response.data;
        
        // 模拟组件中的数据处理逻辑
        const processedConversations = conversations.map(item => ({
            ...item,
            type: item.chat_type === 'kb_chat' ? 'kb' : 'normal'
        }));
        
        console.log('✅ 会话数据处理完成:');
        processedConversations.forEach((conv, index) => {
            console.log(`   ${index + 1}. ${conv.title} (${conv.type})`);
        });
        
        // 模拟分页信息更新
        console.log('✅ 分页信息更新:');
        console.log(`   - 当前页: ${pagination.page}`);
        console.log(`   - 总页数: ${pagination.total_pages}`);
        console.log(`   - 总会话数: ${pagination.total_conversations}`);
        console.log(`   - 是否有更多数据: ${pagination.page < pagination.total_pages}`);
        
    } else {
        console.log('❌ 响应码检查失败');
        console.log(`期望: 0, 实际: ${response.code}`);
    }
}

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
    console.log('🚀 开始响应数据解析测试...');
    testResponseParsing();
    simulateRequestInterceptor(mockConversationsResponse);
    testChatHistoryDataProcessing();
}

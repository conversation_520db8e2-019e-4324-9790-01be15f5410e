package com.ai.aichat.model.vo.chat;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 流式响应VO，用于SSE（Server-Sent Events）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "流式响应")
public class StreamResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码：1-成功，0-失败
     */
    @Schema(description = "响应状态码", example = "1", allowableValues = {"0", "1"})
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "success")
    private String msg;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private Object data;

    /**
     * 创建成功响应（使用ChatResponseVo）
     */
    public static StreamResponseVo success(ChatResponseVo data) {
        return StreamResponseVo.builder()
                .code(1)
                .msg("success")
                .data(data)
                .build();
    }

    /**
     * 创建成功响应（使用SimpleChatResponseVo）
     */
    public static StreamResponseVo success(SimpleChatResponseVo data) {
        return StreamResponseVo.builder()
                .code(1)
                .msg("success")
                .data(data)
                .build();
    }

    /**
     * 创建成功响应（仅内容）- 简化版本
     */
    public static StreamResponseVo success(String content, String sessionId) {
        SimpleChatResponseVo responseData = new SimpleChatResponseVo();
        responseData.setContent(content);
        responseData.setSession_id(sessionId);

        return StreamResponseVo.builder()
                .code(1)
                .msg("success")
                .data(responseData)
                .build();
    }

    /**
     * 创建成功响应（完整版本，包含所有字段）
     */
    public static StreamResponseVo successFull(String content, String sessionId) {
        ChatResponseVo responseData = new ChatResponseVo();
        responseData.setContent(content);
        responseData.setSession_id(sessionId);

        return StreamResponseVo.builder()
                .code(1)
                .msg("success")
                .data(responseData)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static StreamResponseVo error(String message) {
        return StreamResponseVo.builder()
                .code(0)
                .msg(message)
                .data(null)
                .build();
    }

    /**
     * 创建结束标记响应
     */
    public static StreamResponseVo done() {
        return StreamResponseVo.builder()
                .code(1)
                .msg("[DONE]")
                .data(null)
                .build();
    }
}

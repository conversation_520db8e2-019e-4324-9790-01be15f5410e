package com.ai.aichat.service;

import com.ai.aichat.model.dto.user.UserQueryRequest;
import com.ai.aichat.model.entity.User;
import com.ai.aichat.model.vo.LoginUserVO;
import com.ai.aichat.model.vo.UserVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【user(用户)】的数据库操作Service
 * @createDate 2025-04-01 22:10:32
 */
public interface UserService extends IService<User> {

    /**
     * 用户注册
     *
     * @param userAccount   用户账户
     * @param userPassword  用户密码
     * @param checkPassword 校验密码
     * @return 新用户 id
     */
    long userRegister(String userAccount, String userPassword, String checkPassword);

    /**
     * 用户登录
     *
     * @param userAccount  用户账户
     * @param userPassword 用户密码
     * @param request      客户端的HTTP请求
     * @return 脱敏后的用户信息
     */
    LoginUserVO userLogin(String userAccount, String userPassword, HttpServletRequest request);

    /**
     * 获取当前登录用户
     *
     * @param request 客户端的HTTP请求
     * @return 当前登录用户
     */
    User getLoginUser(HttpServletRequest request);

    /**
     * 获取加密后的密码
     *
     * @param userPassword 用户密码
     * @return 加密后的密码
     */
    String getEncryptPassword(String userPassword);

    /**
     * 获得脱敏后的用户登录信息
     *
     * @param user 用户
     * @return 脱敏后的用户信息
     */
    LoginUserVO getLoginUserVO(User user);

    /**
     * 用户注销
     *
     * @param request 客户端的HTTP请求
     * @return 注销信息
     */
    boolean userLogout(HttpServletRequest request);

    /**
     * 用户列表
     *
     * @param userList 用户列表
     * @return 用户列表
     */
    List<UserVO> getUserVOList(List<User> userList);


    /**
     * 根据 id 获取包装类
     *
     * @param user 用户
     * @return 用户vo类
     */
    UserVO getUserVO(User user);

    /**
     * 是否为管理员
     *
     * @param user
     * @return
     */
    boolean isAdmin(User user);
}


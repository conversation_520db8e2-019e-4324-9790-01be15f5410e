<template>
  <div class="kb-selector">
    <!-- 紧凑状态 -->
    <div v-if="isCollapsed" class="compact-mode">
      <div class="current-mode-display" @click="isCollapsed = false">
        <div class="mode-indicator">
          <el-icon v-if="!selectedKB"><ChatDotRound /></el-icon>
          <el-icon v-else><Files /></el-icon>
          <span class="mode-text">{{ !selectedKB ? '直接对话' : '知识库问答' }}</span>
        </div>
        
        <div v-if="selectedKB" class="kb-info">
          <span class="divider">|</span>
          <el-icon><Collection /></el-icon>
          <el-tooltip 
            :content="selectedKB"
            placement="top"
            :show-after="500"
            v-if="selectedKB.length > 20">
            <span class="kb-name">{{ truncateKbName(selectedKB, 20) }}</span>
          </el-tooltip>
          <span v-else class="kb-name">{{ selectedKB }}</span>
        </div>
        
        <div class="edit-button">
          <el-icon><Edit /></el-icon>
        </div>
      </div>
    </div>
    
    <!-- 展开状态 -->
    <div v-else class="expanded-mode">
      <div class="header-row">
        <h3 class="section-title">对话模式</h3>
        <div class="collapse-btn" @click="isCollapsed = true">
          <el-icon><ArrowUp /></el-icon>
          <span>收起</span>
        </div>
      </div>
      
      <div class="mode-toggle">
        <div 
          class="mode-btn" 
          :class="{ 'active-mode': !selectedKB }"
          @click="switchToAIMode">
          <el-icon><ChatDotRound /></el-icon>
          <span>直接对话</span>
        </div>
        <div 
          class="mode-btn" 
          :class="{ 'active-mode': selectedKB }"
          @click="showKbSelector = true">
          <el-icon><Files /></el-icon>
          <span>知识库问答</span>
        </div>
      </div>
      
      <transition name="fade">
        <div v-if="showKbSelector || selectedKB" class="kb-select-wrapper">
          <el-select
            v-model="selectedKB"
            placeholder="请选择知识库"
            @change="handleKBChange"
            class="kb-select"
            popper-class="kb-select-dropdown">
            <template #prefix>
              <el-icon><Collection /></el-icon>
            </template>
            <el-option
              v-for="item in kbList"
              :key="item.kb_name"
              :label="item.kb_name"
              :value="item.kb_name">
              <div class="kb-option">
                <span class="kb-name" :title="item.kb_name">{{ item.kb_name }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useDialogStore } from "/src/store/dialog.js";
import { storeToRefs } from "pinia";
import { ChatDotRound, Files, Collection, Check, Edit, ArrowUp } from '@element-plus/icons-vue'

const dialogStore = useDialogStore();
const { selectedkb } = storeToRefs(dialogStore);
const selectedKB = selectedkb; // 使用store中的状态
const showKbSelector = ref(false);
const isCollapsed = ref(true); // 默认为紧凑模式
const kbList = ref([]);
const isLoadingKB = ref(false);

// 截断长知识库名称并添加省略号
const truncateKbName = (name, maxLength) => {
  if (!name) return '';
  if (name.length <= maxLength) return name;
  return name.substring(0, maxLength) + '...';
};

const loadKnowledgeBases = async () => {
  try {
    const response = await fetch('/dev-api/list_knowledge_bases');
    const result = await response.json();
    
    if (result.code !== 1) {
      throw new Error(result.msg || '获取知识库列表失败');
    }

    kbList.value = result.data.map(kb => ({
      kb_name: kb
    }));
    
  } catch (error) {
    ElMessage.error(error.message);
    kbList.value = [];
  }
};

const emit = defineEmits(['update:selectedKB']);

const handleKBChange = async (kbName) => {
  if (!kbName) {
    selectedKB.value = '';
    emit('update:selectedKB', '');
    return;
  }
  
  try {
    // 设置加载状态为true
    isLoadingKB.value = true;
    
    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('加载知识库超时')), 15000);
    });
    
    // 使用竞争模式，任何一个Promise完成都会结束
    const response = await Promise.race([
      fetch('/dev-api/load_knowledge_base', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ kb_name: kbName })
      }),
      timeoutPromise
    ]);

    if (!response.ok) {
      throw new Error(`服务器错误: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.code !== 1) {
      throw new Error(data.msg || '加载知识库失败');
    }

    ElMessage.success(`已切换到知识库: ${kbName}`);
    emit('update:selectedKB', kbName);
    isCollapsed.value = true; // 选择后自动收起
    
  } catch (error) {
    console.error('知识库加载失败:', error);
    ElMessage.error(error.message || '加载知识库失败，请重试');
    selectedKB.value = '';
    emit('update:selectedKB', '');
  } finally {
    // 无论成功还是失败，都将加载状态设置为false
    isLoadingKB.value = false;
  }
};

const switchToAIMode = () => {
  selectedKB.value = '';
  emit('update:selectedKB', '');
  showKbSelector.value = false;
  ElMessage.success('已切换到直接对话模式');
  isCollapsed.value = true; // 选择后自动收起
};

// 当打开知识库选择时，自动展开界面
watch(showKbSelector, (newVal) => {
  if (newVal) {
    isCollapsed.value = false;
  }
});

onMounted(() => {
  loadKnowledgeBases();
  selectedKB.value = '';
  // 如果已选择了知识库，显示选择器
  if (selectedKB.value) {
    showKbSelector.value = true;
  }
});
</script>

<style scoped>
.kb-selector {
  width: 100%;
}

/* 紧凑模式样式 */
.compact-mode {
  width: 100%;
}

.current-mode-display {
  height: 40px;
  background-color: rgba(9, 39, 98, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.current-mode-display:hover {
  background-color: rgba(15, 45, 90, 0.7);
  border-color: #5d82c9;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.mode-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
}

.mode-indicator .el-icon {
  font-size: 16px;
  color: #13fff3;
}

.mode-text {
  font-weight: 500;
  font-size: 14px;
}

.kb-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.divider {
  color: #3f63a8;
  margin: 0 4px;
}

.kb-info .el-icon {
  font-size: 16px;
  color: #13fff3;
}

.kb-name {
  color: #13fff3;
  font-weight: 500;
  font-size: 14px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.edit-button {
  position: absolute;
  right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: rgba(15, 45, 90, 0.5);
  transition: all 0.2s;
}

.edit-button:hover {
  background-color: rgba(19, 255, 243, 0.15);
}

.edit-button .el-icon {
  font-size: 14px;
  color: #fff;
}

/* 展开模式样式 */
.expanded-mode {
  width: 100%;
  background-color: rgba(9, 39, 98, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 10px;
  padding: 15px;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  margin: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-left: 3px solid #13fff3;
  padding-left: 10px;
}

.collapse-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.2s;
}

.collapse-btn:hover {
  background-color: rgba(15, 45, 90, 0.7);
}

.mode-toggle {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.mode-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 42px;
  background: linear-gradient(to bottom, #0c2d70, #001e57);
  border: 1px solid #3f63a8;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.mode-btn:hover {
  background: linear-gradient(to bottom, #163879, #0e2c64);
  border-color: #5d82c9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.mode-btn.active-mode {
  background: linear-gradient(135deg, #13fff3, #19aeff);
  border-color: transparent;
  color: #001e57;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(19, 255, 243, 0.5);
}

.kb-select-wrapper {
  margin-top: 5px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kb-select {
  width: 100%;
}

.kb-option {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>

<style>
/* 自定义知识库下拉菜单样式 */
.kb-select-dropdown {
  background: linear-gradient(to bottom, #0f2a63, #091d45) !important;
  border: 1px solid #3f63a8 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
  margin-top: 5px !important;
  max-width: 300px !important;
}

.kb-select-dropdown .el-scrollbar__view {
  padding: 5px 0 !important;
}

.kb-select-dropdown .el-select-dropdown__item {
  color: #d0d9f0 !important;
  height: auto !important;
  min-height: 36px !important;
  padding: 8px 15px !important;
  line-height: 1.5 !important;
  white-space: normal !important; /* 允许文本换行 */
  word-break: break-word !important; /* 在需要时断词 */
}

.kb-select-dropdown .el-select-dropdown__item.hover,
.kb-select-dropdown .el-select-dropdown__item:hover {
  background-color: rgba(19, 255, 243, 0.1) !important;
  color: #13fff3 !important;
}

.kb-select-dropdown .el-select-dropdown__item.selected {
  background-color: rgba(19, 255, 243, 0.2) !important;
  color: #13fff3 !important;
  font-weight: bold !important;
}

.kb-select-dropdown .el-select-dropdown__item.selected::after {
  content: '✓';
  margin-left: 5px;
  font-weight: bold;
}

.kb-select-dropdown .el-popper__arrow::before {
  background: #0f2a63 !important;
  border-color: #3f63a8 !important;
}
</style>

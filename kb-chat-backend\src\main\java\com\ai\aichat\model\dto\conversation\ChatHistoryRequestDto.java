package com.ai.aichat.model.dto.conversation;

import com.ai.aichat.common.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天历史查询请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "聊天历史查询请求")
public class ChatHistoryRequestDto extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "chat_123456")
    private String sessionId;

    /**
     * 聊天类型
     */
    @Schema(description = "聊天类型", example = "chat")
    private String chatType;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", example = "default")
    private String kbName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 搜索关键词（在消息内容中搜索）
     */
    @Schema(description = "搜索关键词", example = "AI")
    private String keyword;

    /**
     * 消息角色过滤：user/assistant
     */
    @Schema(description = "消息角色过滤", allowableValues = {"user", "assistant"})
    private String role;

    /**
     * 是否包含消息详情
     */
    @Schema(description = "是否包含消息详情", example = "true")
    private Boolean includeMessages = true;

    /**
     * 最大消息数量限制（每个会话）
     */
    @Schema(description = "最大消息数量限制", example = "50")
    private Integer maxMessages = 50;
}

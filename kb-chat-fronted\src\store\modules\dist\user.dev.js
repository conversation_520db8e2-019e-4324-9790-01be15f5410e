"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _login2 = require("@/api/login");

var _auth = require("@/utils/auth");

var _profile_tk = _interopRequireDefault(require("@/assets/images/profile_tk.jpg"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

var useUserStore = defineStore('user', {
  state: function state() {
    return {
      token: (0, _auth.getToken)(),
      name: '',
      avatar: '',
      roles: [],
      permissions: []
    };
  },
  actions: {
    // 登录
    login: function login(userInfo) {
      var _this = this;

      var username = userInfo.username.trim();
      var password = userInfo.password;
      var code = userInfo.code;
      var uuid = userInfo.uuid;
      return new Promise(function (resolve, reject) {
        (0, _login2.login)(username, password, code, uuid).then(function (res) {
          (0, _auth.setToken)(res.token);
          _this.token = res.token;
          resolve();
        })["catch"](function (error) {
          reject(error);
        });
      });
    },
    // 获取用户信息
    getInfo: function getInfo() {
      var _this2 = this;

      return new Promise(function (resolve, reject) {
        (0, _login2.getInfo)().then(function (res) {
          var user = res.user;
          var avatar = user.avatar == "" || user.avatar == null ? _profile_tk["default"] : user.avatar;

          if (res.roles && res.roles.length > 0) {
            // 验证返回的roles是否是一个非空数组
            _this2.roles = res.roles;
            _this2.permissions = res.permissions;
          } else {
            _this2.roles = ['ROLE_DEFAULT'];
          }

          _this2.name = user.userName;
          _this2.avatar = avatar;
          resolve(res);
        })["catch"](function (error) {
          reject(error);
        });
      });
    },
    // 退出系统
    logOut: function logOut() {
      var _this3 = this;

      return new Promise(function (resolve, reject) {
        (0, _login2.logout)(_this3.token).then(function () {
          _this3.token = '';
          _this3.roles = [];
          _this3.permissions = [];
          (0, _auth.removeToken)();
          resolve();
        })["catch"](function (error) {
          reject(error);
        });
      });
    }
  }
});
var _default = useUserStore;
exports["default"] = _default;
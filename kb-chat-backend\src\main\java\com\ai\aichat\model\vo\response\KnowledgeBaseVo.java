package com.ai.aichat.model.vo.response;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 知识库信息VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "知识库信息")
public class KnowledgeBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "1")
    private Long id;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", example = "default")
    private String name;

    /**
     * 知识库描述
     */
    @Schema(description = "知识库描述", example = "默认知识库")
    private String description;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-06-08 10:30:00")
    private String created_at;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-06-08 15:45:00")
    private String updated_at;

    /**
     * 文件数量
     */
    @Schema(description = "文件数量", example = "10")
    private Integer file_count;

    /**
     * 向量数量
     */
    @Schema(description = "向量数量", example = "1000")
    private Integer vector_count;

    /**
     * 知识库状态
     */
    @Schema(description = "知识库状态", example = "active")
    private String status;
}

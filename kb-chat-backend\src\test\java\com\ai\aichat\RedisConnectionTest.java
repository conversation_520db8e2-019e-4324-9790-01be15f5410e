package com.ai.aichat;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class RedisConnectionTest {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testRedisConnection() {
        // 写入
        stringRedisTemplate.opsForValue().set("test-key", "hello-redis");
        // 读取
        String value = stringRedisTemplate.opsForValue().get("test-key");
        // 断言
        assertEquals("hello-redis", value);
    }
} 
package com.ai.aichat.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

@Configuration
public class WebConfiguration {

    @Bean
    public WebClient webClient() {
        // 创建 HttpClient（默认使用 HTTP/1.1）
        HttpClient httpClient = HttpClient.create();

        // 构建 WebClient
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl("http://localhost:8000")  // 指向 vLLM 服务
                .build();
    }
}

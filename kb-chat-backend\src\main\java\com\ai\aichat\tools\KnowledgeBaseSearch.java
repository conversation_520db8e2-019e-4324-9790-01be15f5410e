package com.ai.aichat.tools;

import com.ai.aichat.model.vo.response.KnowledgeBaseVo;
import com.ai.aichat.service.KnowledgeBaseService;
import jakarta.annotation.Resource;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class KnowledgeBaseSearch {

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @Tool(description = "获取所有知识库的详细信息,知识库详细信息列表")
    public List<KnowledgeBaseVo> searchKnowledgeBaseSearch() {
        List<KnowledgeBaseVo> allKnowledgeBases = knowledgeBaseService.getAllKnowledgeBases();
        return allKnowledgeBases;
    }
}

<template>
  <router-view />
</template>

<script setup>
  import useSettingsStore from "@/store/modules/settings";
  import { handleThemeStyle } from "@/utils/theme";

  onMounted(() => {
    nextTick(() => {
      // 初始化主题样式
      handleThemeStyle(useSettingsStore().theme);
    });
  });
</script>

<style>
  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url("/assets/images/button/主页按钮-正常.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }
</style>

package com.ai.aichat.model.dto.knowledge;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 知识库DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "知识库信息")
public class KnowledgeBaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", required = true, example = "default")
    private String name;

    /**
     * 知识库描述
     */
    @Schema(description = "知识库描述", example = "默认知识库")
    private String description;

    /**
     * 知识库路径
     */
    @Schema(description = "知识库路径", example = "/data/kb/default")
    private String path;

    /**
     * 文档数量
     */
    @Schema(description = "文档数量", example = "10")
    private Integer documentCount;

    /**
     * 向量数量
     */
    @Schema(description = "向量数量", example = "1000")
    private Integer vectorCount;

    /**
     * 问答对数量
     */
    @Schema(description = "问答对数量", example = "50")
    private Integer qaPairCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date updateTime;

    /**
     * 知识库状态：active/inactive
     */
    @Schema(description = "知识库状态", example = "active", allowableValues = {"active", "inactive"})
    private String status = "active";

    /**
     * 支持的文件类型
     */
    @Schema(description = "支持的文件类型")
    private List<String> supportedFileTypes;

    /**
     * 知识库配置信息
     */
    @Schema(description = "知识库配置信息")
    private KbConfigDto config;

    /**
     * 知识库配置DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "知识库配置")
    public static class KbConfigDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 向量维度
         */
        @Schema(description = "向量维度", example = "768")
        private Integer vectorDimension;

        /**
         * 分块大小
         */
        @Schema(description = "分块大小", example = "500")
        private Integer chunkSize;

        /**
         * 分块重叠
         */
        @Schema(description = "分块重叠", example = "50")
        private Integer chunkOverlap;

        /**
         * 嵌入模型名称
         */
        @Schema(description = "嵌入模型名称", example = "text-embedding-ada-002")
        private String embeddingModel;

        /**
         * 是否启用问答对
         */
        @Schema(description = "是否启用问答对", example = "true")
        private Boolean enableQaPairs = true;

        /**
         * 是否启用知识图谱
         */
        @Schema(description = "是否启用知识图谱", example = "false")
        private Boolean enableGraph = false;
    }
}

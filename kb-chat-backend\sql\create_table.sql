-- AI聊天系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_chat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ai_chat;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_account` VARCHAR(256) NOT NULL COMMENT '账号',
    `user_password` VARCHAR(512) NOT NULL COMMENT '密码',
    `union_id` VARCHAR(256) NULL COMMENT '微信开放平台id',
    `mp_open_id` VARCHAR(256) NULL COMMENT '公众号openId',
    `user_name` VARCHAR(256) NULL COMMENT '用户昵称',
    `user_avatar` VARCHAR(1024) NULL COMMENT '用户头像',
    `user_profile` VARCHAR(512) NULL COMMENT '用户简介',
    `user_role` VARCHAR(256) DEFAULT 'user' NOT NULL COMMENT '用户角色：user/admin/ban',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` TINYINT DEFAULT 0 NOT NULL COMMENT '软删除标记 0-正常 1-删除',
    INDEX `idx_account_delete` (`user_account`, `is_delete`),
    INDEX `idx_userName` (`user_name`),
    INDEX `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 会话表
CREATE TABLE IF NOT EXISTS `conversation` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `session_id` VARCHAR(255) NOT NULL COMMENT '会话ID',
    `title` VARCHAR(512) DEFAULT '' COMMENT '会话标题',
    `chat_type` VARCHAR(50) DEFAULT 'chat' COMMENT '聊天类型',
    `kb_name` VARCHAR(100) DEFAULT '' COMMENT '知识库名称',
    `message_count` INT DEFAULT 0 COMMENT '消息数量',
    `user_id` BIGINT DEFAULT 1 COMMENT '用户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` TINYINT DEFAULT 0 NOT NULL COMMENT '软删除标记 0-正常 1-删除',
    INDEX `idx_session_delete` (`session_id`, `is_delete`),
    INDEX `idx_user_update` (`user_id`, `update_time`) COMMENT '用户维度时间排序查询',
    INDEX `idx_chat_type` (`chat_type`),
    INDEX `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';

-- 知识库表
CREATE TABLE IF NOT EXISTS `knowledge_base` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '知识库名称',
    `description` TEXT COMMENT '知识库描述',
    `user_id` BIGINT DEFAULT 1 COMMENT '用户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` TINYINT DEFAULT 0 NOT NULL COMMENT '软删除标记 0-正常 1-删除',
    INDEX `idx_name_delete` (`name`, `is_delete`),
    INDEX `idx_user_kb` (`user_id`, `update_time`) COMMENT '用户知识库管理查询',
    INDEX `idx_name` (`name`),
    INDEX `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `chat_message` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `conversation_id` BIGINT NOT NULL COMMENT '会话ID',
    `role` VARCHAR(20) NOT NULL COMMENT '角色：user/assistant',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `sources` TEXT COMMENT '来源信息',
    `graph_data` TEXT COMMENT '图表数据',
    `qa_pair_data` TEXT COMMENT 'QA对数据',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` TINYINT DEFAULT 0 NOT NULL COMMENT '软删除标记 0-正常 1-删除',
    INDEX `idx_conversation_time` (`conversation_id`, `create_time`) COMMENT '会话消息时间排序',
    INDEX `idx_is_delete` (`is_delete`),
    CONSTRAINT 'fk_cm_c_id' FOREIGN KEY (`conversation_id`) REFERENCES `conversation`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 知识库文件表
CREATE TABLE IF NOT EXISTS `knowledge_base_file` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
    `kb_id` BIGINT NOT NULL COMMENT '知识库ID（外键）',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名（唯一标识）',
    `original_name` VARCHAR(255) NOT NULL COMMENT '文件原始名称',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
    `file_type` VARCHAR(20) COMMENT '文件类型/扩展名',
    `file_md5` VARCHAR(32) COMMENT '文件MD5值',
    `process_status` VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态：pending/processing/completed/failed',
    `process_progress` INT DEFAULT 0 COMMENT '处理进度（0-100）',
    `error_message` TEXT COMMENT '错误信息',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `user_id` BIGINT DEFAULT 1 COMMENT '用户ID',
    `is_delete` TINYINT DEFAULT 0 COMMENT '软删除标记 0-正常 1-删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_kb_file` (`kb_id`, `file_name`, `is_delete`),
    KEY `idx_kb_id` (`kb_id`),
    KEY `idx_process_status` (`process_status`),
    KEY `idx_file_md5` (`file_md5`),
    KEY `idx_is_delete` (`is_delete`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_kb_file_kb_id` FOREIGN KEY (`kb_id`) REFERENCES `knowledge_base`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库文件表';


-- 问答库表
CREATE TABLE IF NOT EXISTS `qa_base` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '问答库名称',
    `description` TEXT COMMENT '问答库描述',
    `user_id` BIGINT DEFAULT 1 COMMENT '用户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` TINYINT DEFAULT 0 NOT NULL COMMENT '软删除标记 0-正常 1-删除',
    INDEX `idx_name_delete` (`name`, `is_delete`),
    INDEX `idx_user_qa` (`user_id`, `update_time`) COMMENT '用户问答库管理查询',
    INDEX `idx_name` (`name`),
    INDEX `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答库表';

-- 问答对文件表
CREATE TABLE IF NOT EXISTS `qa_file` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `qa_base_id` BIGINT NOT NULL COMMENT '问答库ID（外键）',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名（唯一标识）',
    `original_name` VARCHAR(255) NOT NULL COMMENT '文件原始名称',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
    `qa_pair_count` INT DEFAULT 0 COMMENT '问答对数量',
    `description` TEXT COMMENT '文件描述',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `user_id` BIGINT DEFAULT 1 COMMENT '用户ID',
    `is_delete` TINYINT DEFAULT 0 NOT NULL COMMENT '软删除标记 0-正常 1-删除',
    UNIQUE KEY `uk_qa_base_file` (`qa_base_id`, `file_name`, `is_delete`),
    INDEX `idx_qa_base_id` (`qa_base_id`),
    INDEX `idx_file_name` (`file_name`),
    INDEX `idx_is_delete` (`is_delete`),
    INDEX `idx_create_time` (`create_time`),
    CONSTRAINT `fk_qa_file_qa_base_id` FOREIGN KEY (`qa_base_id`) REFERENCES `qa_base`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答对文件表';

-- 模型结构表
CREATE TABLE IF NOT EXISTS `model_structure` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `version` VARCHAR(50) NOT NULL COMMENT '模型版本',
    `base_model` VARCHAR(100) NOT NULL COMMENT '基础模型',
    `dataset` VARCHAR(100) COMMENT '数据集',
    `description` TEXT COMMENT '模型描述',
    `model_path` VARCHAR(500) COMMENT '模型路径',
    `training_status` TINYINT DEFAULT 0 COMMENT '训练状态：0-未训练，1-训练中，2-训练完成，3-训练失败',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    INDEX `idx_version` (`version`),
    INDEX `idx_base_model` (`base_model`),
    INDEX `idx_training_status` (`training_status`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型结构表';

-- 训练数据集表
CREATE TABLE IF NOT EXISTS `training_dataset` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '数据集名称',
    `description` TEXT COMMENT '数据集描述',
    `file_path` VARCHAR(500) COMMENT '数据集文件路径',
    `file_size` BIGINT DEFAULT 0 COMMENT '数据集大小（字节）',
    `data_count` INT DEFAULT 0 COMMENT '数据条数',
    `status` TINYINT DEFAULT 0 COMMENT '数据集状态：0-未处理，1-处理中，2-处理完成，3-处理失败',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    UNIQUE KEY `uk_name_deleted` (`name`, `is_deleted`),
    INDEX `idx_name` (`name`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练数据集表';

-- 训练任务表
CREATE TABLE IF NOT EXISTS `training_task` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `model_id` BIGINT NOT NULL COMMENT '模型ID',
    `dataset_name` VARCHAR(100) NOT NULL COMMENT '数据集名称',
    `epochs` INT NOT NULL COMMENT '训练轮次',
    `learning_rate` DECIMAL(10,6) NOT NULL COMMENT '学习率',
    `status` TINYINT DEFAULT 0 COMMENT '训练状态：0-待开始，1-训练中，2-训练完成，3-训练失败，4-已取消',
    `current_epoch` INT DEFAULT 0 COMMENT '当前轮次',
    `loss` DECIMAL(10,6) COMMENT '训练损失',
    `accuracy` DECIMAL(10,6) COMMENT '训练精度',
    `progress` DECIMAL(5,2) DEFAULT 0.00 COMMENT '训练进度（百分比）',
    `training_log` LONGTEXT COMMENT '训练日志',
    `error_message` TEXT COMMENT '错误信息',
    `start_time` DATETIME COMMENT '开始时间',
    `end_time` DATETIME COMMENT '结束时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    INDEX `idx_model_id` (`model_id`),
    INDEX `idx_dataset_name` (`dataset_name`),
    INDEX `idx_status` (`status`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_is_deleted` (`is_deleted`),
    CONSTRAINT `fk_training_task_model_id` FOREIGN KEY (`model_id`) REFERENCES `model_structure`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练任务表';

-- 插入初始模型结构数据
INSERT INTO `model_structure` (`version`, `base_model`, `dataset`, `description`, `model_path`, `training_status`) VALUES
('v1.0', 'ChatGLM2-6B', 'default', 'ChatGLM2-6B基础模型', '/models/chatglm2-6b', 0),
('v1.1', 'ChatGLM2-6B', 'knowledge_qa', 'ChatGLM2-6B知识问答微调版本', '/models/chatglm2-6b-ft-v1.1', 0),
('v2.0', 'Qwen-7B', 'default', 'Qwen-7B基础模型', '/models/qwen-7b', 0),
('v2.1', 'Qwen-7B', 'conversation', 'Qwen-7B对话微调版本', '/models/qwen-7b-ft-v2.1', 0),
('v3.0', 'Baichuan2-7B', 'default', 'Baichuan2-7B基础模型', '/models/baichuan2-7b', 0);

-- 插入初始训练数据集数据
INSERT INTO `training_dataset` (`name`, `description`, `file_path`, `file_size`, `data_count`, `status`) VALUES
('knowledge_qa', '知识问答数据集', '/datasets/knowledge_qa.jsonl', 1024000, 5000, 2),
('conversation', '对话数据集', '/datasets/conversation.jsonl', 2048000, 8000, 2),
('instruction_following', '指令跟随数据集', '/datasets/instruction_following.jsonl', 1536000, 6000, 2),
('code_generation', '代码生成数据集', '/datasets/code_generation.jsonl', 3072000, 10000, 2),
('medical_qa', '医疗问答数据集', '/datasets/medical_qa.jsonl', 2560000, 7500, 2);

-- 显示表结构
SHOW TABLES;

-- 显示各表的索引信息
SELECT
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'ai_chat'
  AND INDEX_NAME NOT IN ('PRIMARY')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 添加外键约束
ALTER TABLE chat_message
    ADD CONSTRAINT fk_cm_c_id
        FOREIGN KEY (conversation_id) REFERENCES conversation(id)
            ON DELETE CASCADE;

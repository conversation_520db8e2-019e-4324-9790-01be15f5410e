package com.ai.aichat.service;

import com.ai.aichat.model.vo.response.ConversationListVo;
import com.ai.aichat.model.vo.response.ChatHistoryListVo;

/**
 * 会话历史服务接口
 */
public interface ConversationHistoryService {

    /**
     * 获取会话列表
     * @param page 页码
     * @param pageSize 每页数量
     * @param chatType 聊天类型
     * @param dateRange 日期范围
     * @return 会话列表
     */
    ConversationListVo getConversations(Integer page, Integer pageSize, String chatType, String dateRange);

    /**
     * 获取指定会话的聊天历史
     * @param sessionId 会话ID
     * @return 聊天历史
     */
    ChatHistoryListVo getChatHistory(String sessionId);

    /**
     * 删除指定会话
     * @param sessionId 会话ID
     * @return 是否删除成功
     */
    Boolean deleteConversation(String sessionId);

    /**
     * 更新会话标题
     * @param sessionId 会话ID
     * @param title 新标题
     * @return 是否更新成功
     */
    Boolean updateConversationTitle(String sessionId, String title);
}

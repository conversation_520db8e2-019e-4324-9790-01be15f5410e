package com.ai.aichat.model.dto.conversation;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 会话DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会话信息")
public class ConversationDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "1")
    private Long id;

    /**
     * 会话唯一标识
     */
    @Schema(description = "会话唯一标识", example = "chat_123456")
    private String sessionId;

    /**
     * 会话标题
     */
    @Schema(description = "会话标题", example = "关于AI的讨论")
    private String title;

    /**
     * 会话类型
     */
    @Schema(description = "会话类型", example = "chat")
    private String chatType;

    /**
     * 关联的知识库名称
     */
    @Schema(description = "关联的知识库名称", example = "default")
    private String kbName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date updateTime;

    /**
     * 当前会话消息总数
     */
    @Schema(description = "当前会话消息总数", example = "5")
    private Integer messageCount;

    /**
     * 所属用户ID
     */
    @Schema(description = "所属用户ID", example = "1")
    private Long userId;

    /**
     * 会话中的消息列表（可选）
     */
    @Schema(description = "会话中的消息列表")
    private List<MessageDto> messages;

    /**
     * 消息DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "消息信息")
    public static class MessageDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 消息ID
         */
        @Schema(description = "消息ID", example = "1")
        private Long id;

        /**
         * 消息角色：user/assistant
         */
        @Schema(description = "消息角色", example = "user", allowableValues = {"user", "assistant"})
        private String role;

        /**
         * 消息内容
         */
        @Schema(description = "消息内容", example = "你好，请介绍一下自己")
        private String content;

        /**
         * 消息来源（JSON格式）
         */
        @Schema(description = "消息来源")
        private String sources;

        /**
         * 知识图谱数据（JSON格式）
         */
        @Schema(description = "知识图谱数据")
        private String graphData;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private Date createTime;
    }
}

"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = createVitePlugins;

var _pluginVue = _interopRequireDefault(require("@vitejs/plugin-vue"));

var _autoImport = _interopRequireDefault(require("./auto-import"));

var _svgIcon = _interopRequireDefault(require("./svg-icon"));

var _compression = _interopRequireDefault(require("./compression"));

var _setupExtend = _interopRequireDefault(require("./setup-extend"));

var _vitePluginCesium = _interopRequireDefault(require("vite-plugin-cesium"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance"); }

function _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }

function createVitePlugins(viteEnv) {
  var isBuild = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var vitePlugins = [(0, _pluginVue["default"])()];
  vitePlugins.push((0, _autoImport["default"])());
  vitePlugins.push((0, _setupExtend["default"])());
  vitePlugins.push((0, _svgIcon["default"])(isBuild));
  vitePlugins.push((0, _vitePluginCesium["default"])());
  isBuild && vitePlugins.push.apply(vitePlugins, _toConsumableArray((0, _compression["default"])(viteEnv)));
  return vitePlugins;
}
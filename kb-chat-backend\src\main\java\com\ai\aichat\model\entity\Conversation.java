package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 会话记录表
 * @TableName conversation
 */
@TableName(value ="conversation")
@Data
public class Conversation {
    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话唯一标识
     */
    private String sessionId;

    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话类型（群聊/私聊等）
     */
    private String chatType;

    /**
     * 关联的知识库名称
     */
    private String kbName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 当前会话消息总数
     */
    private Integer messageCount;

    /**
     * 所属用户ID（外键）
     */
    private Long userId;

    /**
     * 软删除标记 0-正常 1-删除
     */
    @TableLogic
    private Integer isDelete;
}
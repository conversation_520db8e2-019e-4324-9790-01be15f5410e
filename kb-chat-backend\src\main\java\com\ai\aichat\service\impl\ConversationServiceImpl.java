package com.ai.aichat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ai.aichat.model.entity.Conversation;
import com.ai.aichat.service.ConversationService;
import com.ai.aichat.mapper.ConversationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【conversation(会话记录表)】的数据库操作Service实现
* @createDate 2025-04-29 17:57:18
*/
@Service
public class ConversationServiceImpl extends ServiceImpl<ConversationMapper, Conversation>
    implements ConversationService{
    @Autowired
    private ConversationMapper conversationMapper;

    @Override
    public void saveConversation(String chatType, String sessionId) {
        Conversation conversation = new Conversation();
        conversation.setChatType(chatType);
        conversation.setSessionId(sessionId);
        conversation.setTitle("");
        conversation.setKbName("");
        conversation.setMessageCount(0);
        conversation.setUserId(1L); // 使用默认系统用户ID
        // isDelete、createTime、updateTime字段会通过自动填充设置

        conversationMapper.insert(conversation);
//        this.save(conversation);
    }

    @Override
    public void saveConversation(String type, String chatId, String kbName) {
        Conversation conversation = new Conversation();
        conversation.setChatType(type);
        conversation.setSessionId(chatId);
        conversation.setTitle("");
        conversation.setKbName(kbName);
        conversation.setMessageCount(0);
        conversation.setUserId(1L);

        conversationMapper.insert(conversation);
    }
}





package com.ai.aichat.service;

import com.ai.aichat.model.entity.KnowledgeBaseFile;
import com.ai.aichat.model.vo.response.KnowledgeBaseFileVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 知识库文件服务接口
 */
public interface KnowledgeBaseFileService extends IService<KnowledgeBaseFile> {

    /**
     * 上传文件到知识库
     * @param file 上传的文件
     * @param kbName 知识库名称
     * @return 上传结果
     */
    Map<String, Object> uploadFile(MultipartFile file, Long kbId, String kbName);

    /**
     * 从知识库删除文件（根据唯一文件名）
     * @param kbName 知识库名称
     * @param fileName 唯一文件名
     * @return 删除结果
     */
    Boolean removeFile(Long id, String kbName, String fileName);


    /**
     * 获取知识库文件列表
     * @param kbName 知识库名称
     * @return 文件列表
     */
    List<KnowledgeBaseFileVo> getKnowledgeBaseFiles(String kbName);

    /**
     * 处理文档（向量化）
     * @param kbName 知识库名称
     * @param fileNames 文件名列表
     * @return 处理结果
     */
    Map<String, Object> processDocuments(Long kbId, String kbName, List<String> fileNames);

    /**
     * 获取处理进度
     * @param kbName 知识库名称
     * @return 处理进度信息
     */
    Map<String, Object> getProcessProgress(String kbName);

    /**
     * 根据文件名获取文件信息
     * @param kbName 知识库名称
     * @param fileName 文件名
     * @return 文件信息
     */
    KnowledgeBaseFileVo getFileInfo(String kbName, String fileName);

    /**
     * 更新文件处理状态
     * @param fileId 文件ID
     * @param status 处理状态
     * @param progress 处理进度
     * @param errorMessage 错误信息
     */
    void updateProcessStatus(Long fileId, String status, Integer progress, String errorMessage);

    /**
     * 检查文件是否已存在
     * @param kbName 知识库名称
     * @param fileName 文件名
     * @param fileMd5 文件MD5
     * @return 是否存在
     */
    Boolean fileExists(String kbName, String fileName, String fileMd5);

    /**
     * 删除知识库下的所有文件
     * @param kbName 知识库名称
     * @return 删除的文件数量
     */
    Integer removeAllFilesByKnowledgeBase(String kbName);

    /**
     * 删除文件（使用kb_id）
     * @param kbId 知识库ID
     * @param fileName 文件名
     * @return 删除结果
     */
    Boolean removeFileByKbId(Long kbId, String fileName);

    /**
     * 获取知识库文件列表（使用kb_id）
     * @param kbId 知识库ID
     * @return 文件列表
     */
    List<KnowledgeBaseFileVo> getKnowledgeBaseFilesByKbId(Long kbId);

    /**
     * 删除知识库下的所有文件（使用kb_id）
     * @param kbId 知识库ID
     * @return 删除的文件数量
     */
    Integer removeAllFilesByKbId(Long kbId);

    /**
     * 获取知识库文件列表（分页）
     * @param kbId 知识库ID
     * @param kbName 知识库名称
     * @param page 页码
     * @param pageSize 每页大小
     * @param statusFilter 状态过滤 (pending, completed, all)
     * @return 分页结果
     */
    Map<String, Object> getKnowledgeBaseFilesPaged(Long kbId, String kbName, Integer page, Integer pageSize, String statusFilter);

    /**
     * 通用文件上传
     * @param file 文件
     * @return 上传结果
     */
    Map<String, Object> commonUpload(MultipartFile file);

    /**
     * 获取知识库文件数量（使用kb_id）
     * @param kbId 知识库ID
     * @return 文件数量
     */
    Integer getFileCountByKbId(Long kbId);

    /**
     * 调试：检查文件状态
     * @param kbId 知识库ID
     * @param fileName 文件名（可选）
     * @return 调试信息
     */
    Map<String, Object> debugFileStatus(Long kbId, String fileName);

    /**
     * 重新处理失败的文档
     * @param kbId 知识库ID
     * @param kbName 知识库名称
     * @param fileNames 文件名列表
     * @return 处理结果
     */
    Map<String, Object> retryFailedDocuments(Long kbId, String kbName, List<String> fileNames);
}

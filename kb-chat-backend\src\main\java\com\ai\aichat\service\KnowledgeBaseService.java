package com.ai.aichat.service;

import com.ai.aichat.model.entity.KnowledgeBase;
import com.ai.aichat.model.vo.response.KnowledgeBaseVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 知识库服务接口
 */
public interface KnowledgeBaseService extends IService<KnowledgeBase> {

    /**
     * 获取知识库名称列表
     *
     * @return 知识库名称列表
     */
    List<String> listKnowledgeBaseNames();

    /**
     * 获取所有知识库的详细信息
     *
     * @return 知识库详细信息列表
     */
    List<KnowledgeBaseVo> getAllKnowledgeBases();

    /**
     * 根据名称获取知识库实体
     *
     * @param name 知识库名称
     * @return 知识库实体
     */
    KnowledgeBase getKnowledgeBaseEntityByName(String name);

    /**
     * 创建知识库
     *
     * @param name 知识库名称
     * @param description 知识库描述
     * @return 是否创建成功
     */
    Boolean createKnowledgeBase(String name, String description);

    /**
     * 删除知识库
     *
     * @param id 知识库ID
     * @return 是否删除成功
     */
    Boolean deleteKnowledgeBase(Long id);

    /**
     * 更新知识库名称和/或描述
     *
     * @param kbId 知识库ID
     * @param newName 新名称（可选）
     * @param description 新描述（可选）
     * @return 是否更新成功
     */
    Boolean updateKnowledgeBase(Long kbId, String newName, String description);

    /**
     * 根据ID获取知识库实体
     *
     * @param kbId 知识库ID
     * @return 知识库实体
     */
    KnowledgeBase getKnowledgeBaseEntityById(Long kbId);
}

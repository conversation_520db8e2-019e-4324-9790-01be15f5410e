package com.ai.aichat.mcp;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

/**
 * MCP深度调查测试类
 * 深入调查MCP工具为什么没有被识别
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
@DisplayName("MCP深度调查测试")
class McpDeepInvestigationTest {

    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    @Qualifier("localToolCallbackProvider")
    private ToolCallbackProvider toolCallbackProvider;

    @BeforeEach
    void setUp() {
        log.info("开始MCP深度调查测试");
    }

    @Test
    @DisplayName("检查Spring上下文中的所有MCP相关Bean")
    void testAllMcpBeansInContext() {
        log.info("=== 检查Spring上下文中的所有MCP相关Bean ===");
        
        // 获取所有Bean名称
        String[] allBeanNames = applicationContext.getBeanDefinitionNames();
        log.info("Spring上下文中总共有 {} 个Bean", allBeanNames.length);
        
        // 查找MCP相关的Bean
        int mcpBeanCount = 0;
        for (String beanName : allBeanNames) {
            if (beanName.toLowerCase().contains("mcp")) {
                mcpBeanCount++;
                Object bean = applicationContext.getBean(beanName);
                log.info("发现MCP相关Bean: {} (类型: {})", beanName, bean.getClass().getName());
            }
        }
        
        log.info("总共发现 {} 个MCP相关Bean", mcpBeanCount);
        
        // 特别检查SyncMcpToolCallbackProvider
        try {
            SyncMcpToolCallbackProvider syncMcpProvider = applicationContext.getBean(SyncMcpToolCallbackProvider.class);
            log.info("✅ 发现SyncMcpToolCallbackProvider: {}", syncMcpProvider.getClass().getName());
            
            var mcpTools = syncMcpProvider.getToolCallbacks();
            log.info("SyncMcpToolCallbackProvider提供的工具数量: {}", mcpTools.length);
            
            for (int i = 0; i < mcpTools.length; i++) {
                var tool = mcpTools[i];
                log.info("MCP工具 {}: {} ({})", i + 1, tool.getName(), tool.getClass().getSimpleName());
                log.info("  描述: {}", tool.getDescription());
            }
            
        } catch (Exception e) {
            log.warn("❌ 没有找到SyncMcpToolCallbackProvider: {}", e.getMessage());
        }
        
        // 检查当前的ToolCallbackProvider
        log.info("当前ToolCallbackProvider类型: {}", toolCallbackProvider.getClass().getName());
        var currentTools = toolCallbackProvider.getToolCallbacks();
        log.info("当前ToolCallbackProvider提供的工具数量: {}", currentTools.length);
    }

    @Test
    @DisplayName("检查MCP客户端Bean")
    void testMcpClientBeans() {
        log.info("=== 检查MCP客户端Bean ===");
        
        // 查找所有可能的MCP客户端Bean
        String[] allBeanNames = applicationContext.getBeanDefinitionNames();
        
        for (String beanName : allBeanNames) {
            if (beanName.toLowerCase().contains("client") && beanName.toLowerCase().contains("mcp")) {
                Object bean = applicationContext.getBean(beanName);
                log.info("发现MCP客户端Bean: {} (类型: {})", beanName, bean.getClass().getName());
            }
        }
        
        // 尝试按类型查找
        try {
            var mcpClients = applicationContext.getBeansOfType(Object.class);
            for (var entry : mcpClients.entrySet()) {
                String className = entry.getValue().getClass().getName();
                if (className.toLowerCase().contains("mcp") && className.toLowerCase().contains("client")) {
                    log.info("按类型发现MCP客户端: {} -> {}", entry.getKey(), className);
                }
            }
        } catch (Exception e) {
            log.error("按类型查找MCP客户端失败", e);
        }
    }

    @Test
    @DisplayName("检查MCP配置属性")
    void testMcpConfigurationProperties() {
        log.info("=== 检查MCP配置属性 ===");
        
        // 查找配置相关的Bean
        String[] allBeanNames = applicationContext.getBeanDefinitionNames();
        
        for (String beanName : allBeanNames) {
            if (beanName.toLowerCase().contains("mcp") && 
                (beanName.toLowerCase().contains("config") || 
                 beanName.toLowerCase().contains("properties") ||
                 beanName.toLowerCase().contains("auto"))) {
                Object bean = applicationContext.getBean(beanName);
                log.info("发现MCP配置Bean: {} (类型: {})", beanName, bean.getClass().getName());
            }
        }
    }

    @Test
    @DisplayName("尝试手动创建MCP工具回调提供者")
    void testManualMcpToolCallbackProvider() {
        log.info("=== 尝试手动创建MCP工具回调提供者 ===");
        
        try {
            // 尝试查找MCP同步客户端
            var allBeans = applicationContext.getBeansOfType(Object.class);
            
            for (var entry : allBeans.entrySet()) {
                String className = entry.getValue().getClass().getName();
                if (className.contains("McpSyncClient") || className.contains("McpAsyncClient")) {
                    log.info("发现MCP客户端: {} -> {}", entry.getKey(), className);
                    
                    // 尝试获取工具列表
                    Object client = entry.getValue();
                    log.info("MCP客户端详细信息: {}", client.toString());
                }
            }
            
        } catch (Exception e) {
            log.error("手动创建MCP工具回调提供者失败", e);
        }
    }
}

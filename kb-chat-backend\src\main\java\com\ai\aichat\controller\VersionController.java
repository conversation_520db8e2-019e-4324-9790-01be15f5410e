package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.model.vo.response.ModelStructureVo;
import com.ai.aichat.service.ModelStructureService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Tag(name = "版本管理", description = "模型版本管理相关接口")
@RestController
@RequestMapping("/version")
public class VersionController {

    private static final Logger log = LoggerFactory.getLogger(VersionController.class);

    @Autowired
    private ModelStructureService modelStructureService;

    @Value("${app.model.upload-path:data/models}")
    private String modelUploadPath;

    @Value("${app.model.export-path:data/exports}")
    private String modelExportPath;

    /**
     * 初始化方法，确保上传和导出目录存在
     */
    @PostConstruct
    public void init() {
        try {
            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");
            log.info("项目根目录: {}", projectRoot);

            // 创建上传目录（基于项目根目录）
            Path uploadPath = Paths.get(projectRoot, modelUploadPath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                log.info("创建上传目录: {}", uploadPath.toAbsolutePath());
            } else {
                log.info("上传目录已存在: {}", uploadPath.toAbsolutePath());
            }

            // 创建导出目录（基于项目根目录）
            Path exportPath = Paths.get(projectRoot, modelExportPath);
            if (!Files.exists(exportPath)) {
                Files.createDirectories(exportPath);
                log.info("创建导出目录: {}", exportPath.toAbsolutePath());
            } else {
                log.info("导出目录已存在: {}", exportPath.toAbsolutePath());
            }
        } catch (Exception e) {
            log.error("初始化目录失败", e);
        }
    }

    @Operation(summary = "更新模型信息")
    @PutMapping("/model/{id}")
    public BaseResponse<Boolean> updateModel(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {

        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "模型ID无效");
        ThrowUtils.throwIf(request == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");

        String version = (String) request.get("version");
        String description = (String) request.get("description");

        Boolean result = modelStructureService.updateModelStructure(id, version, null, null, description, null);
        return ResultUtils.success(result);
    }

    @Operation(summary = "删除模型")
    @DeleteMapping("/model/{id}")
    public BaseResponse<Boolean> deleteModel(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {

        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "模型ID无效");
        Boolean result = modelStructureService.deleteModelStructure(id);
        return ResultUtils.success(result);
    }

    @Operation(summary = "上传模型文件夹")
    @PostMapping("/upload-model-folder")
    public BaseResponse<Map<String, Object>> uploadModelFolder(
            @RequestParam("folderName") String folderName,
            @RequestParam Map<String, MultipartFile> files) {

        ThrowUtils.throwIf(folderName == null || folderName.trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "文件夹名称不能为空");
        ThrowUtils.throwIf(files == null || files.isEmpty(),
                ErrorCode.PARAMS_ERROR, "文件列表不能为空");

        Map<String, Object> result = modelStructureService.uploadModelFolder(folderName, files);
        return ResultUtils.success(result);
    }

    @Operation(summary = "导入模型信息")
    @PostMapping("/import-model")
    public BaseResponse<Boolean> importModel(@RequestBody Map<String, Object> request) {

        ThrowUtils.throwIf(request == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");

        String version = (String) request.get("version");
        String baseModel = (String) request.get("base_model");
        String description = (String) request.get("description");
        String modelPath = (String) request.get("model_path");
        String dataset = (String) request.get("dataset");

        ThrowUtils.throwIf(version == null || version.trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "版本不能为空");
        ThrowUtils.throwIf(baseModel == null || baseModel.trim().isEmpty(),
                ErrorCode.PARAMS_ERROR, "基础模型不能为空");

        Boolean result = modelStructureService.createModelStructure(
            version.trim(),
            baseModel.trim(),
            dataset,
            description,
            modelPath
        );

        return ResultUtils.success(result);
    }

    @Operation(summary = "获取导出路径")
    @GetMapping("/export-path")
    public BaseResponse<Map<String, String>> getExportPath() {
        Map<String, String> result = modelStructureService.getExportPath();
        return ResultUtils.success(result);
    }

    @Operation(summary = "导出模型")
    @PostMapping("/export-model/{id}")
    public BaseResponse<Map<String, Object>> exportModel(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {

        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "模型ID无效");
        Map<String, Object> result = modelStructureService.exportModel(id);
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取模型详情")
    @GetMapping("/model/{id}")
    public BaseResponse<ModelStructureVo> getModelDetail(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long id) {

        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "模型ID无效");
        ModelStructureVo model = modelStructureService.getModelStructureById(id);
        return ResultUtils.success(model);
    }
}

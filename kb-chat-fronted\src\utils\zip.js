import * as ZIP from '@zip.js/zip.js'
export async function unzip(base64Str) {
    const reader = new ZIP.ZipReader(
        new ZIP.Data64URIReader('data:application/zip;base64,' + base64Str)
    )
    const entries = await reader.getEntries()
    const data = await entries[0].getData(new ZIP.TextWriter())
    await reader.close()
    return data
}
export async function zip(str) {
    const inputBlob = new Blob([str], {type: 'text/plain'})
    const zipWriter = new ZIP.ZipWriter(new ZIP.Data64URIWriter('application/zip'))
    await zipWriter.add('text.txt', new ZIP.BlobReader(inputBlob))
    let data = await zipWriter.close()
    data = data.replace("data:application/zip;base64,", "")
    return data;
}
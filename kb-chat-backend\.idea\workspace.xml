<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3329a2f7-0b4c-4e3d-8871-fc91e3eda25a" name="Changes" comment="[new]文档处理websocket支持，代码优化等">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ai/aichat/controller/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ai/aichat/controller/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ai/aichat/model/entity/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ai/aichat/model/entity/User.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JUnit5 Test Class" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="C:\Code\codeEnvironment\apache-maven-3.8.8\" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="C:\Code\codeEnvironment\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yBnhWDKbQwA2YzvyzgNcuxegGN" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "JUnit.CommonConfigurationTest.executor": "Run",
    "JUnit.CommonConfigurationTest.testChat.executor": "Run",
    "JUnit.CommonConfigurationTest.testChatClientBeanCreation.executor": "Run",
    "JUnit.CommonConfigurationTest.testChatClientCall.executor": "Run",
    "JUnit.CommonConfigurationTest.testChatClientInitialization (1).executor": "Run",
    "JUnit.CommonConfigurationTest.testChatClientInitialization.executor": "Run",
    "JUnit.CommonConfigurationTest.testChatMemory.executor": "Run",
    "JUnit.CommonConfigurationTest.testChatMemoryBeanCreation.executor": "Run",
    "JUnit.CommonConfigurationTest.testChatMemoryStream.executor": "Run",
    "JUnit.EnhancedToolCallAgentTest.testLoopDetection.executor": "Run",
    "JUnit.EnhancedToolCallAgentTest.testUserInteraction.executor": "Debug",
    "JUnit.FileOperationToolTest.testReadFile.executor": "Debug",
    "JUnit.FileOperationToolTest.testWriteFile.executor": "Debug",
    "JUnit.KnowledgeBaseSearchTest.testGetAllKnowledgeBases.executor": "Debug",
    "JUnit.LoveAppTest.doChatWithMcp.executor": "Run",
    "JUnit.LoveAppTest.doChatWithRag.executor": "Run",
    "JUnit.LoveAppTest.testChat.executor": "Run",
    "JUnit.ManusTest.run.executor": "Run",
    "JUnit.MilvusTest.testVectorStoreAddAndSearch (1).executor": "Run",
    "JUnit.MilvusTest.testVectorStoreAddAndSearch.executor": "Run",
    "JUnit.MilvusVectorStoreTest.delete.executor": "Debug",
    "JUnit.PDFGenerationToolTest.testGeneratePDF.executor": "Debug",
    "JUnit.PgVectorVectorStoreConfigTest.executor": "Run",
    "JUnit.PgVectorVectorStoreConfigTest.test.executor": "Debug",
    "JUnit.PostgreSQLConnectionTest.testPostgreSQLConnection.executor": "Run",
    "JUnit.RagAppTest.doChatWithRag.executor": "Run",
    "JUnit.RedisConnectionTest.testRedisConnection.executor": "Run",
    "JUnit.ResourceDownloadToolTest.testDownloadResource.executor": "Run",
    "JUnit.WebScrapingToolTest.testScrapeWebPage.executor": "Debug",
    "JUnit.WebSearchToolTest.testSearchWeb.executor": "Debug",
    "JUnit.WindowsTerminalOperationToolTest.testExecuteTerminalCommand.executor": "Debug",
    "Maven.ai-chat [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.AiChatApplication.executor": "Run",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary": "JUnit5",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5": "",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Code/project/redis/hm-dianping",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Code\project\kb-chat\kb-chat-backend\src\main\resources\static\fonts" />
      <recent name="C:\Code\project\kb-chat\kb-chat-backend\src\main\resources\document" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="com.ai.aichat.util" />
      <recent name="com.ai.aichat.love" />
      <recent name="com.ai.aichat.agent" />
      <recent name="com.ai.aichat.tools" />
      <recent name="com.ai.aichat.config" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ai.aichat.love.rag" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <myKeys>
      <visibility group="Gradle 任务" flag="true" />
      <visibility group="Grunt" flag="true" />
      <visibility group="Gulp" flag="true" />
      <visibility group="HTTP Requests" flag="true" />
      <visibility group="HTTP 请求" flag="true" />
      <visibility group="Maven 目标" flag="true" />
      <visibility group="Node.js" flag="true" />
      <visibility group="npm" flag="true" />
      <visibility group="yarn" flag="true" />
      <visibility group="最近的项目" flag="true" />
      <visibility group="运行配置" flag="true" />
    </myKeys>
  </component>
  <component name="RunManager" selected="Spring Boot.AiChatApplication">
    <configuration name="LoveAppTest.doChatWithRag" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ai-chat" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.aichat.love.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.aichat.love" />
      <option name="MAIN_CLASS_NAME" value="com.ai.aichat.love.LoveAppTest" />
      <option name="METHOD_NAME" value="doChatWithRag" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ManusTest.run" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ai-chat" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.aichat.agent.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.aichat.agent" />
      <option name="MAIN_CLASS_NAME" value="com.ai.aichat.agent.ManusTest" />
      <option name="METHOD_NAME" value="run" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MilvusVectorStoreTest.delete" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ai-chat" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.aichat.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.aichat.util" />
      <option name="MAIN_CLASS_NAME" value="com.ai.aichat.util.MilvusVectorStoreTest" />
      <option name="METHOD_NAME" value="delete" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RagAppTest.doChatWithRag" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ai-chat" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.aichat.love.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.aichat.love" />
      <option name="MAIN_CLASS_NAME" value="com.ai.aichat.love.RagAppTest" />
      <option name="METHOD_NAME" value="doChatWithRag" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RedisConnectionTest.testRedisConnection" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ai-chat" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.aichat.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.aichat" />
      <option name="MAIN_CLASS_NAME" value="com.ai.aichat.RedisConnectionTest" />
      <option name="METHOD_NAME" value="testRedisConnection" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="chat-backend" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="chat-backend" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AiChatApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ai-chat" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.aichat.AiChatApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.RedisConnectionTest.testRedisConnection" />
        <item itemvalue="JUnit.MilvusVectorStoreTest.delete" />
        <item itemvalue="JUnit.ManusTest.run" />
        <item itemvalue="JUnit.RagAppTest.doChatWithRag" />
        <item itemvalue="JUnit.LoveAppTest.doChatWithRag" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19072.14" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19072.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3329a2f7-0b4c-4e3d-8871-fc91e3eda25a" name="Changes" comment="" />
      <created>1749316954080</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749316954080</updated>
      <workItem from="1749316955214" duration="6856000" />
      <workItem from="1749351448237" duration="41570000" />
      <workItem from="1749435301645" duration="368000" />
      <workItem from="1749435678103" duration="1109000" />
      <workItem from="1749437207914" duration="627000" />
      <workItem from="1749440046937" duration="27578000" />
      <workItem from="1749741104447" duration="3286000" />
      <workItem from="1749744598683" duration="19120000" />
      <workItem from="1749791804581" duration="2161000" />
      <workItem from="1749794531217" duration="9620000" />
      <workItem from="1749822431849" duration="25110000" />
      <workItem from="1749894435321" duration="38719000" />
      <workItem from="1750662278628" duration="3416000" />
      <workItem from="1750845468178" duration="346000" />
      <workItem from="1752580863736" duration="2070000" />
      <workItem from="1752664617842" duration="26000" />
      <workItem from="1752718997933" duration="13495000" />
      <workItem from="1752740130393" duration="45677000" />
      <workItem from="1752826726432" duration="25716000" />
      <workItem from="1752929835780" duration="172000" />
      <workItem from="1752930014661" duration="25563000" />
      <workItem from="1753062493045" duration="30445000" />
    </task>
    <task id="LOCAL-00001" summary="[new]后端项目代码初始化">
      <option name="closed" value="true" />
      <created>1749366550017</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749366550017</updated>
    </task>
    <task id="LOCAL-00002" summary="[new]SSE响应实现，历史会话列表实现">
      <option name="closed" value="true" />
      <created>1749377029114</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749377029114</updated>
    </task>
    <task id="LOCAL-00003" summary="[new]知识库CRUD实现">
      <option name="closed" value="true" />
      <created>1749386254195</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749386254195</updated>
    </task>
    <task id="LOCAL-00004" summary="[new]知识库文件管理CRUD实现">
      <option name="closed" value="true" />
      <created>1749396925316</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1749396925316</updated>
    </task>
    <task id="LOCAL-00005" summary="[new]问答库文件管理CRUD实现">
      <option name="closed" value="true" />
      <created>1749403319855</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1749403319855</updated>
    </task>
    <task id="LOCAL-00006" summary="[new+bugfix]训练界面，版本管理界面接口实现，重启后不能构建历史记录提示词bug修复">
      <option name="closed" value="true" />
      <created>1749788999122</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1749788999123</updated>
    </task>
    <task id="LOCAL-00007" summary="[new]tools工具调用">
      <option name="closed" value="true" />
      <created>1749833044781</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1749833044781</updated>
    </task>
    <task id="LOCAL-00008" summary="[new]MCP工具调用及本地服务端开发">
      <option name="closed" value="true" />
      <created>1749907422122</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1749907422122</updated>
    </task>
    <task id="LOCAL-00009" summary="[new]AI智能体构建">
      <option name="closed" value="true" />
      <created>1749918170705</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1749918170705</updated>
    </task>
    <task id="LOCAL-00010" summary="[bugfix]when using the new version of Ollama and a tool call is made, a NullPointerException occurs involving &quot;evalDuration&quot;.">
      <option name="closed" value="true" />
      <created>1749963471948</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1749963471948</updated>
    </task>
    <task id="LOCAL-00011" summary="[new]添加milvus向量数据库，实现了demo">
      <option name="closed" value="true" />
      <created>1752752735833</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752752735833</updated>
    </task>
    <task id="LOCAL-00012" summary="[new]前端添加文件到milvus，rag问答支持">
      <option name="closed" value="true" />
      <created>1752834221953</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752834221953</updated>
    </task>
    <task id="LOCAL-00013" summary="[new]milvus 的 collection 创建，删除，以及根据文件名称删除collection 中对应的向量">
      <option name="closed" value="true" />
      <created>1752852101682</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752852101682</updated>
    </task>
    <task id="LOCAL-00014" summary="[new]文档处理websocket支持，代码优化等">
      <option name="closed" value="true" />
      <created>1753157424156</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753157424156</updated>
    </task>
    <option name="localTasksCounter" value="15" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="[new]后端项目代码初始化" />
    <MESSAGE value="[new]SSE响应实现，历史会话列表实现" />
    <MESSAGE value="[new]知识库CRUD实现" />
    <MESSAGE value="[new]知识库文件管理CRUD实现" />
    <MESSAGE value="[new]问答库文件管理CRUD实现" />
    <MESSAGE value="[new+bugfix]训练界面，版本管理界面接口实现，重启后不能构建历史记录提示词bug修复" />
    <MESSAGE value="[new]tools工具调用" />
    <MESSAGE value="[new]MCP工具调用及本地服务端开发" />
    <MESSAGE value="[new]AI智能体构建" />
    <MESSAGE value="[bugfix]when using the new version of Ollama and a tool call is made, a NullPointerException occurs involving &quot;evalDuration&quot;." />
    <MESSAGE value="[new]添加milvus向量数据库，实现了demo" />
    <MESSAGE value="[new]前端添加文件到milvus，rag问答支持" />
    <MESSAGE value="[new]milvus 的 collection 创建，删除，以及根据文件名称删除collection 中对应的向量" />
    <MESSAGE value="[new]文档处理websocket支持，代码优化等" />
    <option name="LAST_COMMIT_MESSAGE" value="[new]文档处理websocket支持，代码优化等" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/com/ai/aichat/tools/WindowsTerminalOperationToolTest.java</url>
          <line>13</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/com/ai/aichat/tools/WebSearchToolTest.java</url>
          <line>19</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/tools/FileOperationTool.java</url>
          <line>25</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/tools/WebScrapingTool.java</url>
          <line>14</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/com/ai/aichat/tools/KnowledgeBaseSearchTest.java</url>
          <line>19</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/config/MyLoggerAdvisor.java</url>
          <line>26</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/config/MyLoggerAdvisor.java</url>
          <line>30</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/tools/WebSearchTool.java</url>
          <line>29</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/tools/WebSearchTool.java</url>
          <line>44</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/MilvusServiceImpl.java</url>
          <line>138</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/MilvusServiceImpl.java</url>
          <line>93</line>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/MilvusServiceImpl.java</url>
          <line>108</line>
          <option name="timeStamp" value="53" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/MilvusServiceImpl.java</url>
          <line>104</line>
          <option name="timeStamp" value="59" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/MilvusServiceImpl.java</url>
          <line>132</line>
          <option name="timeStamp" value="62" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/util/MilvusVectorStore.java</url>
          <line>78</line>
          <option name="timeStamp" value="66" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/KnowledgeBaseRagServiceImpl.java</url>
          <line>75</line>
          <option name="timeStamp" value="73" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/KnowledgeBaseFileServiceImpl.java</url>
          <line>172</line>
          <option name="timeStamp" value="75" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/KnowledgeBaseFileServiceImpl.java</url>
          <line>144</line>
          <option name="timeStamp" value="76" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ai/aichat/service/impl/KnowledgeBaseFileServiceImpl.java</url>
          <line>177</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="public Boolean createKnowledgeBase(String name, String description) {&#10;        try {&#10;            // 检查知识库是否已存在&#10;            QueryWrapper&lt;KnowledgeBase&gt; queryWrapper = new QueryWrapper&lt;&gt;();&#10;            queryWrapper.eq(&quot;name&quot;, name)&#10;                       .eq(&quot;is_delete&quot;, 0);&#10;&#10;            if (getOne(queryWrapper) != null) {&#10;                throw new RuntimeException(&quot;知识库已存在: &quot; + name);&#10;            }&#10;            &#10;            // 1.创建新知识库&#10;            KnowledgeBase knowledgeBase = new KnowledgeBase();&#10;            knowledgeBase.setName(name);&#10;            knowledgeBase.setDescription(description != null ? description : &quot;&quot;);&#10;            knowledgeBase.setUserId(1L); // 默认用户ID，可以根据实际需求修改&#10;            &#10;            boolean result = save(knowledgeBase);" />
      </configuration>
    </watches-manager>
  </component>
</project>
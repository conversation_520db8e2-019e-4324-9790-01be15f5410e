package com.ai.aichat.controller;

import com.ai.aichat.service.MilvusService;
import com.ai.aichat.util.TikaUtil;
import com.ai.aichat.util.TikaVo;
import com.alibaba.fastjson.JSON;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.*;

@RestController
@RequestMapping("/api/vector")
@Tag(name = "向量数据库实践", description = "向量数据库实践")
@RequiredArgsConstructor
public class MilvusController {

    private final MilvusService milvusService;
    private final TikaUtil tikaUtil;
    private final ChatClient ragChatClient;
    private final OpenAiEmbeddingModel embeddingModel;

    @Autowired
    public MilvusController(
            OpenAiEmbeddingModel embeddingModel,
            MilvusService milvusService,
            TikaUtil tikaUtil,
            @Qualifier("ragChatClient") ChatClient ragChatClient) {
        this.embeddingModel = embeddingModel;
        this.milvusService = milvusService;
        this.tikaUtil = tikaUtil;
        this.ragChatClient = ragChatClient;
    }



    @Operation(summary = "添加自定义rag数据")
    @GetMapping("/addCustomRagData")
    public ResponseEntity<InsertResp> addCustomRagData(@RequestParam String text) {
        Assert.notNull(text, "text不能为空");
        float[] embed = embeddingModel.embed(text);
        // 插入数据
        InsertResp insert = milvusService.insert("default", embed, text, JSON.toJSONString(new HashMap<>().put("custom", text)), "custom");
        return ResponseEntity.ok(insert);
    }

    @Operation(summary = "搜索")
    @GetMapping("/search")
    public ResponseEntity<SearchResp> search(@RequestParam String text) {
        Assert.notNull(text, "text不能为空");
        float[] embed = embeddingModel.embed(text);
        // 搜索数据
        return ResponseEntity.ok(milvusService.search("default",embed));
    }

    @Operation(summary = "解析文件内容")
    @PostMapping("/extractFileString")
    public ResponseEntity<String> extractFileString(MultipartFile file) {
        return ResponseEntity.ok(tikaUtil.extractTextString(file));
    }

    @Operation(summary = "解析文件内容-LangChina分片")
    @PostMapping("/splitParagraphsLangChain")
    public ResponseEntity<List<String>> splitParagraphsLangChain(MultipartFile file) {
        return ResponseEntity.ok(tikaUtil.splitParagraphsLangChain(tikaUtil.extractTextString(file)));
    }

    @Operation(summary = "解析文件内容-HanLP分片")
    @PostMapping("/splitParagraphsHanLP")
    public ResponseEntity<List<String>> splitParagraphsHanLP(MultipartFile file) {
        return ResponseEntity.ok(tikaUtil.splitParagraphsHanLP(tikaUtil.extractTextString(file)));
    }

    @Operation(summary = "上传知识库")
    @PostMapping("/uploadFile")
    public ResponseEntity<InsertResp> uploadFile(MultipartFile file) {
        // 获取文件内容
        TikaVo tikaVo = tikaUtil.extractText(file);
        if (tikaVo != null && Objects.nonNull(tikaVo.getText())) {
            List<float[]> embedList = new ArrayList<>();
            List<String> textList = tikaVo.getText();
            List<String> metadataList = tikaVo.getMetadata();
            List<String> fileNameList = new ArrayList<>();
            for (String s : tikaVo.getText()) {
                embedList.add(embeddingModel.embed(s));
                fileNameList.add(file.getOriginalFilename());
            }
            return ResponseEntity.ok(milvusService.batchInsert("default",embedList, textList, metadataList, fileNameList));
        }
        return ResponseEntity.ok(null);
    }



    @PostMapping("/ask")
    public Flux<ChatResponse> askQuestion(@RequestBody String question) {
        // 调用 RAG 问答（自动结合向量检索和生成）
        return ragChatClient.prompt()
                .user(question)
                .stream()
                .chatResponse();
    }

    @GetMapping("/test")
    public String testRag() {
        return ragChatClient.prompt()
                .user("西游记")
                .call()
                .content();
    }
}
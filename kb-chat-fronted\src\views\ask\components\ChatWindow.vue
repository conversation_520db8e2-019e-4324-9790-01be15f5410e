<template>
  <div class="chat-container chat-window" :class="{'preview-active': showFilePreview}">
    <!-- 对话内容区域 -->
    <div class="chat-main-area">
      <div
        class="talk-window"
        ref="talkWindow"
        @scroll="handleScroll">
        <!-- 聊天消息 -->
        <div class="messages-container">
          <div
            v-for="item in formattedTalkInfo"
            :key="item.id + '-' + item.renderKey"
            class="message-row"
            :class="item.type === 'ai' ? 'ai-message' : 'user-message'">
            <div class="message-avatar">
              <img
                :src="
                  item.type === 'ai'
                    ? '/assets/images/AI.png'
                    : '/assets/images/用户.png'
                " />
            </div>
            <div class="message-bubble" v-if="!item.isEditing">
              <div class="message-content">
                <div v-if="item.type === 'ai'" class="message-header">
                  <span class="sender-name">AI助手</span>
                  <div
                    class="edit-icon"
                    @click="startEdit(item)">
                    🖊
                  </div>
                </div>
                
                <div class="message-body">
                  <div v-if="!item.isEditing">
                    <!-- 显示正在输入状态 -->
                    <div v-if="item.type === 'ai' && item.isTyping && !item.info" class="typing-indicator">
                      <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <span class="typing-text">AI正在思考中...</span>
                    </div>

                    <!-- 显示实际内容 -->
                    <div v-else>
                      <div v-if="hasThinkingContent(item.info)" class="thinking-section">
                        <div class="thinking-container">
                          <div class="thinking-header" @click="toggleThinking(item)">
                            <div class="thinking-icon">
                              <div class="brain-icon" :class="{ 'thinking-active': isThinkingInProgress(item.info) }">
                                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.11 3.89 21 5 21H11V19H5V3H13V9H21Z" fill="currentColor"/>
                                  <circle cx="12" cy="12" r="3" fill="currentColor" opacity="0.6"/>
                                  <path d="M16 12C16 14.21 14.21 16 12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8C14.21 8 16 9.79 16 12Z" stroke="currentColor" stroke-width="0.5" fill="none"/>
                                </svg>
                              </div>
                              <div class="thinking-pulse" :class="{ 'active': isThinkingInProgress(item.info) }"></div>
                            </div>
                            <div class="thinking-title-content">
                              <span class="thinking-title">{{ getThinkingTitle(item.info) }}</span>
                              <span class="thinking-subtitle">{{ getThinkingSubtitle(item.info) }}</span>
                            </div>
                            <div class="thinking-toggle" :class="{ 'expanded': getThinkingExpanded(item) }">
                              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
                              </svg>
                            </div>
                          </div>

                          <div class="thinking-content" :class="{ 'expanded': getThinkingExpanded(item) }">
                            <div class="thinking-inner">
                              <div class="thinking-process-header">
                                <div class="process-indicator">
                                  <div
                                    v-for="(step, stepIndex) in getThinkingSteps(item.info)"
                                    :key="stepIndex"
                                    class="step-indicator"
                                  >
                                    <div class="indicator-dot" :class="{ 'active': step.isComplete, 'current': step.isCurrent }"></div>
                                    <div v-if="stepIndex < getThinkingSteps(item.info).length - 1" class="indicator-line"></div>
                                  </div>
                                </div>
                                <span class="process-text">{{ getProcessText(item.info) }}</span>
                              </div>
                              <div class="markdown-body thinking-markdown" v-html="renderThinkingContent(item.info)"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="markdown-body" v-html="renderFinalContent(item.info)"></div>

                      <!-- 如果还在输入中，显示光标 -->
                       <!-- <span v-if="item.type === 'ai' && item.isTyping" class="typing-cursor">|</span> -->
                    </div>
                    
                    <!-- 所有知识库问答的AI回复都显示调用大模型按钮 -->
                    <div v-if="item.type === 'ai' && !item.isUsingLLM && props.selectedKb" class="llm-query-btn">
                      <el-button type="primary" @click="queryLLM(item)" :loading="item.isQuerying">
                        <i class="el-icon-magic-stick"></i>
                        通过大模型探索
                      </el-button>
                    </div>
                    
                    <!-- 引用来源 -->
                    <div
                      v-if="hasValidSources(item.sources) || hasGraphData(item) || hasQAPairData(item)"
                      class="sources">
                      <p class="source-title">
                        <i class="el-icon-document"></i> 参考来源：
                      </p>
                      <ul class="source-list">
                        <!-- 文档来源 -->
                        <li
                          v-for="(source, sourceIndex) in getUniqueSources(item.sources)"
                          :key="sourceIndex"
                          class="source-item"
                          :class="source.file_source === activeFileUrl ? 'active-source' : ''"
                          @click="handleSourceClick(source, formattedTalkInfo.findIndex(msg => msg.id === item.id))">
                          <span class="source-icon">📄</span>
                          <span class="source-text">{{ formatSourceTitle(source.title) }}</span>
                          <span class="view-indicator">查看 <i class="el-icon-view"></i></span>
                        </li>

                        <!-- 问答对来源 - 新增 -->
                        <li
                          v-if="hasQAPairData(item)"
                          class="source-item qa-pair-source-item"
                          :class="{'active-source': showQAPairView && activeQAPairMessageId === item.id}"
                          @click="handleQAPairClick(item)">
                          <span class="source-icon">💬</span>
                          <span class="source-text">相关问答对 ({{ getQAPairCount(item) }}个)</span>
                          <span class="view-indicator">查看 <i class="el-icon-view"></i></span>
                        </li>



                        <!-- 知识图谱来源 - 以文件形式显示 -->
                        <li
                          v-if="hasGraphData(item)"
                          class="source-item graph-source-item"
                          :class="{'active-source': showGraphView && activeGraphMessageId === item.id}"
                          @click="handleGraphClick(item)">
                          <span class="source-icon">📊</span>
                          <span class="source-text">关联知识图谱</span>
                          <span class="view-indicator">查看 <i class="el-icon-view"></i></span>
                        </li>
                      </ul>
                      <!-- 添加跳转到高亮的按钮 -->
                      <div v-if="showFilePreview && item.sources && item.sources.length > 0" class="highlight-jump-btn">
                        <div class="highlight-navigation">
                          <el-button type="info" size="small" @click="scrollToHighlight('prev')" :disabled="!hasHighlights">
                            <i class="el-icon-arrow-left"></i> 上一处
                          </el-button>
                          <span class="highlight-counter" v-if="hasHighlights">
                            {{ currentHighlightIndex + 1 }} / {{ totalHighlights }}
                          </span>
                          <span class="highlight-counter" v-else>
                            0 / 0
                          </span>
                          <el-button type="info" size="small" @click="scrollToHighlight('next')" :disabled="!hasHighlights">
                            下一处 <i class="el-icon-arrow-right"></i>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 编辑区域移到外面，不被气泡包裹 -->
            <div
              v-if="item.isEditing"
              class="editing-area">
              <textarea
                v-model="item.editingInfo"
                class="edit-textarea"
                @blur="saveEdit(item)"
                @input="autoAdjustEditHeight($event.target)">
              </textarea>
              <div class="editing-controls">
                <el-button
                  @click="saveEdit(item)"
                  size="small"
                  class="save-button"
                  >保存</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 回到底部按钮 -->
      <div
        v-if="showScrollToBottom"
        class="scroll-to-bottom-btn"
        @click="scrollToBottomSmooth">
        <div class="scroll-btn-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- 右边的文件预览，使用固定定位避免影响主布局 -->
    <div v-if="showFilePreview" class="file-preview-overlay">
      <div class="file-preview-container">
        <div class="preview-header">
          <div class="preview-title">参考文档</div>
          <div class="close-preview-btn" @click.stop="closePreview">
            <i class="el-icon-close"></i>
            <span>关闭预览</span>
          </div>
        </div>
        <FilePreview
          v-model="showFilePreview"
          @fileUrlChange="url => activeFileUrl = url"
          ref="filePreviewRef"
        />
      </div>
    </div>

    <!-- 右边的问答对预览 - 新增部分 -->
    <div v-if="showQAPairView" class="qa-pair-view-overlay">
      <QAPairView
        :qaPairData="currentQAPairData"
        @close="closeQAPairView"
      />
    </div>

    <!-- 右边的图谱预览 - 新增部分 -->
    <div v-if="showGraphView" class="graph-view-overlay">
      <GraphView
        :graphData="currentGraphData"
        @close="closeGraphView"
      />
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, nextTick, onMounted, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import { marked } from 'marked'; // 导入 marked
  import hljs from 'highlight.js'; // 导入 highlight.js
  import 'highlight.js/styles/atom-one-dark.css'; // 导入代码高亮样式
  import FilePreview from './FilePreview.vue';
  import GraphView from './GraphView.vue'; // 导入图谱组件
  import QAPairView from './QAPairView.vue'; // 导入问答对组件

  import { kbChatStream, chatStream } from '@/api/knowledgeManage/chat';

  const props = defineProps({
    selectedKb: {
      type: String,
      required: true,
    },
    sessionId: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['update:talkInfo', 'preview-change', 'session-created']);

  const talkWindow = ref(null);
  const userScrolling = ref(false);
  const inputValue = ref('');
  const loading = ref(false);
  const talkInfo = ref([]);
  const showFilePreview = ref(false);
  const filePreviewRef = ref(null);

  // 回到底部按钮相关
  const showScrollToBottom = ref(false);

  // 添加中止相关状态
  const abortController = ref(null);
  const isAborting = ref(false);

  // 添加高亮导航相关变量
  const currentHighlightIndex = ref(0);
  const totalHighlights = ref(0);
  const hasHighlights = ref(false);
  
  // 图谱相关变量 - 新增
  const showGraphView = ref(false);
  const currentGraphData = ref(null);
  const activeGraphMessageId = ref(null);

  // 问答对相关变量 - 新增
  const showQAPairView = ref(false);
  const currentQAPairData = ref(null);
  const activeQAPairMessageId = ref(null);

  // 添加 session_id 变量
  const currentSessionId = ref('');

  // 添加一个变量来强制更新key
  const renderKey = ref(0);

  // 添加聊天类型变量
  const chatType = ref('chat'); // 默认为普通对话

  // 添加一个标志，表示是否正在加载历史会话
  const isLoadingHistory = ref(false);

  // 添加一个变量来记录当前会话所使用的知识库名称
  const currentSessionKb = ref('');

  // 增加更新渲染key的方法
  const forceRender = () => {
    renderKey.value++;
  };

  // 将talkInfo转换为带有唯一key的对象
  const formattedTalkInfo = computed(() => {
    return talkInfo.value.map(item => ({
      ...item,
      renderKey: renderKey.value // 添加渲染key
    }));
  });

  // 监视props中的sessionId变化
  watch(
    () => props.sessionId,
    (newSessionId) => {
      // console.log('ChatWindow接收到新的sessionId:', newSessionId);
      if (newSessionId) {
        currentSessionId.value = newSessionId;
      }
    },
    { immediate: true }
  );

  // 滚动相关方法
  const scrollToBottom = async () => {
    // 先强制更新渲染key
    forceRender();
    
    // 滚动到消息列表底部，并增加重试机制确保UI更新后执行
    const doScroll = (retryCount = 0) => {
      nextTick(() => {
        if (talkWindow.value) {
          talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
          
          // 确认是否真的滚动到底部，如果没有且未超过重试次数，则重试
          if (talkWindow.value.scrollHeight - talkWindow.value.scrollTop > talkWindow.value.clientHeight + 10 && retryCount < 5) {
            // console.log('滚动尚未到底部，重试...', retryCount + 1);
            setTimeout(() => doScroll(retryCount + 1), 100 * (retryCount + 1)); // 指数退避
          }
        }
      });
    };
    
    // 先立即滚动一次，然后再次尝试（通常在DOM更新后）
    if (talkWindow.value) {
      talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
    }
    
    // 延迟执行，确保DOM已更新
    setTimeout(() => doScroll(), 50);
  };

  // 平滑滚动到底部
  const scrollToBottomSmooth = () => {
    if (talkWindow.value) {
      talkWindow.value.scrollTo({
        top: talkWindow.value.scrollHeight,
        behavior: 'smooth'
      });
      // 隐藏按钮
      showScrollToBottom.value = false;
    }
  };

  const handleScroll = (e) => {
    // 使用节流来避免过于频繁的处理
    if (handleScroll.timer) {
      clearTimeout(handleScroll.timer);
    }

    handleScroll.timer = setTimeout(() => {
      const element = e.target;
      const isAtBottom =
        element.scrollHeight - element.scrollTop - element.clientHeight < 50;
      userScrolling.value = !isAtBottom;

      // 控制回到底部按钮的显示/隐藏
      // 当用户向上滚动超过200px时显示按钮
      const scrollFromBottom = element.scrollHeight - element.scrollTop - element.clientHeight;
      showScrollToBottom.value = scrollFromBottom > 200;
    }, 16); // 约60fps的更新频率
  };

  // 监听聊天记录变化
  watch(
    () => [...talkInfo.value],
    async (newTalkInfo) => {
      // console.log('聊天记录已更新，新条目数量:', newTalkInfo.length);

      // 只有在用户没有主动滚动时才自动滚动到底部
      if (!userScrolling.value) {
        await scrollToBottom();
      }

      emit('update:talkInfo', newTalkInfo);

      // 延迟执行代码高亮，确保DOM已更新
      nextTick(() => {
        setTimeout(() => {
          document.querySelectorAll('pre code:not(.hljs)').forEach((block) => {
            console.log('手动高亮代码块:', block);
            hljs.highlightElement(block);
          });
          // 为新添加的代码块添加复制按钮
          addCopyButtonsToCodeBlocks();
        }, 100);
      });
    },
    { deep: true }
  );

  // 单独监听talkInfo的整体替换
  watch(
    talkInfo,
    (newTalkInfo) => {
      // console.log('talkInfo被整体替换，新消息数量:', newTalkInfo.length);
      scrollToBottom();
    },
    { deep: true }
  );

  // 监听 selectedKb 的变化
  watch(
    () => props.selectedKb,
    (newKb) => {
      // 如果正在加载历史会话，不执行清空操作
      if (isLoadingHistory.value) {
        // console.log('正在加载历史会话，不清空聊天记录');
        return;
      }
      
      // 如果当前已有会话且知识库变化了，才清空聊天记录
      if (talkInfo.value.length > 0 && newKb !== currentSessionKb.value) {
        // console.log('知识库变更，清空聊天记录', currentSessionKb.value, '->', newKb);
        
        // 清空聊天记录
        talkInfo.value = [];
        // 标记为新对话
        startNewChat();
        // 更新当前会话的知识库
        currentSessionKb.value = newKb || '';
      } else if (talkInfo.value.length === 0) {
        // 如果没有会话，只需更新当前知识库记录
        currentSessionKb.value = newKb || '';
      }
    }
  );

  // 添加新对话标记变量
  const isNewChat = ref(true);

  // 修改 startNewChat 方法
  const startNewChat = () => {
    isNewChat.value = true;
    currentSessionId.value = ''; // 清空当前会话ID
  };

  // 提供设置聊天类型的方法
  const setChatType = (type) => {
    // console.log('设置聊天类型为:', type);
    chatType.value = type;
  };

  // 中止回答功能
  const abortResponse = () => {
    if (abortController.value) {
      isAborting.value = true;
      abortController.value.abort();
      abortController.value = null;
      loading.value = false;

      // 更新最后一条AI消息，标记为已中止
      const lastMessage = talkInfo.value[talkInfo.value.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        lastMessage.info += '\n\n[回答已中止]';
        lastMessage.isTyping = false;
      }

      setTimeout(() => {
        isAborting.value = false;
      }, 1000);

      ElMessage.info('已中止回答');
    }
  };

  // 提交处理
  const handleSubmit = async () => {
    if (!inputValue.value.trim()) {
      ElMessage.warning('请输入问题内容！');
      return;
    }

    if (loading.value) {
      ElMessage.warning('请等待当前回答完成');
      return;
    }

    const userMessage = {
      id: talkInfo.value.length + 1,
      info: inputValue.value.trim(),
      type: 'user',
    };
    talkInfo.value.push(userMessage);

    const userMessageText = inputValue.value;
    inputValue.value = '';
    loading.value = true;

    // 创建新的AbortController
    abortController.value = new AbortController();

    try {
      // 构建请求体，确保 kb_name 为知识库名称
      const requestBody = {
        prompt: userMessageText,
        new_chat: isNewChat.value,
        session_id: currentSessionId.value
      };
      // 如果有会话ID，强制设置 new_chat 为 false
      if (currentSessionId.value) {
        requestBody.new_chat = false;
        isNewChat.value = false;
      }
      // 如果有选择知识库，则添加 kb_name（知识库名称）
      if (props.selectedKb) {
        requestBody.kb_name = props.selectedKb; // 这里 props.selectedKb 必须为知识库名称
      }
      // 添加聊天类型（如果不是默认值）
      if (chatType.value && chatType.value !== 'chat') {
        requestBody.chat_type = chatType.value;
      }
      // 发送请求
      const response = await (props.selectedKb
        ? kbChatStream({ ...requestBody, signal: abortController.value.signal })
        : chatStream({ ...requestBody, signal: abortController.value.signal }));

      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
      }

      const aiMessage = {
        id: talkInfo.value.length + 1,
        info: '',
        type: 'ai',
        sources: [],
        isTyping: true,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: false
      };
      talkInfo.value.push(aiMessage);

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;

          if (line.startsWith('data:')) {
            const data = line.slice(5).trim();
            if (!data || data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              if (parsed.code === 1) {
                aiMessage.info += parsed.data.content || '';
                if (parsed.data.session_id) {
                  currentSessionId.value = parsed.data.session_id;
                }
                if (parsed.data.sources?.length > 0) {
                  aiMessage.sources = [...parsed.data.sources];
                }
                // 处理图谱数据
                if (parsed.data.graph_data) {
                  aiMessage.graph_data = JSON.stringify(parsed.data.graph_data);
                }
                // 处理问答对数据
                if (parsed.data.qa_pair_data) {
                  aiMessage.qa_pair_data = JSON.stringify(parsed.data.qa_pair_data);
                }
                talkInfo.value = [...talkInfo.value];
              }
            } catch (e) {
              console.error('解析响应数据失败:', e);
              console.error('原始数据:', data);
              aiMessage.info = '服务器返回数据格式错误，请联系管理员';
              break;
            }
          }
        }
      }
    } catch (error) {
      // 检查是否是用户主动中止
      if (error.name === 'AbortError') {
        // 用户主动中止，不显示错误消息
        return;
      }

      ElMessage.error(error.message || '发送消息失败');
      const lastMessage = talkInfo.value[talkInfo.value.length - 1];
      if (lastMessage?.type === 'ai') {
        lastMessage.info = '发送消息失败，请稍后重试';
      }
    } finally {
      loading.value = false;
      isNewChat.value = false;
      abortController.value = null;

      // 确保AI消息的isTyping状态被正确设置为false
      const lastMessage = talkInfo.value[talkInfo.value.length - 1];
      if (lastMessage?.type === 'ai') {
        lastMessage.isTyping = false;
        talkInfo.value = [...talkInfo.value]; // 触发响应式更新
      }

      // 如果这是第一次发送消息，记录当前使用的知识库
      if (currentSessionKb.value === '') {
        currentSessionKb.value = props.selectedKb || '';
        // console.log('新对话设置知识库:', currentSessionKb.value);
      }

      // 当接收到会话ID时，通知父组件更新历史记录列表
      if (currentSessionId.value) {
        // 发送自定义事件，通知父组件刷新历史记录
        emit('session-created', currentSessionId.value);
      }
    }
  };

  // 编辑相关方法
  const startEdit = (item) => {
    // console.log('开始编辑消息:', item);
    if (item.type === 'ai') {
      // 使用Vue的响应式更新方式
      const itemIndex = talkInfo.value.findIndex(msg => msg.id === item.id);
      if (itemIndex >= 0) {
        // console.log('找到要编辑的消息索引:', itemIndex);
        
        // 创建一个新对象以确保Vue能检测到变化
        const updatedItems = [...talkInfo.value];
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          isEditing: true,
          editingInfo: updatedItems[itemIndex].info
        };
        
        // 替换整个数组以确保响应式更新
        talkInfo.value = updatedItems;
        // console.log('已更新talkInfo，设置isEditing为true');
        
        // 强制重新渲染
        forceRender();
        
        // 等待DOM更新后聚焦文本框
        nextTick(() => {
          // console.log('DOM更新后尝试聚焦编辑框');
          const textareas = document.querySelectorAll('.edit-textarea');
          // console.log('找到的编辑框数量:', textareas.length);
          
          // 直接选择最后一个编辑框
          if (textareas.length > 0) {
            const lastTextarea = textareas[textareas.length - 1];
            // console.log('聚焦到最后一个编辑框');
            lastTextarea.focus();
            autoAdjustEditHeight(lastTextarea);
          } else {
            // console.error('未找到编辑框元素');
          }
        });
      } else {
        // console.error('未找到要编辑的消息');
      }
    }
  };

  const saveEdit = (item) => {
    // console.log('保存编辑:', item);
    
    // 找到要更新的消息索引
    const itemIndex = talkInfo.value.findIndex(msg => msg.id === item.id);
    if (itemIndex >= 0) {
      // console.log('找到要保存的消息索引:', itemIndex);
      
      // 创建一个新对象以确保Vue能检测到变化
      const updatedItems = [...talkInfo.value];
      
      // 更新消息内容
      if (item.editingInfo?.trim()) {
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          info: item.editingInfo,
          isEditing: false
        };
        
        // 删除临时编辑信息
        delete updatedItems[itemIndex].editingInfo;
      } else {
        // 如果编辑内容为空，只关闭编辑模式
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          isEditing: false
        };
        
        // 删除临时编辑信息
        delete updatedItems[itemIndex].editingInfo;
      }
      
      // 替换整个数组以确保响应式更新
      talkInfo.value = updatedItems;
      // console.log('已保存编辑，设置isEditing为false');
      
      // 强制重新渲染
      forceRender();
    } else {
      // console.error('未找到要保存的消息');
    }
  };

  // 添加函数检查
  const autoAdjustEditHeight = (element) => {
    // console.log('自动调整编辑区域高度', element);
    if (!element) {
      // console.warn('无法调整编辑框高度: 元素为空');
      return;
    }
    
    element.style.height = 'auto';
    element.style.height = `${element.scrollHeight}px`;
  };

  // 使用 FilePreview 组件的方法
  const formatSourceTitle = (title) => {
    if (!title) return '';
    // 只移除"来源文件："前缀，保留文件名中的所有字符（包括前导下划线）
    return title.replace('来源文件：', '');
  };

  const hasValidSources = (sources) => {
    if (!sources || !Array.isArray(sources) || sources.length === 0) {
      return false;
    }
    return sources.some(source => 
      source && source.title && source.title.trim() !== ''
    );
  };

  const getUniqueSources = (sources) => {
    if (!hasValidSources(sources)) return [];
    
    const uniqueMap = new Map();
    sources.forEach(source => {
      if (source && source.title && source.file_source) {
        if (!uniqueMap.has(source.file_source)) {
          uniqueMap.set(source.file_source, { ...source });
        }
      }
    });
    
    return Array.from(uniqueMap.values());
  };

  // 暴露方法给父组件
  defineExpose({
    talkInfo,
    inputValue,
    loading,
    handleSubmit,
    abortResponse, // 暴露中止方法
    isAborting, // 暴露中止状态
    forceRender, // 暴露强制渲染方法
    setChatType, // 暴露设置聊天类型方法
    setHistorySession: (sessionData, sessionId) => {
      // 设置正在加载历史会话标志
      isLoadingHistory.value = true;
      
      // 如果有传入会话数据且包含消息
      if (sessionData && Array.isArray(sessionData)) {
        // 格式化会话消息，确保包含需要的所有字段
        const formattedMessages = sessionData.map(msg => {
          const formattedMsg = {
            id: msg.id || Date.now(),
            type: msg.role === 'user' ? 'user' : 'ai',
            info: msg.content,
            sources: msg.sources || [],
            graph_data: msg.graph_data,  // 保留graph_data
            qa_pair_data: msg.qa_pair_data,  // 保留qa_pair_data
            isTyping: false,
            isEditing: false,
            editingInfo: '',
            isUsingLLM: false,
            timestamp: msg.timestamp
          };

          // 如果qa_pair_data是字符串，尝试解析为对象
          if (formattedMsg.qa_pair_data && typeof formattedMsg.qa_pair_data === 'string') {
            try {
              formattedMsg.qa_pair_data = JSON.parse(formattedMsg.qa_pair_data);
            } catch (error) {
              console.error('解析历史记录中的qa_pair_data失败:', error);
              formattedMsg.qa_pair_data = null;
            }
          }

          // 如果graph_data是字符串，尝试解析为对象
          if (formattedMsg.graph_data && typeof formattedMsg.graph_data === 'string') {
            try {
              formattedMsg.graph_data = JSON.parse(formattedMsg.graph_data);
            } catch (error) {
              console.error('解析历史记录中的graph_data失败:', error);
              formattedMsg.graph_data = null;
            }
          }

          return formattedMsg;
        });
        
        // 设置格式化后的消息
        talkInfo.value = formattedMessages;
        
        // 关键修复: 标记为非新对话，这样发送消息时会继续会话而不是创建新的
        isNewChat.value = false;
        
        // 如果有会话ID，设置当前会话ID
        if (sessionId) {
          currentSessionId.value = sessionId;
        }
        
        // 记录当前选中的知识库，防止知识库切换时清空会话
        currentSessionKb.value = props.selectedKb || '';
        
        // 强制重新渲染并滚动到底部
        forceRender();

        // 确保Vue能够检测到qa_pair_data的变化
        nextTick(() => {
          // 再次强制渲染以确保问答对数据被正确处理
          forceRender();
          scrollToBottom();
        });
      }
      
      // 延迟重置加载标志，确保不会触发selectedKb的监听器
      setTimeout(() => {
        isLoadingHistory.value = false;
      }, 500);
    },
    scrollToBottom: () => {
      // 先强制更新渲染key
      forceRender();
      
      // 滚动到消息列表底部，并增加重试机制确保UI更新后执行
      const doScroll = (retryCount = 0) => {
        nextTick(() => {
          if (talkWindow.value) {
            talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
            
            // 确认是否真的滚动到底部，如果没有且未超过重试次数，则重试
            if (talkWindow.value.scrollHeight - talkWindow.value.scrollTop > talkWindow.value.clientHeight + 10 && retryCount < 5) {
              // console.log('滚动尚未到底部，重试...', retryCount + 1);
              setTimeout(() => doScroll(retryCount + 1), 100 * (retryCount + 1)); // 指数退避
            }
          }
        });
      };
      
      // 先立即滚动一次，然后再次尝试（通常在DOM更新后）
      if (talkWindow.value) {
        talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
      }
      
      // 延迟执行，确保DOM已更新
      setTimeout(() => doScroll(), 50);
    }
  });

  // 修改 handleSourceClick 方法
  const handleSourceClick = (source, messageIndex) => {
    // console.log('点击引用源:', source);
    // console.log('原始文件路径:', source.file_source);

    // 关闭其他预览(如果已打开)
    if (showQAPairView.value) {
      closeQAPairView();
    }
    if (showGraphView.value) {
      closeGraphView();
    }

    showFilePreview.value = true;
    // 通知父组件预览状态变化
    emit('preview-change', true);
    
    // 重置高亮计数
    currentHighlightIndex.value = 0;
    totalHighlights.value = 0;
    hasHighlights.value = false;
    
    nextTick(() => {
      if (filePreviewRef.value) {
        // 如果提供了消息索引，直接使用该索引查找对应的消息
        if (messageIndex !== undefined && talkInfo.value[messageIndex]) {
          const parentMessage = talkInfo.value[messageIndex];
          // console.log('直接使用提供的消息索引找到父消息:', messageIndex);
          
          // 为点击的source添加sourcesCollection属性，包含所有来自同一父消息中相同file_source的sources
          const enrichedSource = {
            ...source,
            parentMessageIndex: messageIndex,  // 记录来源消息索引
            sourcesCollection: parentMessage.sources.filter(s => 
              s.file_source === source.file_source
            )
          };
          
          // console.log('传递扩展后的引用源，完整文件路径:', enrichedSource.file_source);
          filePreviewRef.value.handleSourceClick(enrichedSource);
        }
        // 如果没有提供消息索引，则尝试查找匹配的父消息
        else {
          const parentMessageIndex = findParentMessageIndex(source);
          
          if (parentMessageIndex !== -1) {
            const parentMessage = talkInfo.value[parentMessageIndex];
            // console.log('通过搜索找到包含此引用的父消息:', parentMessageIndex);
            
            const enrichedSource = {
              ...source,
              parentMessageIndex: parentMessageIndex,  // 记录来源消息索引
              sourcesCollection: parentMessage.sources.filter(s => 
                s.file_source === source.file_source
              )
            };
            
            // console.log('传递扩展后的引用源，完整文件路径:', enrichedSource.file_source);
            filePreviewRef.value.handleSourceClick(enrichedSource);
          } else {
            // 如果找不到，退回到原来的逻辑
            // console.log('找不到父消息，使用单个引用源，完整文件路径:', source.file_source);
            filePreviewRef.value.handleSourceClick(source);
          }
        }
        
        // 等待文档加载和高亮处理完成后，更新高亮计数
        setTimeout(() => {
          updateHighlightStats();
        }, 3000); // 增加等待时间，确保高亮处理完成
      } else {
        // console.error('filePreviewRef 未初始化');
        ElMessage.warning('无法打开文件预览');
      }
    });
  };

  // 通过比较引用来查找父消息的索引
  const findParentMessageIndex = (clickedSource) => {
    // 遍历所有消息，找到包含完全匹配的source的那个消息
    for (let i = talkInfo.value.length - 1; i >= 0; i--) {
      const msg = talkInfo.value[i];
      if (msg.sources && Array.isArray(msg.sources)) {
        // 寻找精确匹配的源（通过内容和文件路径匹配）
        const hasExactSource = msg.sources.some(s => 
          s.file_source === clickedSource.file_source && 
          s.content === clickedSource.content
        );
        
        if (hasExactSource) {
          return i;
        }
      }
    }
    return -1;
  };

  // 激活的文件index
  const activeFileUrl = ref('');

  // 配置 marked
  marked.setOptions({
    highlight: function (code, lang) {
      console.log('高亮代码:', { code: code.substring(0, 50), lang });
      if (lang && hljs.getLanguage(lang)) {
        try {
          const result = hljs.highlight(code, { language: lang }).value;
          console.log('高亮结果 (指定语言):', lang, result.substring(0, 100));
          return result;
        } catch (e) {
          console.error('代码高亮错误:', e);
        }
      }
      const autoResult = hljs.highlightAuto(code);
      console.log('自动高亮结果:', autoResult.language, autoResult.value.substring(0, 100));

      // 如果自动检测到语言，我们需要在后续处理中添加data-language属性
      if (autoResult.language) {
        // 这里我们返回带有语言信息的结果，稍后在DOM中处理
        return `<span data-detected-language="${autoResult.language}">${autoResult.value}</span>`;
      }

      return autoResult.value;
    },
    breaks: true, // 支持 GitHub 风格的换行
    gfm: true, // 启用 GitHub 风格的 Markdown
    langPrefix: 'language-', // 设置语言前缀
  });

  // 添加 Markdown 渲染函数
  const renderMarkdown = (text) => {
    if (!text) return '';
    try {
      const html = marked(text);
      console.log('Markdown渲染结果:', html.substring(0, 200));

      // 延迟执行代码高亮和语言标签添加
      nextTick(() => {
        setTimeout(() => {
          // 处理自动检测的语言标签
          document.querySelectorAll('pre code span[data-detected-language]').forEach((span) => {
            const language = span.getAttribute('data-detected-language');
            const codeBlock = span.parentElement;
            if (codeBlock && codeBlock.tagName === 'CODE') {
              codeBlock.setAttribute('data-language', language);
              console.log('设置检测到的语言:', language);
              // 移除包装的span，保留内容
              codeBlock.innerHTML = span.innerHTML;
              codeBlock.classList.add('hljs');
            }
          });

          // 手动高亮未处理的代码块
          document.querySelectorAll('pre code:not(.hljs)').forEach((block) => {
            console.log('发现未高亮的代码块，手动高亮:', block);
            hljs.highlightElement(block);
          });

          // 为代码块添加复制按钮
          addCopyButtonsToCodeBlocks();
        }, 50);
      });

      return html;
    } catch (e) {
      console.error('Markdown 渲染错误:', e);
      return text;
    }
  };

  // 添加处理深度思考内容的方法
  const hasThinkingContent = (text) => {
    return text && (text.includes('<think>') || text.includes('</think>'));
  };

  // 检查是否正在思考中
  const isThinkingInProgress = (text) => {
    if (!text) return false;

    // 计算 <think> 和 </think> 的数量
    const thinkStartCount = (text.match(/<think>/g) || []).length;
    const thinkEndCount = (text.match(/<\/think>/g) || []).length;

    // 如果开始标签多于结束标签，说明还在思考中
    return thinkStartCount > thinkEndCount;
  };

  // 思考展开状态管理
  const thinkingExpandedMap = ref(new Map());

  // 获取思考展开状态
  const getThinkingExpanded = (item) => {
    // 如果正在思考中，自动展开
    if (isThinkingInProgress(item.info)) {
      return true;
    }

    // 如果AI正在输入中（实时回复），且包含思考内容，则保持展开
    if (item.isTyping && hasThinkingContent(item.info)) {
      return true;
    }

    const key = item.id || item.timestamp || JSON.stringify(item);
    const manualState = thinkingExpandedMap.value.get(key);

    // 如果用户手动设置过状态，使用用户设置的状态
    if (manualState !== undefined) {
      return manualState;
    }

    // 检查是否为刚完成的AI回复
    const allMessages = formattedTalkInfo.value;
    const currentIndex = allMessages.findIndex(msg =>
      (msg.id === item.id) || (msg.timestamp === item.timestamp)
    );

    // 如果是最后一条AI消息，且包含完整的思考内容，则默认展开
    const isLastAiMessage = currentIndex === allMessages.length - 1 && item.type === 'ai';
    const hasCompleteThinking = item.info.includes('</think>');

    // 刚完成的AI回复默认展开，历史记录默认折叠
    return isLastAiMessage && hasCompleteThinking;
  };

  // 获取思考标题
  const getThinkingTitle = (text) => {
    if (isThinkingInProgress(text)) {
      return '深度思考中...';
    }
    return '深度思考过程';
  };

  // 获取思考副标题
  const getThinkingSubtitle = (text) => {
    if (isThinkingInProgress(text)) {
      return 'AI正在进行深度推理分析';
    }
    return '点击查看AI的思维推理过程';
  };

  // 获取进程文本
  const getProcessText = (text) => {
    const blocks = extractThinkingBlocks(text);
    const completedCount = blocks.filter(block => block.isComplete).length;
    const totalCount = blocks.length;

    if (isThinkingInProgress(text)) {
      return `思维链推理中... (${completedCount}/${totalCount})`;
    }
    return `思维链推理完成 (${totalCount}个步骤)`;
  };

  // 获取思考步骤指示器
  const getThinkingSteps = (text) => {
    const blocks = extractThinkingBlocks(text);
    const isInProgress = isThinkingInProgress(text);

    // 至少显示3个步骤指示器
    const minSteps = 3;
    const actualSteps = Math.max(blocks.length, minSteps);

    const steps = [];
    for (let i = 0; i < actualSteps; i++) {
      const block = blocks[i];
      steps.push({
        isComplete: block ? block.isComplete : false,
        isCurrent: block ? !block.isComplete && isInProgress : false,
        index: i
      });
    }

    return steps;
  };

  // 切换思考展开状态
  const toggleThinking = (item) => {
    // 如果正在思考中，不允许手动折叠
    if (isThinkingInProgress(item.info)) {
      return;
    }

    const key = item.id || item.timestamp || JSON.stringify(item);
    const currentState = thinkingExpandedMap.value.get(key) || false;
    thinkingExpandedMap.value.set(key, !currentState);

    // 强制触发响应式更新
    thinkingExpandedMap.value = new Map(thinkingExpandedMap.value);
  };

  const renderThinkingContent = (text) => {
    if (!text) return '';

    let thinkingText = '';

    // 提取所有思考内容
    const thinkingBlocks = extractThinkingBlocks(text);

    if (thinkingBlocks.length > 0) {
      // 如果有完整的思考块，显示所有思考内容
      thinkingText = thinkingBlocks.map((block, index) => {
        if (block.isComplete) {
          return `### 思考步骤 ${index + 1}\n\n${block.content}`;
        } else {
          return `### 思考步骤 ${index + 1} (进行中)\n\n${block.content}\n\n*正在深度思考中...*`;
        }
      }).join('\n\n---\n\n');
    }

    const html = marked(thinkingText);

    // 延迟执行代码高亮和添加复制按钮
    nextTick(() => {
      setTimeout(() => {
        document.querySelectorAll('pre code:not(.hljs)').forEach((block) => {
          hljs.highlightElement(block);
        });
        addCopyButtonsToCodeBlocks();
      }, 50);
    });

    return html;
  };

  // 提取思考块的辅助函数
  const extractThinkingBlocks = (text) => {
    const blocks = [];
    let currentPos = 0;
    let blockIndex = 0;

    while (currentPos < text.length) {
      const thinkStart = text.indexOf('<think>', currentPos);
      if (thinkStart === -1) break;

      const thinkEnd = text.indexOf('</think>', thinkStart);

      if (thinkEnd === -1) {
        // 未完成的思考块
        const content = text.substring(thinkStart + 7); // 7 是 '<think>' 的长度
        blocks.push({
          content: content.trim(),
          isComplete: false,
          index: blockIndex++
        });
        break;
      } else {
        // 完整的思考块
        const content = text.substring(thinkStart + 7, thinkEnd);
        blocks.push({
          content: content.trim(),
          isComplete: true,
          index: blockIndex++
        });
        currentPos = thinkEnd + 8; // 8 是 '</think>' 的长度
      }
    }

    return blocks;
  };

  const renderFinalContent = (text) => {
    if (!text) return '';

    // 如果还在思考中，不显示最终内容
    if (isThinkingInProgress(text)) {
      return '';
    }

    // 移除所有思考标签，获取最终内容
    let finalText = text;

    // 移除所有 <think>...</think> 块
    finalText = finalText.replace(/<think>[\s\S]*?<\/think>/g, '');

    // 清理多余的空白
    finalText = finalText.trim();

    if (!finalText) return '';

    const html = marked(finalText);

    // 延迟执行代码高亮和添加复制按钮
    nextTick(() => {
      setTimeout(() => {
        document.querySelectorAll('pre code:not(.hljs)').forEach((block) => {
          hljs.highlightElement(block);
        });
        addCopyButtonsToCodeBlocks();
      }, 50);
    });

    return html;
  };

  // 添加输入法状态追踪
  const isComposing = ref(false);

  // 处理输入法开始输入
  const handleCompositionStart = () => {
    isComposing.value = true;
  };

  // 处理输入法结束输入
  const handleCompositionEnd = () => {
    isComposing.value = false;
  };

  // 处理回车键按下
  const handleEnterPress = (e) => {
    if (!isComposing.value) {
      handleSubmit();
    }
  };

  const handleUseLLM = async (item) => {
    if (item.isUsingLLM || loading.value) return;
    
    // 将 aiMessage 变量声明移到 try 块外部
    let aiMessage;
    
    try {
      item.isUsingLLM = true;
      loading.value = true;
      
      // 获取原始问题
      const originalQuestion = findUserQuestion(item);
      if (!originalQuestion) {
        throw new Error('未找到原始问题');
      }

      // 调用普通聊天接口
      const response = await chatStream({
        prompt: originalQuestion,
        new_chat: false,
        session_id: currentSessionId.value
      });

      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
      }

      // 创建新的 AI 消息
      aiMessage = {
        id: talkInfo.value.length + 1,
        info: '',
        type: 'ai',
        sources: [],
        isTyping: true,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: false,
        fromLLM: true  // 添加标记表示这是来自大模型的回答
      };
      talkInfo.value.push(aiMessage);

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;

          if (line.startsWith('data:')) {
            const data = line.slice(5).trim();
            if (!data || data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              if (parsed.code === 1) {
                aiMessage.info += parsed.data.content || '';
                if (parsed.data.session_id) {
                  currentSessionId.value = parsed.data.session_id;
                }
                talkInfo.value = [...talkInfo.value];
              }
            } catch (e) {
              console.error('解析响应数据失败:', e);
              console.error('原始数据:', data);
            }
          }
        }
      }
    } catch (error) {
      ElMessage.error(error.message || '使用大模型回答失败');
    } finally {
      item.isUsingLLM = false;
      loading.value = false;
      if (aiMessage) {
        aiMessage.isTyping = false;
        talkInfo.value = [...talkInfo.value];
      }
    }
  };

  // 添加查找原始问题的方法
  const findUserQuestion = (aiMessage) => {
    const aiIndex = talkInfo.value.findIndex(item => item.id === aiMessage.id);
    if (aiIndex > 0) {
      const userMessage = talkInfo.value[aiIndex - 1];
      if (userMessage.type === 'user') {
        return userMessage.info;
      }
    }
    return null;
  };

  // 修改关闭预览方法
  const closePreview = () => {
    // console.log('关闭预览按钮被点击');
    showFilePreview.value = false;
    // 通知父组件预览状态变化
    emit('preview-change', false);
    
    // 添加延迟确保状态更新
    setTimeout(() => {
      if (showFilePreview.value === true) {
        // console.log('强制关闭预览');
        showFilePreview.value = false;
        emit('preview-change', false);
      }
    }, 100);
  };

  // 添加跳转到高亮位置的方法
  const scrollToHighlight = (direction) => {
    if (filePreviewRef.value && showFilePreview.value) {
      const result = filePreviewRef.value.scrollToHighlight(direction);
      // 更新高亮计数
      updateHighlightStats();
      
      if (!result && hasHighlights.value) {
        // 如果导航失败但应该有高亮，尝试等待并重试
        setTimeout(() => {
          updateHighlightStats();
          if (hasHighlights.value) {
            filePreviewRef.value.scrollToHighlight(direction);
          }
        }, 500);
      }
    } else {
      ElMessage.warning('请先打开文件预览');
    }
  };
  
  // 添加更新高亮统计信息的方法
  const updateHighlightStats = () => {
    if (filePreviewRef.value) {
      const stats = filePreviewRef.value.getHighlightStats();
      // console.log('更新高亮统计:', stats);
      currentHighlightIndex.value = stats.currentIndex;
      totalHighlights.value = stats.total;
      hasHighlights.value = stats.total > 0;
    } else {
      hasHighlights.value = false;
    }
  };

  // 监听showFilePreview变化通知父组件
  watch(showFilePreview, (newVal) => {
    emit('preview-change', newVal);
    
    // 如果关闭预览，重置高亮计数
    if (!newVal) {
      currentHighlightIndex.value = 0;
      totalHighlights.value = 0;
      hasHighlights.value = false;
    }
  });

  // 在 script setup 部分添加新的方法
  const queryLLM = async (item) => {
    if (!item || item.isQuerying) return;
    
    try {
      item.isQuerying = true;
      const lastUserMessage = talkInfo.value
        .slice(0, talkInfo.value.indexOf(item))
        .reverse()
        .find(msg => msg.type === 'user');

      if (!lastUserMessage) {
        ElMessage.warning('未找到相关问题');
        return;
      }

      // 调用普通聊天接口
      const response = await chatStream({
        prompt: lastUserMessage.info,
        new_chat: false,
        session_id: currentSessionId.value
      });

      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
      }

      // 创建新的AI消息
      const newAiMessage = {
        id: talkInfo.value.length + 1,
        info: '',
        type: 'ai',
        sources: [],
        isTyping: true,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: true
      };
      talkInfo.value.push(newAiMessage);

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data:')) {
            const data = line.slice(5).trim();
            if (!data || data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              if (parsed.code === 1) {
                newAiMessage.info += parsed.data.content || '';
                if (parsed.data.session_id) {
                  currentSessionId.value = parsed.data.session_id;
                  isNewChat.value = false;
                }
                talkInfo.value = [...talkInfo.value];
              }
            } catch (e) {
              console.error('解析响应数据失败:', e);
              console.error('原始数据:', data);
            }
          }
        }
      }

      newAiMessage.isTyping = false;
      talkInfo.value = [...talkInfo.value];
    } catch (error) {
      ElMessage.error(error.message || '请求失败');
      // console.error('调用大模型失败:', error);
    } finally {
      item.isQuerying = false;
    }
  };

  // 检查消息是否包含图谱数据
  const hasGraphData = (item) => {
    if (!item) return false;
    
    // 检查graph_data是否存在且有效
    if (item.graph_data) {
      // 如果是字符串类型，检查是否为非空字符串
      if (typeof item.graph_data === 'string') {
        return item.graph_data.trim().length > 0;
      }
      // 如果是对象类型，直接返回true
      else if (typeof item.graph_data === 'object') {
        return true;
      }
    }
    
    return false;
  };

  // 处理点击图谱按钮的事件
  const handleGraphClick = (item) => {
    try {
      // 检查图谱数据
      if (hasGraphData(item)) {
        let graphData;
        
        // 如果是字符串，尝试解析
        if (typeof item.graph_data === 'string') {
          try {
            graphData = JSON.parse(item.graph_data);
          } catch (parseError) {
            console.error('JSON解析错误:', parseError);
            ElMessage.error('解析图谱数据失败');
            return;
          }
        } 
        // 如果已经是对象，直接使用
        else if (typeof item.graph_data === 'object') {
          graphData = item.graph_data;
        }
        
        // 检查图谱数据是否有节点和边
        if (graphData && graphData.nodes && graphData.nodes.length > 0) {
          showGraphView.value = true;
          currentGraphData.value = graphData;
          activeGraphMessageId.value = item.id;
          
          // 关闭其他预览(如果已打开)
          if (showFilePreview.value) {
            closePreview();
          }
          if (showQAPairView.value) {
            closeQAPairView();
          }
          
          // 通知父组件预览状态变化
          emit('preview-change', true);
        } else {
          ElMessage.warning('图谱数据不包含节点信息');
        }
      } else {
        ElMessage.warning('没有可用的图谱数据');
      }
    } catch (error) {
      console.error('处理图谱点击时发生错误:', error);
      ElMessage.error('解析图谱数据失败');
    }
  };

  // 关闭图谱视图
  const closeGraphView = () => {
    showGraphView.value = false;
    currentGraphData.value = null;
    activeGraphMessageId.value = null;

    // 通知父组件预览状态变化
    emit('preview-change', false);
  };

  // 检查消息是否包含问答对数据
  const hasQAPairData = (item) => {
    if (!item) return false;

    // 检查qa_pair_data是否存在且有效
    if (item.qa_pair_data) {
      // 如果是字符串类型，检查是否为非空字符串
      if (typeof item.qa_pair_data === 'string') {
        return item.qa_pair_data.trim().length > 0;
      }
      // 如果是对象类型，直接返回true（与hasGraphData保持一致）
      else if (typeof item.qa_pair_data === 'object') {
        return true;
      }
    }

    return false;
  };

  // 获取问答对数量
  const getQAPairCount = (item) => {
    if (!hasQAPairData(item)) return 0;

    try {
      let qaPairData;

      if (typeof item.qa_pair_data === 'string') {
        qaPairData = JSON.parse(item.qa_pair_data);
      } else {
        qaPairData = item.qa_pair_data;
      }

      return qaPairData.nodes ? qaPairData.nodes.length : 0;
    } catch (error) {
      console.error('解析问答对数据失败:', error);
      return 0;
    }
  };

  // 处理点击问答对按钮的事件
  const handleQAPairClick = (item) => {
    try {
      // 检查问答对数据
      if (hasQAPairData(item)) {
        let qaPairData;

        // 如果是字符串，尝试解析
        if (typeof item.qa_pair_data === 'string') {
          try {
            qaPairData = JSON.parse(item.qa_pair_data);
          } catch (parseError) {
            console.error('解析问答对数据失败:', parseError);
            ElMessage.error('问答对数据格式错误');
            return;
          }
        } else {
          // 如果已经是对象，直接使用
          qaPairData = item.qa_pair_data;
        }

        // 验证解析后的数据结构
        if (qaPairData && qaPairData.nodes && qaPairData.nodes.length > 0) {
          showQAPairView.value = true;
          currentQAPairData.value = qaPairData;
          activeQAPairMessageId.value = item.id;

          // 关闭其他预览(如果已打开)
          if (showFilePreview.value) {
            closePreview();
          }
          if (showGraphView.value) {
            closeGraphView();
          }

          // 通知父组件预览状态变化
          emit('preview-change', true);
        } else {
          ElMessage.warning('问答对数据不包含节点信息');
        }
      } else {
        ElMessage.warning('没有可用的问答对数据');
      }
    } catch (error) {
      console.error('处理问答对点击时发生错误:', error);
      ElMessage.error('解析问答对数据失败');
    }
  };

  // 关闭问答对视图
  const closeQAPairView = () => {
    showQAPairView.value = false;
    currentQAPairData.value = null;
    activeQAPairMessageId.value = null;

    // 通知父组件预览状态变化
    emit('preview-change', false);
  };

  // 为代码块添加复制按钮
  const addCopyButtonsToCodeBlocks = () => {
    document.querySelectorAll('pre code').forEach((codeBlock) => {
      const preElement = codeBlock.parentElement;

      // 检查是否已经添加了复制按钮
      if (preElement.querySelector('.code-copy-btn')) {
        return;
      }

      // 创建复制按钮
      const copyBtn = document.createElement('button');
      copyBtn.className = 'code-copy-btn';
      copyBtn.innerHTML = `
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
        <span>复制</span>
      `;

      // 添加点击事件
      copyBtn.addEventListener('click', async () => {
        try {
          // 获取代码文本（去除HTML标签）
          const codeText = codeBlock.textContent || codeBlock.innerText;

          // 使用现代的 Clipboard API
          if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(codeText);
          } else {
            // 降级方案：使用传统的复制方法
            const textArea = document.createElement('textarea');
            textArea.value = codeText;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            document.execCommand('copy');
            textArea.remove();
          }

          // 显示复制成功状态
          copyBtn.classList.add('copied');
          copyBtn.innerHTML = `
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
            <span>已复制</span>
          `;

          // 2秒后恢复原状
          setTimeout(() => {
            copyBtn.classList.remove('copied');
            copyBtn.innerHTML = `
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
              <span>复制</span>
            `;
          }, 2000);

        } catch (err) {
          console.error('复制失败:', err);
          ElMessage.error('复制失败，请手动选择复制');
        }
      });

      // 将按钮添加到pre元素中
      preElement.appendChild(copyBtn);
    });
  };



  // 监听showGraphView变化通知父组件
  watch(showGraphView, (newVal) => {
    emit('preview-change', newVal);
  });

  // 监听showQAPairView变化通知父组件
  watch(showQAPairView, (newVal) => {
    emit('preview-change', newVal);
  });

  onMounted(() => {});
</script>

<style scoped>
.chat-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: transparent;
  overflow: hidden;
}

.chat-container.preview-active {
  /* 当预览激活时的距离调整 */
  /* 避免使用任何会导致主区域移位的样式 */
  transition: all 0.3s ease;
}

.chat-main-area {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* 只移动消息容器，不移动整个chat-main-area */
.preview-active .chat-main-area {
  /* 移除整个聊天区域的左移 */
  /* transform: translateX(-10%); */
  transition: transform 0.3s ease;
}

/* 只移动消息部分 */
.preview-active .messages-container {
  /* 预览激活时保持消息容器与普通状态一致，确保不变形 */
  width: 100%;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center; /* 保持居中对齐 */
  transform: translateX(-5%) translateY(0); /* 减小左移幅度，因为预览框变小了 */
  transition: transform 0.3s ease;
}

/* 文件预览覆盖层 - 修改位置到右上角 */
.file-preview-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 由35%增加到40%，利用左移的空间 */
  max-width: none; /* 移除最大宽度限制，让预览区域充分利用空间 */
  z-index: 5; /* 确保不会覆盖关闭对话按钮 */
  border-left: 2px solid #3f63a8;
  border-top: 2px solid #3f63a8;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease; /* 添加过渡效果 */
}

/* 问答对视图覆盖层 */
.qa-pair-view-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 与文件预览保持一致 */
  max-width: none;
  z-index: 5; /* 确保不会覆盖关闭对话按钮 */
  border-left: 2px solid #ffa500;
  border-top: 2px solid #ffa500;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(255, 165, 0, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 图谱视图覆盖层 */
.graph-view-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 与文件预览保持一致 */
  max-width: none;
  z-index: 5; /* 确保不会覆盖关闭对话按钮 */
  border-left: 2px solid #13fff3;
  border-top: 2px solid #13fff3;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(19, 255, 243, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 删除Vue过渡钩子，它们可能会干扰关闭操作 */

.file-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  height: 36px; /* 减小高度 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px; /* 减小内边距 */
  background-color: rgba(9, 39, 98, 0.9);
  border-bottom: 1px solid #3f63a8;
}

.preview-title {
  font-weight: bold;
  color: #13fff3;
  font-size: 14px; /* 减小字体 */
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-title::before {
  content: '📄';
  font-size: 14px; /* 减小图标 */
}

.close-preview-btn {
  cursor: pointer;
  padding: 4px 8px; /* 减小内边距 */
  color: white;
  background-color: rgba(15, 45, 90, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 3px; /* 减小间距 */
  font-size: 13px; /* 减小字体 */
  transition: all 0.2s;
  z-index: 1010; /* 确保按钮在最上层 */
}

.close-preview-btn:hover {
  background-color: rgba(19, 255, 243, 0.15);
  color: #13fff3;
  border-color: #13fff3;
}

.preview-active .talk-window {
  /* 预览激活时确保聊天区域不被遮挡，保持原样 */
  width: 100%;
}

.talk-window {
  width: 100%;
  height: calc(100% - 20px);
  margin-bottom: 100px; /* 从70px增加到100px，增加与输入框的间距 */
  overflow-y: auto !important; /* 强制确保可以滚动 */
  overflow-x: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  transition: transform 0.3s ease; /* 只过渡transform属性，避免其他属性变化 */
  /* 确保在任何状态下都可以滚动 */
  pointer-events: auto !important;
  touch-action: pan-y !important; /* 允许垂直滚动 */
}

.empty-chat {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding-top: 80px;
  transform: translateX(0) translateY(0); /* 默认位置，明确指定Y轴不变 */
  transition: transform 0.3s ease; /* 只过渡transform属性 */
}

.preview-active .empty-chat {
  transform: translateX(-5%) translateY(0); /* 同步调整空状态左移量 */
  transition: transform 0.3s ease;
}

.welcome-message {
  text-align: center;
  color: #fff;
  max-width: 600px;
  padding: 30px;
  background-color: rgba(9, 39, 98, 0.7);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.welcome-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.welcome-message h2 {
  font-size: 28px;
  margin-bottom: 15px;
}

.welcome-message p {
  font-size: 16px;
  opacity: 0.8;
}

/* 消息容器 */
.messages-container {
  width: 100%;
  padding: 20px 0 40px 0; /* 增加底部内边距到40px */
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center; /* 正常情况下居中对齐 */
  transform: translateX(0) translateY(0); /* 默认位置，明确指定Y轴不变 */
  transition: transform 0.3s ease; /* 只过渡transform属性 */
}

/* 消息行样式 - 正常情况 */
.message-row {
  width: 100%;
  max-width: 800px; /* 限制最大宽度，确保居中效果 */
  padding: 0 20px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

/* 最后一条消息额外的底部边距 */
.message-row:last-child {
  margin-bottom: 120px; /* 增加底部边距，避免与输入框重叠 */
}

/* 消息行样式 - 预览激活时修改 */
.preview-active .message-row {
  /* 保持与非预览状态相同 */
  width: 100%;
  max-width: 800px; /* 使用与正常状态相同的最大宽度 */
  margin-left: auto;
  margin-right: auto;
  /* 重置内边距，与普通状态保持一致 */
  padding: 0 20px;
  /* 添加过渡效果 */
  transition: transform 0.3s ease;
}

.message-row.user-message {
  justify-content: flex-end;
}

.message-row.ai-message {
  justify-content: flex-start;
}

/* 确保用户消息在预览模式下仍然保持右对齐 */
.preview-active .message-row.user-message {
  justify-content: flex-end;
}

/* 头像样式 */
.message-avatar {
  width: 36px;
  height: 36px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  object-fit: cover;
}

.ai-message .message-avatar {
  margin-right: 12px;
}

.user-message .message-avatar {
  margin-left: 12px;
  order: 2;
}

/* 气泡样式 */
.message-bubble {
  max-width: 75%;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* 确保气泡内容不会因为容器变化而收缩 */
  flex-shrink: 0;
  min-width: 0;
}

.ai-message .message-bubble {
  background-color: rgba(9, 39, 98, 0.8);
  color: #fff;
  border-radius: 16px;
  border-top-left-radius: 4px;
}

.user-message .message-bubble {
  background-color: rgba(15, 75, 165, 0.8);
  color: #fff;
  border-radius: 16px;
  border-top-right-radius: 4px;
}

/* 确保预览模式下的气泡宽度保持一致 */
.preview-active .message-bubble {
  max-width: 75%; /* 与普通状态保持一致 */
  width: auto; /* 由内容决定宽度 */
  min-width: min-content; /* 确保气泡至少有足够的宽度 */
}

/* 消息内容样式 */
.message-content {
  width: 100%;
  min-width: 0; /* 确保内容不会影响布局 */
}

/* 确保预览模式下内容宽度不变 */
.preview-active .message-content {
  width: 100%;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sender-name {
  font-weight: bold;
  opacity: 0.8;
}

.edit-icon {
  cursor: pointer;
  font-size: 14px;
  padding: 3px;
  border-radius: 4px;
  opacity: 0.7;
  transition: all 0.2s;
}

.edit-icon:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.message-body {
  line-height: 1.6;
}

/* 参考来源样式 */
.sources {
  margin-top: 16px;
  padding: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  background-color: rgba(9, 39, 98, 0.5);
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.source-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #13fff3;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.source-list {
  margin: 0;
  padding-left: 10px;
  list-style-type: none;
}

.source-item {
  margin-bottom: 8px;
  padding: 6px 10px;
  cursor: pointer;
  color: #a8c7ff;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  border-radius: 4px;
  background-color: rgba(9, 39, 98, 0.3);
  position: relative;
}

.source-item:hover {
  background-color: rgba(19, 255, 243, 0.15);
}

.source-item.active-source {
  background-color: rgba(19, 255, 243, 0.3);
  border-left: 3px solid #13fff3;
}

/* 知识图谱来源项特殊样式 */
.graph-source-item {
  background-color: rgba(9, 39, 98, 0.3);
  border-left: 3px solid transparent;
}

.graph-source-item:hover {
  background-color: rgba(19, 255, 243, 0.15);
  border-left: 3px solid #13fff3;
}

.graph-source-item.active-source {
  background-color: rgba(19, 255, 243, 0.3);
  border-left: 3px solid #13fff3;
}

.graph-source-item .source-icon {
  color: #13fff3;
}

/* 问答对来源项特殊样式 */
.qa-pair-source-item {
  background-color: rgba(9, 39, 98, 0.3);
  border-left: 3px solid transparent;
}

.qa-pair-source-item:hover {
  background-color: rgba(255, 165, 0, 0.15);
  border-left: 3px solid #ffa500;
}

.qa-pair-source-item.active-source {
  background-color: rgba(255, 165, 0, 0.3);
  border-left: 3px solid #ffa500;
}

.qa-pair-source-item .source-icon {
  color: #ffa500;
}

.source-icon {
  margin-right: 8px;
  font-size: 16px;
}

.source-text {
  text-decoration: underline;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.view-indicator {
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-left: 8px;
  color: #13fff3;
}

.source-item:hover .view-indicator {
  opacity: 1;
}

.source-item:hover, .active-source {
  background-color: rgba(19, 255, 243, 0.1);
  color: #13fff3;
}

.active-source .view-indicator {
  opacity: 1;
}

/* 跳转到高亮位置按钮样式 */
.highlight-jump-btn {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.highlight-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(9, 39, 98, 0.6);
  padding: 6px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.highlight-counter {
  color: #13fff3;
  font-size: 14px;
  min-width: 60px;
  text-align: center;
  font-weight: bold;
}

.highlight-jump-btn .el-button {
  background: linear-gradient(135deg, #3f63a8, #5c8df6);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.highlight-jump-btn .el-button:hover:not([disabled]) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(63, 99, 168, 0.3);
  background: linear-gradient(135deg, #4e78cf, #6f9dff);
}

.highlight-jump-btn .el-button[disabled] {
  opacity: 0.6;
  background: linear-gradient(135deg, #2a446f, #3a5ba0);
  cursor: not-allowed;
}

.highlight-jump-btn .el-button i {
  font-size: 14px;
}

/* 自定义滚动条 */
.talk-window::-webkit-scrollbar {
  width: 8px;
}

.talk-window::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.talk-window::-webkit-scrollbar-thumb {
  background: rgba(63, 99, 168, 0.6);
  border-radius: 4px;
}

.talk-window::-webkit-scrollbar-thumb:hover {
  background: rgba(93, 130, 201, 0.8);
}

/* 大模型回答相关样式 */
.use-llm-container {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.use-llm-divider {
  display: flex;
  align-items: center;
  text-align: center;
  color: #8e9dbb;
  font-size: 13px;
  margin: 8px 0;
}

.use-llm-divider::before,
.use-llm-divider::after {
  content: '';
  flex: 1;
  border-top: 1px solid #3f63a8;
  margin: 0 8px;
}

.divider-text {
  padding: 0 10px;
  white-space: nowrap;
}

.use-llm-button {
  display: flex;
  justify-content: center;
}

.button-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 4px;
}

/* 编辑区域样式 */
.editing-area {
  width: 75%;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.edit-textarea {
  width: 100%;
  min-height: 180px;
  padding: 12px 16px;
  background-color: rgba(9, 39, 98, 0.8);
  border: none;
  border-radius: 16px;
  border-top-left-radius: 4px;
  color: #fff;
  font-size: inherit;
  line-height: 1.6;
  resize: none;
  margin-bottom: 5px;
  font-family: inherit;
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}

.editing-controls {
  display: flex;
  justify-content: flex-end;
  padding: 0 5px;
}

.save-button {
  background-color: rgba(63, 81, 181, 0.8) !important;
  border-color: rgb(63, 81, 181) !important;
  color: white !important;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
}

.save-button:hover {
  background-color: rgb(63, 81, 181) !important;
  border-color: rgb(79, 125, 243) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 添加滚动条样式 */
.edit-textarea::-webkit-scrollbar {
  width: 8px;
}

.edit-textarea::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.edit-textarea::-webkit-scrollbar-thumb {
  background: rgba(63, 81, 181, 0.6);
  border-radius: 4px;
}

.edit-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(63, 81, 181, 0.8);
}

/* 深度思考部分样式 */
.thinking-section {
  margin-bottom: 20px;
}

.thinking-container {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(168, 85, 247, 0.05));
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.thinking-container:hover {
  border-color: rgba(99, 102, 241, 0.4);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.1);
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.02);
}

.thinking-header:hover {
  background: rgba(99, 102, 241, 0.05);
}

.thinking-icon {
  position: relative;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brain-icon {
  width: 32px;
  height: 32px;
  color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  position: relative;
  z-index: 2;
}

.brain-icon svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
}

.brain-icon.thinking-active {
  animation: brain-pulse 1.5s ease-in-out infinite;
}

.brain-icon.thinking-active svg {
  color: #10b981;
}

@keyframes brain-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.thinking-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 2px solid #6366f1;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.thinking-pulse.active {
  opacity: 0.6;
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0;
  }
}

.thinking-title-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.thinking-title {
  color: #6366f1;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.thinking-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  margin: 0;
}

.thinking-toggle {
  width: 24px;
  height: 24px;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  transform: rotate(0deg);
}

.thinking-toggle.expanded {
  transform: rotate(180deg);
  color: #6366f1;
}

.thinking-toggle svg {
  width: 100%;
  height: 100%;
}

.thinking-content {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(0, 0, 0, 0.2);
}

.thinking-content.expanded {
  max-height: 9999px; /* 设置一个足够大的值，实际上不限制高度 */
  border-top: 1px solid rgba(99, 102, 241, 0.2);
}

.thinking-inner {
  padding: 20px;
  padding-bottom: 30px; /* 确保底部内容有足够空间 */
}

/* 为思维链内容添加滚动条样式 */
.thinking-content.expanded::-webkit-scrollbar {
  width: 6px;
}

.thinking-content.expanded::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.thinking-content.expanded::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 3px;
}

.thinking-content.expanded::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.7);
}

.thinking-process-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.process-indicator {
  display: flex;
  align-items: center;
  margin-right: 12px;
}

.step-indicator {
  display: flex;
  align-items: center;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.indicator-dot.active {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.indicator-dot.current {
  background: #f59e0b;
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
  animation: pulse-current 1.5s infinite;
}

@keyframes pulse-current {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.indicator-line {
  width: 16px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 4px;
}

.process-text {
  color: #10b981;
  font-size: 13px;
  font-weight: 500;
}

.thinking-markdown {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #6366f1;
}

.thinking-markdown h1,
.thinking-markdown h2,
.thinking-markdown h3,
.thinking-markdown h4,
.thinking-markdown h5,
.thinking-markdown h6 {
  color: #a855f7;
  margin-top: 16px;
  margin-bottom: 8px;
}

.thinking-markdown p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 12px;
}

.thinking-markdown code {
  background: rgba(99, 102, 241, 0.2);
  color: #c084fc;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
}

.thinking-markdown pre {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 12px 0;
}

.thinking-markdown pre code {
  background: none;
  color: #e2e8f0;
  padding: 0;
}

.thinking-markdown ul,
.thinking-markdown ol {
  color: rgba(255, 255, 255, 0.8);
  padding-left: 20px;
  margin-bottom: 12px;
}

.thinking-markdown li {
  margin-bottom: 4px;
}

.thinking-markdown blockquote {
  border-left: 4px solid #6366f1;
  background: rgba(99, 102, 241, 0.1);
  padding: 12px 16px;
  margin: 12px 0;
  border-radius: 0 6px 6px 0;
}

.thinking-markdown blockquote p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
}

/* 确保markdown内容不会在预览模式下变窄 */
.preview-active .markdown-body {
  width: 100%;
  min-width: 0;
}

/* 增强源文件引用容器样式，保持宽度 */
.preview-active .sources {
  width: 100%;
  min-width: 0;
}

/* 添加调用大模型按钮的样式 */
.llm-query-btn {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

.llm-query-btn .el-button {
  background: linear-gradient(135deg, #13fff3, #19aeff);
  border: none;
  color: #001e57;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.llm-query-btn .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(19, 255, 243, 0.3);
  background: linear-gradient(135deg, #00e6da, #0095ff);
}

.llm-query-btn .el-button i {
  font-size: 16px;
}

.llm-query-btn .el-button.is-loading {
  opacity: 0.8;
  transform: none;
}

/* 图谱预览相关样式 */
.graph-view-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 与文件预览相同大小 */
  max-width: none;
  z-index: 5;
  border-left: 2px solid #3f63a8;
  border-top: 2px solid #3f63a8;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 图谱标题和操作按钮样式 */
.graph-data {
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(19, 58, 138, 0.2);
  border-radius: 6px;
}

.graph-title {
  font-weight: bold;
  color: #13fff3;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.graph-action {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.view-graph-btn {
  background-color: #13a6ff !important;
  border: 1px solid #1396ee !important;
  color: white !important;
}

/* 回到底部按钮样式 */
.scroll-to-bottom-btn {
  position: fixed;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(99, 102, 241, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.scroll-to-bottom-btn:hover {
  background: rgba(99, 102, 241, 1);
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.scroll-btn-icon {
  width: 20px;
  height: 20px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-btn-icon svg {
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
}

.scroll-to-bottom-btn:hover .scroll-btn-icon svg {
  transform: translateY(1px);
}
</style>

<style>
  /* 添加 Markdown 样式 */
  .markdown-body {
    color: #fff;
    line-height: 1.6;
  }

  .markdown-body h1,
  .markdown-body h2,
  .markdown-body h3,
  .markdown-body h4,
  .markdown-body h5,
  .markdown-body h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  .markdown-body code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.5);
    border-radius: 6px;
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  }

  .markdown-body pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: rgba(27, 31, 35, 0.5);
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .markdown-body pre code {
    padding: 0;
    margin: 0;
    background-color: transparent;
  }

  /* 确保highlight.js样式能正确显示 */
  .markdown-body pre code.hljs,
  .markdown-body pre code[class*="language-"],
  .markdown-body pre code {
    display: block !important;
    overflow-x: auto !important;
    padding: 1em !important;
    background: #282c34 !important;
    color: #abb2bf !important;
    border-radius: 6px !important;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  /* 重置pre的padding，因为code已经有了 */
  .markdown-body pre {
    padding: 0 !important;
    background-color: transparent !important;
    margin: 16px 0 !important;
    position: relative !important;
  }

  /* 为代码块添加语言标签 */
  .markdown-body pre code[class*="language-"]::before {
    content: attr(class);
    position: absolute;
    top: 8px;
    right: 80px;
    background: rgba(0, 0, 0, 0.3);
    color: #abb2bf;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    text-transform: capitalize;
    z-index: 1;
  }

  /* 特殊处理常见语言的显示名称 */
  .markdown-body pre code.language-python::before {
    content: "Python";
  }

  .markdown-body pre code.language-javascript::before {
    content: "JavaScript";
  }

  .markdown-body pre code.language-java::before {
    content: "Java";
  }

  .markdown-body pre code.language-cpp::before {
    content: "C++";
  }

  .markdown-body pre code.language-c::before {
    content: "C";
  }

  .markdown-body pre code.language-html::before {
    content: "HTML";
  }

  .markdown-body pre code.language-css::before {
    content: "CSS";
  }

  .markdown-body pre code.language-sql::before {
    content: "SQL";
  }

  .markdown-body pre code.language-json::before {
    content: "JSON";
  }

  .markdown-body pre code.language-xml::before {
    content: "XML";
  }

  .markdown-body pre code.language-bash::before {
    content: "Bash";
  }

  .markdown-body pre code.language-shell::before {
    content: "Shell";
  }

  .markdown-body pre code.language-typescript::before {
    content: "TypeScript";
  }

  .markdown-body pre code.language-go::before {
    content: "Go";
  }

  .markdown-body pre code.language-rust::before {
    content: "Rust";
  }

  .markdown-body pre code.language-php::before {
    content: "PHP";
  }

  .markdown-body pre code.language-ruby::before {
    content: "Ruby";
  }

  .markdown-body pre code.language-swift::before {
    content: "Swift";
  }

  .markdown-body pre code.language-kotlin::before {
    content: "Kotlin";
  }

  .markdown-body pre code.language-dart::before {
    content: "Dart";
  }

  /* 为自动检测的语言添加标签 */
  .markdown-body pre code[data-language]::before {
    content: attr(data-language);
    position: absolute;
    top: 8px;
    right: 80px;
    background: rgba(0, 0, 0, 0.3);
    color: #abb2bf;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    text-transform: capitalize;
    z-index: 1;
  }

  /* 为没有语言标识的代码块添加通用标签 */
  .markdown-body pre code.hljs:not([class*="language-"]):not([data-language])::before {
    content: "Code";
    position: absolute;
    top: 8px;
    right: 80px;
    background: rgba(0, 0, 0, 0.3);
    color: #abb2bf;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    z-index: 1;
  }

  /* 代码块复制按钮样式 */
  .code-copy-btn {
    position: absolute;
    top: 8px;
    right: 12px;
    background: rgba(0, 0, 0, 0.3);
    color: #abb2bf;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    z-index: 2;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .code-copy-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
  }

  .code-copy-btn.copied {
    background: rgba(34, 197, 94, 0.3);
    color: #22c55e;
  }

  .code-copy-btn svg {
    width: 14px;
    height: 14px;
  }

  /* 确保代码块容器有足够的空间放置按钮 */
  .markdown-body pre {
    position: relative !important;
  }

  /* 覆盖可能冲突的样式 - 使用更强的选择器 */
  .markdown-body pre code .hljs-keyword,
  .markdown-body pre code .hljs-selector-tag,
  .markdown-body pre code .hljs-literal,
  .markdown-body pre code .hljs-section,
  .markdown-body pre code .hljs-link,
  .markdown-body .hljs-keyword,
  .markdown-body .hljs-selector-tag,
  .markdown-body .hljs-literal,
  .markdown-body .hljs-section,
  .markdown-body .hljs-link {
    color: #c678dd !important;
    font-weight: bold !important;
  }

  .markdown-body pre code .hljs-function .hljs-keyword,
  .markdown-body .hljs-function .hljs-keyword {
    color: #c678dd !important;
    font-weight: bold !important;
  }

  .markdown-body pre code .hljs-subst,
  .markdown-body .hljs-subst {
    color: #abb2bf !important;
  }

  .markdown-body pre code .hljs-string,
  .markdown-body pre code .hljs-title,
  .markdown-body pre code .hljs-name,
  .markdown-body pre code .hljs-attribute,
  .markdown-body pre code .hljs-symbol,
  .markdown-body pre code .hljs-bullet,
  .markdown-body pre code .hljs-addition,
  .markdown-body pre code .hljs-variable,
  .markdown-body pre code .hljs-template-tag,
  .markdown-body pre code .hljs-template-variable,
  .markdown-body .hljs-string,
  .markdown-body .hljs-title,
  .markdown-body .hljs-name,
  .markdown-body .hljs-attribute,
  .markdown-body .hljs-symbol,
  .markdown-body .hljs-bullet,
  .markdown-body .hljs-addition,
  .markdown-body .hljs-variable,
  .markdown-body .hljs-template-tag,
  .markdown-body .hljs-template-variable {
    color: #98c379 !important;
  }

  .markdown-body pre code .hljs-comment,
  .markdown-body pre code .hljs-quote,
  .markdown-body pre code .hljs-deletion,
  .markdown-body pre code .hljs-meta,
  .markdown-body .hljs-comment,
  .markdown-body .hljs-quote,
  .markdown-body .hljs-deletion,
  .markdown-body .hljs-meta {
    color: #5c6370 !important;
    font-style: italic !important;
  }

  .markdown-body pre code .hljs-number,
  .markdown-body pre code .hljs-regexp,
  .markdown-body .hljs-number,
  .markdown-body .hljs-regexp {
    color: #d19a66 !important;
  }

  .markdown-body pre code .hljs-built_in,
  .markdown-body pre code .hljs-builtin-name,
  .markdown-body pre code .hljs-class .hljs-title,
  .markdown-body .hljs-built_in,
  .markdown-body .hljs-builtin-name,
  .markdown-body .hljs-class .hljs-title {
    color: #e6c07b !important;
  }

  .markdown-body pre code .hljs-attr,
  .markdown-body pre code .hljs-type,
  .markdown-body .hljs-attr,
  .markdown-body .hljs-type {
    color: #e06c75 !important;
  }

  /* 特殊处理Python语法 */
  .markdown-body pre code .hljs-params,
  .markdown-body .hljs-params {
    color: #abb2bf !important;
  }

  .markdown-body pre code .hljs-function,
  .markdown-body .hljs-function {
    color: #61afef !important;
  }

  /* 强制应用代码高亮样式 - 最高优先级 */
  .markdown-body pre code,
  .markdown-body pre code.hljs,
  .markdown-body pre code[class*="language-"],
  .markdown-body pre code[class*="hljs-"] {
    background: #282c34 !important;
    color: #abb2bf !important;
    display: block !important;
    padding: 1em !important;
    border-radius: 6px !important;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    overflow-x: auto !important;
  }

  /* 确保所有语法高亮类都有正确的颜色 */
  .markdown-body pre code *[class*="hljs-"] {
    font-style: inherit !important;
    font-weight: inherit !important;
  }

  /* 强制应用关键字颜色 */
  .markdown-body pre code *[class*="hljs-keyword"],
  .markdown-body pre code *[class*="hljs-selector-tag"],
  .markdown-body pre code *[class*="hljs-literal"],
  .markdown-body pre code *[class*="hljs-section"],
  .markdown-body pre code *[class*="hljs-link"] {
    color: #c678dd !important;
    font-weight: bold !important;
  }

  /* 强制应用字符串颜色 */
  .markdown-body pre code *[class*="hljs-string"],
  .markdown-body pre code *[class*="hljs-title"],
  .markdown-body pre code *[class*="hljs-name"],
  .markdown-body pre code *[class*="hljs-attribute"],
  .markdown-body pre code *[class*="hljs-symbol"],
  .markdown-body pre code *[class*="hljs-bullet"],
  .markdown-body pre code *[class*="hljs-addition"],
  .markdown-body pre code *[class*="hljs-variable"],
  .markdown-body pre code *[class*="hljs-template-tag"],
  .markdown-body pre code *[class*="hljs-template-variable"] {
    color: #98c379 !important;
  }

  /* 强制应用注释颜色 */
  .markdown-body pre code *[class*="hljs-comment"],
  .markdown-body pre code *[class*="hljs-quote"],
  .markdown-body pre code *[class*="hljs-deletion"],
  .markdown-body pre code *[class*="hljs-meta"] {
    color: #5c6370 !important;
    font-style: italic !important;
  }

  /* 强制应用数字颜色 */
  .markdown-body pre code *[class*="hljs-number"],
  .markdown-body pre code *[class*="hljs-regexp"] {
    color: #d19a66 !important;
  }

  /* 强制应用内置函数颜色 */
  .markdown-body pre code *[class*="hljs-built_in"],
  .markdown-body pre code *[class*="hljs-builtin-name"],
  .markdown-body pre code *[class*="hljs-class"] *[class*="hljs-title"] {
    color: #e6c07b !important;
  }

  /* 强制应用属性颜色 */
  .markdown-body pre code *[class*="hljs-attr"],
  .markdown-body pre code *[class*="hljs-type"] {
    color: #e06c75 !important;
  }

  .markdown-body a {
    color: #58a6ff;
    text-decoration: none;
  }

  .markdown-body a:hover {
    text-decoration: underline;
  }

  .markdown-body blockquote {
    padding: 0 1em;
    color: #8b949e;
    border-left: 0.25em solid #30363d;
    margin: 0 0 16px 0;
  }

  .markdown-body ul,
  .markdown-body ol {
    padding-left: 2em;
    margin-bottom: 16px;
  }

  .markdown-body table {
    display: block;
    width: 100%;
    overflow: auto;
    margin-bottom: 16px;
    border-spacing: 0;
    border-collapse: collapse;
  }

  .markdown-body table th,
  .markdown-body table td {
    padding: 6px 13px;
    border: 1px solid #30363d;
  }

  .markdown-body table tr {
    background-color: #0d1117;
    border-top: 1px solid #30363d;
  }

  .markdown-body table tr:nth-child(2n) {
    background-color: #161b22;
  }

  .markdown-body img {
    max-width: 100%;
    box-sizing: border-box;
  }

  .markdown-body p {
    margin-bottom: 16px;
  }

  /* 输入状态指示器样式 */
  .typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    color: #8b949e;
  }

  .typing-dots {
    display: flex;
    gap: 4px;
  }

  .typing-dots span {
    width: 6px;
    height: 6px;
    background-color: #58a6ff;
    border-radius: 50%;
    animation: typing-bounce 1.4s infinite ease-in-out;
  }

  .typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }

  .typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }

  .typing-dots span:nth-child(3) {
    animation-delay: 0s;
  }

  @keyframes typing-bounce {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .typing-text {
    font-size: 14px;
    font-style: italic;
  }

  .typing-cursor {
    display: inline-block;
    background-color: #58a6ff;
    width: 2px;
    height: 1.2em;
    margin-left: 2px;
    animation: cursor-blink 1s infinite;
  }

  @keyframes cursor-blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }
</style>

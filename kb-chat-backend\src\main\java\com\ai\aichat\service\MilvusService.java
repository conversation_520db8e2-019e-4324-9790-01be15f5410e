package com.ai.aichat.service;

import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;

import java.util.List;

public interface MilvusService {

    /**
     * 检查集合是否存在,不存在则创建集合
     */
    void hasCollection(String collectionName, String description);

    /**
     * 插入数据
     *
     * @param vectorParam 向量参数
     * @param text        文本
     * @param metadata    元数据
     * @param fileName    文件名
     */
    InsertResp insert(String collectionName, float[] vectorParam, String text, String metadata, String fileName);

    /**
     * 搜索
     *
     * @param embed 向量参数
     * @return 搜索结果
     */
    SearchResp search(String collectionName, float[] embed);


    /**
     * 批量插入数据
     *
     * @param vectorParam 向量参数
     * @param text        文本
     * @param metadata    元数据
     * @param fileName    文件名
     */
    InsertResp batchInsert(String collectionName, List<float[]> vectorParam, List<String> text, List<String> metadata, List<String> fileName);

    /**
     * 删除数据
     *
     * @param collectionName 集合名称
     * @param ids            id
     */
    void delete(String collectionName, String[] ids);

    /**
     * 搜索
     *
     * @param collectionName
     * @param request
     * @return
     */
    List<Document> search(String collectionName, SearchRequest request);

    /**
     * 删除数据
     *
     * @param collectionName 集合名称
     * @param fileName       文件名
     */
    void delete(String collectionName, String fileName);

    void deleteCollection(Long id);
}
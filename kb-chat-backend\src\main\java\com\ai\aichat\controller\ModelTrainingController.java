package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.model.dto.request.TrainingRequestDto;
import com.ai.aichat.model.vo.response.ModelStructureVo;
import com.ai.aichat.model.vo.response.TrainingDatasetVo;
import com.ai.aichat.model.vo.response.TrainingTaskVo;
import com.ai.aichat.service.ModelStructureService;
import com.ai.aichat.service.TrainingDatasetService;
import com.ai.aichat.service.TrainingTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "模型训练管理", description = "模型微调训练相关接口")
@RequiredArgsConstructor
@RestController
public class ModelTrainingController {

    private final ModelStructureService modelStructureService;
    private final TrainingDatasetService trainingDatasetService;
    private final TrainingTaskService trainingTaskService;

    @Operation(summary = "获取模型结构列表")
    @GetMapping("/model-structure")
    public BaseResponse<List<ModelStructureVo>> getModelStructures() {
        List<ModelStructureVo> result = modelStructureService.getAllModelStructures();
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取训练数据集列表")
    @GetMapping("/datasets")
    public BaseResponse<List<TrainingDatasetVo>> getTrainingDatasets() {
        List<TrainingDatasetVo> result = trainingDatasetService.getAllTrainingDatasets();
        return ResultUtils.success(result);
    }

    @Operation(summary = "开始训练")
    @PostMapping("/train")
    public BaseResponse<TrainingTaskVo> startTraining(@RequestBody TrainingRequestDto requestDto) {
        ThrowUtils.throwIf(requestDto == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        TrainingTaskVo taskVo = trainingTaskService.startTraining(requestDto);
        return ResultUtils.success(taskVo);
    }

    @Operation(summary = "获取训练日志")
    @GetMapping("/training-log")
    public BaseResponse<List<Map<String, Object>>> getTrainingLog() {
        List<Map<String, Object>> result = trainingTaskService.getTrainingLog();
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取训练任务列表")
    @GetMapping("/training-tasks")
    public BaseResponse<List<TrainingTaskVo>> getTrainingTasks() {
        List<TrainingTaskVo> tasks = trainingTaskService.getTrainingTasks();
        return ResultUtils.success(tasks);
    }

    @Operation(summary = "获取训练任务详情")
    @GetMapping("/training-task/{id}")
    public BaseResponse<TrainingTaskVo> getTrainingTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long id) {
        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "任务ID无效");
        TrainingTaskVo task = trainingTaskService.getTrainingTaskById(id);
        return ResultUtils.success(task);
    }

    @Operation(summary = "取消训练任务")
    @PostMapping("/training-task/{id}/cancel")
    public BaseResponse<Boolean> cancelTrainingTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long id) {
        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "任务ID无效");
        Boolean result = trainingTaskService.cancelTrainingTask(id);
        return ResultUtils.success(result);
    }

    @Operation(summary = "创建训练数据集")
    @PostMapping("/datasets")
    public BaseResponse<Boolean> createTrainingDataset(@RequestBody Map<String, Object> request) {
        ThrowUtils.throwIf(request == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");

        String name = (String) request.get("name");
        String description = (String) request.get("description");
        String filePath = (String) request.get("filePath");

        ThrowUtils.throwIf(name == null || name.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "数据集名称不能为空");

        Boolean result = trainingDatasetService.createTrainingDataset(name.trim(), description, filePath);
        return ResultUtils.success(result);
    }

    @Operation(summary = "删除训练数据集")
    @DeleteMapping("/datasets/{id}")
    public BaseResponse<Boolean> deleteTrainingDataset(
            @Parameter(description = "数据集ID", required = true)
            @PathVariable Long id) {
        ThrowUtils.throwIf(id == null || id <= 0, ErrorCode.PARAMS_ERROR, "数据集ID无效");
        Boolean result = trainingDatasetService.deleteTrainingDataset(id);
        return ResultUtils.success(result);
    }
}

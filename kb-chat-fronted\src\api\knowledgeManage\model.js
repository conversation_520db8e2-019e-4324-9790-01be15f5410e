import request from "/src/utils/request";

// 获取模型配置
export const getModelConfig = () => {
  return request({
    url: "/model/config",
    method: "get",
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

// 更新模型配置
export const updateModelConfig = (data) => {
  return request({
    url: "/model/config",
    method: "post",
    headers: {
      'Content-Type': 'application/json'
    },
    data,
  });
};

// 重新加载模型
export const reloadModel = (data) => {
  return request({
    url: "/model/reload",
    method: "post",
    headers: {
      'Content-Type': 'application/json'
    },
    data,
  });
};

// 获取可用模型列表
export const getModelList = () => {
  return request({
    url: "/model/list",
    method: "get",
    headers: {
      'Content-Type': 'application/json'
    }
  });
}; 
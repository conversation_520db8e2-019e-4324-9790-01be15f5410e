package com.ai.aichat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ai.aichat.model.entity.ChatMessage;
import com.ai.aichat.service.ChatMessageService;
import com.ai.aichat.mapper.ChatMessageMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【chat_message(聊天消息明细表)】的数据库操作Service实现
* @createDate 2025-04-25 20:56:02
*/
@Service
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageMapper, ChatMessage>
    implements ChatMessageService{

    @Override
    public void saveMessagePair(Long conversationId, String userMessage, String assistantMessage,
                               String sources, String graphData, String qaPairData) {
        // 保存用户消息
        saveMessage(conversationId, "user", userMessage, null, null, null);

        // 保存助手回复
        saveMessage(conversationId, "assistant", assistantMessage, sources, graphData, qaPairData);
    }

    @Override
    public void saveMessage(Long conversationId, String role, String content, String sources, String graphData, String qaPairData) {
        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setRole(role);
        message.setContent(content);
        message.setSources(sources);
        message.setGraphData(graphData);
        message.setQaPairData(qaPairData);
        // createTime、updateTime、isDelete字段会通过自动填充设置

        this.save(message);
    }

    @Override
    public List<ChatMessage> getMessagesByConversationId(Long conversationId) {
        QueryWrapper<ChatMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("conversation_id", conversationId)
                   .eq("is_delete", 0) // 只查询未删除的消息
                   .orderByAsc("create_time");
        return this.list(queryWrapper);
    }
}





package com.ai.aichat.mcp;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * MCP工具调用测试类
 * 专门测试MCP工具是否能被正确调用
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
@DisplayName("MCP工具调用测试")
class McpToolCallTest {

    @Autowired
    private ChatClient chatClient;

    @BeforeEach
    void setUp() {
        log.info("开始MCP工具调用测试");
    }

    @Test
    @DisplayName("明确指示AI使用天气工具")
    void testExplicitWeatherToolCall() {
        log.info("=== 明确指示AI使用天气工具 ===");
        
        try {
            String prompt = "请使用天气查询工具查询北京的天气情况。你有可用的工具，请一定要使用工具来获取实时天气信息。";
            
            log.info("发送请求: {}", prompt);
            
            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();
            
            log.info("AI响应: {}", response.getResult().getOutput().getText());
            
            // 检查是否调用了工具
            if (response.getResult().getOutput().getToolCalls() != null && 
                !response.getResult().getOutput().getToolCalls().isEmpty()) {
                log.info("✅ AI调用了工具！");
                response.getResult().getOutput().getToolCalls().forEach(toolCall -> {
                    log.info("调用的工具: {}", toolCall.name());
                    log.info("工具参数: {}", toolCall.arguments());
                });
            } else {
                log.warn("❌ AI没有调用任何工具");
            }
            
        } catch (Exception e) {
            log.error("明确指示天气工具测试失败", e);
        }
    }

    @Test
    @DisplayName("测试具体的MCP工具名称调用")
    void testSpecificMcpToolCall() {
        log.info("=== 测试具体的MCP工具名称调用 ===");
        
        try {
            String prompt = "请使用 spring_ai_mcp_client_amap_maps_maps_weather 工具查询北京的天气。";
            
            log.info("发送请求: {}", prompt);
            
            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();
            
            log.info("AI响应: {}", response.getResult().getOutput().getText());
            
            // 检查是否调用了工具
            if (response.getResult().getOutput().getToolCalls() != null && 
                !response.getResult().getOutput().getToolCalls().isEmpty()) {
                log.info("✅ AI调用了工具！");
                response.getResult().getOutput().getToolCalls().forEach(toolCall -> {
                    log.info("调用的工具: {}", toolCall.name());
                    log.info("工具参数: {}", toolCall.arguments());
                });
            } else {
                log.warn("❌ AI没有调用任何工具");
            }

        } catch (Exception e) {
            log.error("具体MCP工具调用测试失败", e);
        }
    }

    @Test
    @DisplayName("测试英文提示的工具调用")
    void testEnglishPromptToolCall() {
        log.info("=== 测试英文提示的工具调用 ===");

        try {
            String prompt = "Please use the weather tool to check the weather in Beijing. You have access to weather tools, please use them to get real-time weather information.";

            log.info("发送请求: {}", prompt);

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();

            log.info("AI响应: {}", response.getResult().getOutput().getText());

            // 检查是否调用了工具
            if (response.getResult().getOutput().getToolCalls() != null &&
                !response.getResult().getOutput().getToolCalls().isEmpty()) {
                log.info("✅ AI调用了工具！");
                response.getResult().getOutput().getToolCalls().forEach(toolCall -> {
                    log.info("调用的工具: {}", toolCall.name());
                    log.info("工具参数: {}", toolCall.arguments());
                });
            } else {
                log.warn("❌ AI没有调用任何工具");
            }

        } catch (Exception e) {
            log.error("英文提示工具调用测试失败", e);
        }
    }

    @Test
    @DisplayName("测试系统提示中说明可用工具")
    void testSystemPromptWithTools() {
        log.info("=== 测试系统提示中说明可用工具 ===");

        try {
            String systemPrompt = "你是一个AI助手，你有以下可用的工具：\n" +
                "1. spring_ai_mcp_client_amap_maps_maps_weather - 查询天气信息\n" +
                "2. spring_ai_mcp_client_amap_maps_maps_geo - 地址转坐标\n" +
                "3. spring_ai_mcp_client_amap_maps_maps_regeocode - 坐标转地址\n" +
                "当用户询问相关信息时，请主动使用这些工具。";

            String userPrompt = "北京现在的天气怎么样？";

            log.info("系统提示: {}", systemPrompt);
            log.info("用户请求: {}", userPrompt);

            ChatResponse response = chatClient.prompt()
                .system(systemPrompt)
                .user(userPrompt)
                .call()
                .chatResponse();

            log.info("AI响应: {}", response.getResult().getOutput().getText());

            // 检查是否调用了工具
            if (response.getResult().getOutput().getToolCalls() != null &&
                !response.getResult().getOutput().getToolCalls().isEmpty()) {
                log.info("✅ AI调用了工具！");
                response.getResult().getOutput().getToolCalls().forEach(toolCall -> {
                    log.info("调用的工具: {}", toolCall.name());
                    log.info("工具参数: {}", toolCall.arguments());
                });
            } else {
                log.warn("❌ AI没有调用任何工具");
            }

        } catch (Exception e) {
            log.error("系统提示工具调用测试失败", e);
        }
    }
}

package com.ai.aichat.service.impl;

import com.ai.aichat.config.MilvusArchive;
import com.ai.aichat.service.MilvusService;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.*;
import io.milvus.v2.service.index.request.CreateIndexReq;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.DeleteResp;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class MilvusServiceImpl implements MilvusService {

    private final MilvusClientV2 milvusClientV2;
    private final OpenAiEmbeddingModel openAiEmbeddingModel;

    /**
     * 检查集合是否存在
     */
    @Override
    public void hasCollection(String collectionName, String description) {
        Boolean b = milvusClientV2.hasCollection(HasCollectionReq.builder().collectionName(collectionName).build());
        if (!b) {
            this.createCollection(collectionName, description);
            milvusClientV2.loadCollection(LoadCollectionReq.builder().collectionName(collectionName).build());
        }
    }

    /**
     * 插入数据
     */
    @Override
    public InsertResp insert(String collectionName, float[] vectorParam, String text, String metadata, String fileName) {
        this.hasCollection(collectionName, null);
        JsonObject jsonObject = new JsonObject();
        jsonObject.add(MilvusArchive.Field.FEATURE, new Gson().toJsonTree(vectorParam));
        jsonObject.add(MilvusArchive.Field.TEXT, new Gson().toJsonTree(text));
        jsonObject.add(MilvusArchive.Field.METADATA, new Gson().toJsonTree(metadata));
        jsonObject.add(MilvusArchive.Field.FILE_NAME, new Gson().toJsonTree(fileName));
        InsertReq insertReq = InsertReq.builder()
                .collectionName(collectionName)
                .data(Collections.singletonList(jsonObject))
                .build();
        return milvusClientV2.insert(insertReq);
    }

    /**
     * 搜索数据
     *
     * @param vectorParam 向量参数
     */
    @Override
    public SearchResp search(String collectionName, float[] vectorParam) {
        milvusClientV2.loadCollection(LoadCollectionReq.builder().collectionName(collectionName).build());
        FloatVec floatVec = new FloatVec(vectorParam);
        SearchReq searchReq = SearchReq.builder()
                .collectionName(collectionName)
                .metricType(IndexParam.MetricType.COSINE)
                .data(Collections.singletonList(floatVec))
                .annsField(MilvusArchive.Field.FEATURE)
                .outputFields(Arrays.asList(MilvusArchive.Field.ID, MilvusArchive.Field.TEXT, MilvusArchive.Field.METADATA, MilvusArchive.Field.FILE_NAME))
                .topK(5)
                .build();
        return milvusClientV2.search(searchReq);
    }

    /**
     * 批量插入数据
     *
     * @param vectorParam 向量参数
     * @param text        文本
     * @param metadata    元数据
     * @param fileName    文件名
     */
    @Override
    public InsertResp batchInsert(String collectionName, List<float[]> vectorParam, List<String> text, List<String> metadata, List<String> fileName) {
        if (vectorParam.size() == text.size() && vectorParam.size() == metadata.size() && vectorParam.size() == fileName.size()) {
            List<JsonObject> jsonObjects = new ArrayList<>();
            for (int i = 0; i < vectorParam.size(); i++) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.add(MilvusArchive.Field.FEATURE, new Gson().toJsonTree(vectorParam.get(i)));
                jsonObject.add(MilvusArchive.Field.TEXT, new Gson().toJsonTree(text.get(i)));
                jsonObject.add(MilvusArchive.Field.METADATA, new Gson().toJsonTree(metadata.get(i)));
                jsonObject.add(MilvusArchive.Field.FILE_NAME, new Gson().toJsonTree(fileName.get(i)));
                jsonObjects.add(jsonObject);
            }
            InsertReq insertReq = InsertReq.builder()
                    .collectionName(collectionName)
                    .data(jsonObjects)
                    .build();
            this.hasCollection(collectionName,null);
            if(!jsonObjects.isEmpty()){
                return milvusClientV2.insert(insertReq);
            }
        }
        return null;
    }

    @Override
    public void delete(String collectionName, String[] ids) {
        DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(collectionName)
                .ids(Arrays.asList(ids))
                .build();

        milvusClientV2.delete(deleteReq);
    }

    @Override
    public List<Document> search(String collectionName, SearchRequest request) {
        this.hasCollection(collectionName,null);
        milvusClientV2.loadCollection(LoadCollectionReq.builder().collectionName(collectionName).build());
        SearchResp searchResp = this.search(collectionName, openAiEmbeddingModel.embed(request.getQuery()));

        List<List<SearchResp.SearchResult>> searchResults = searchResp.getSearchResults();
        for (List<SearchResp.SearchResult> results : searchResults) {
            System.out.println("TopK results:");
            for (SearchResp.SearchResult result : results) {
                System.out.println(result);
            }
        }
        return searchResults.stream().flatMap(Collection::stream).map(result -> {
            Document document = new Document(result.getEntity().get(MilvusArchive.Field.TEXT).toString());
            document.getMetadata();
            return document;
        }).toList();
    }

    @Override
    public void delete(String collectionName, String fileName) {
        Boolean b = milvusClientV2.hasCollection(HasCollectionReq.builder().collectionName(collectionName).build());
        if (!b) {
            throw new RuntimeException("集合 [" + collectionName + "] 不存在");
        }
        System.out.println("准备在集合 [" + collectionName + "] 中删除文件名为 [" + fileName + "] 的实体...");

        // 1.构造删除表达式 (Expression)
        String deleteExpression = String.format("file_name in [\"%s\"]", fileName);
        System.out.println("生成的删除表达式: " + deleteExpression);

        // 2. 构建删除请求
        DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(collectionName)
                .filter(deleteExpression)
                .build();

        // 3. 执行删除
        DeleteResp response = milvusClientV2.delete(deleteReq);

        System.out.println("删除结果: " + response);
    }

    @Override
    public void deleteCollection(Long id) {
        String collectionName = "kb_" +  id;
        DropCollectionReq dropQuickSetupParam = DropCollectionReq.builder()
                .collectionName(collectionName)
                .build();

        milvusClientV2.dropCollection(dropQuickSetupParam);
    }

    /**
     * 创建集合
     */
    public void createCollection(String collectionName, String description) {
        CreateCollectionReq.CollectionSchema schema = milvusClientV2.createSchema()
                .addField(AddFieldReq.builder()
                        .fieldName(MilvusArchive.Field.ID)
                        .description("主键ID")
                        .dataType(DataType.Int64)
                        .isPrimaryKey(true)
                        .autoID(true)
                        .build())
                .addField(AddFieldReq.builder()
                        .fieldName(MilvusArchive.Field.FILE_NAME)
                        .description("文件名")
                        .dataType(DataType.VarChar)
                        .isNullable(true)
                        .build())
                .addField(AddFieldReq.builder()
                        .fieldName(MilvusArchive.Field.FEATURE)
                        .description("特征向量")
                        .dataType(DataType.FloatVector)
                        .dimension(MilvusArchive.FEATURE_DIM)
                        .build())
                .addField(AddFieldReq.builder()
                        .fieldName(MilvusArchive.Field.TEXT)
                        .description("文本")
                        .dataType(DataType.VarChar)
                        .isNullable(true)
                        .build())
                .addField(AddFieldReq.builder()
                        .fieldName(MilvusArchive.Field.METADATA)
                        .description("元数据")
                        .dataType(DataType.VarChar)
                        .isNullable(true)
                        .build());
        CreateCollectionReq collectionReq = CreateCollectionReq.builder()
                .collectionName(collectionName)
                .description(description != null ? description : "自定义知识库")
                .collectionSchema(schema)
                .numShards(MilvusArchive.SHARDS_NUM)
                .build();
        milvusClientV2.createCollection(collectionReq);

        IndexParam indexParam = IndexParam.builder()
                .fieldName(MilvusArchive.Field.FEATURE)
                .indexType(IndexParam.IndexType.IVF_FLAT)
                .metricType(IndexParam.MetricType.COSINE)
                .build();
        CreateIndexReq createIndexReq = CreateIndexReq.builder()
                .collectionName(collectionName)
                .indexParams(Collections.singletonList(indexParam))
                .build();

        milvusClientV2.createIndex(createIndexReq);
    }
}
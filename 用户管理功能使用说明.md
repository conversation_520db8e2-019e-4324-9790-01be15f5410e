# KB-Chat 用户管理功能使用说明

## 概述

本文档介绍了 KB-Chat 系统中用户管理功能的使用方法，包括用户注册、登录、个人信息管理以及管理员用户管理等功能。

## 功能特性

### 1. 用户注册与登录
- ✅ 用户注册功能（默认注册为普通用户）
- ✅ 用户登录功能
- ✅ 登录状态保持
- ✅ 安全退出登录

### 2. 个人信息管理
- ✅ 查看个人信息
- ✅ 修改用户名称
- ✅ 修改用户简介
- ✅ 查看用户角色和创建时间
- ⏳ 头像上传（暂未开放）
- ⏳ 密码修改（暂未开放）

### 3. 管理员功能
- ✅ 用户列表查看
- ✅ 用户信息搜索（按用户名、账号、角色）
- ✅ 新增用户
- ✅ 编辑用户信息
- ✅ 删除用户（不能删除ID为1的管理员）
- ✅ 角色权限控制

## 快速开始

### 1. 数据库初始化

首先需要初始化数据库和测试数据：

```sql
-- 1. 执行数据库表创建脚本
source kb-chat-backend/sql/create_table.sql;

-- 2. 执行初始化数据脚本（可选）
source kb-chat-backend/sql/init_data.sql;
```

### 2. 启动服务

```bash
# 启动后端服务
cd kb-chat-backend
mvn spring-boot:run

# 启动前端服务
cd kb-chat-fronted
npm run dev
```

### 3. 访问系统

打开浏览器访问：http://localhost:6522

## 使用指南

### 用户注册

1. 在登录页面点击"立即注册"按钮
2. 填写注册信息：
   - 登录账号：4-20个字符
   - 用户密码：8-20个字符
   - 确认密码：需与密码一致
3. 点击"注册"按钮完成注册
4. 注册成功后会自动跳转到登录页面

### 用户登录

1. 在登录页面输入账号和密码
2. 点击"登录"按钮
3. 登录成功后会跳转到系统首页

### 个人信息管理

1. 点击右上角用户头像下拉菜单
2. 选择"个人中心"
3. 在个人中心可以：
   - 查看基本信息
   - 编辑用户名称和简介
   - 查看账号信息和角色

### 管理员用户管理

**注意：只有管理员用户才能访问用户管理功能**

1. 使用管理员账号登录
2. 点击右上角用户头像下拉菜单
3. 选择"用户管理"
4. 在用户管理页面可以：
   - 查看所有用户列表
   - 搜索用户（按用户名、账号、角色）
   - 新增用户
   - 编辑用户信息
   - 删除用户

## 测试账号

系统提供了以下测试账号（如果执行了初始化数据脚本）：

| 账号 | 密码 | 角色 | 说明 |
|------|------|------|------|
| admin | 123456 | 管理员 | 系统管理员，拥有所有权限 |
| user1 | 123456 | 普通用户 | 测试用户账号1 |
| user2 | 123456 | 普通用户 | 测试用户账号2 |

## 权限说明

### 普通用户权限
- 登录系统
- 查看和修改个人信息
- 使用聊天功能
- 管理个人知识库

### 管理员权限
- 普通用户的所有权限
- 查看所有用户信息
- 新增、编辑、删除用户
- 系统管理功能

## 注意事项

1. **密码安全**：系统使用BCrypt加密存储密码，确保安全性
2. **角色权限**：管理员功能受到严格的权限控制，普通用户无法访问
3. **数据保护**：ID为1的管理员用户不能被删除，确保系统始终有管理员
4. **功能限制**：部分功能（如头像上传、密码修改）暂未开放，后续版本会完善

## 常见问题

### Q: 忘记密码怎么办？
A: 目前系统暂未提供密码重置功能，请联系管理员重置密码。

### Q: 如何创建第一个管理员用户？
A: 可以执行初始化数据脚本，或者直接在数据库中插入管理员用户记录。

### Q: 普通用户能否升级为管理员？
A: 需要现有管理员在用户管理页面修改用户角色。

### Q: 系统支持多少用户？
A: 理论上没有限制，取决于服务器性能和数据库配置。

## 技术支持

如有问题，请联系开发团队或查看项目文档。

import request from '@/utils/request'

// 查询接口调用记录列表
export function listOpenInterfaceAccessLog(query) {
  return request({
    url: '/open/openInterfaceAccessLog/list',
    method: 'get',
    params: query
  })
}

// 查询接口调用记录详细
export function getOpenInterfaceAccessLog(id) {
  return request({
    url: '/open/openInterfaceAccessLog/' + id,
    method: 'get'
  })
}

// 新增接口调用记录
export function addOpenInterfaceAccessLog(data) {
  return request({
    url: '/open/openInterfaceAccessLog',
    method: 'post',
    data: data
  })
}

// 修改接口调用记录
export function updateOpenInterfaceAccessLog(data) {
  return request({
    url: '/open/openInterfaceAccessLog',
    method: 'put',
    data: data
  })
}

// 删除接口调用记录
export function delOpenInterfaceAccessLog(id) {
  return request({
    url: '/open/openInterfaceAccessLog/' + id,
    method: 'delete'
  })
}

<template>
  <div class="qa-pair-view-container">
    <div class="preview-header">
      <div class="preview-title">问答对检索结果</div>
      <div class="close-preview-btn" @click="$emit('close')">
        <i class="el-icon-close"></i>
        <span>关闭预览</span>
      </div>
    </div>
    <div class="qa-pair-content">
      <div
        class="drawBoard"
        ref="qaPairContainer"></div>
      <div class="info-box">
        <h3>问答对详情</h3>
        <div v-if="selectedQA">
          <h4>{{ selectedQA.question }}</h4>
          <div class="answer-content">
            <strong>答案：</strong>
            <p>{{ selectedQA.answer }}</p>
          </div>
          <div class="qa-meta">
            <div class="score-info">
              <span class="score-label">相似度：</span>
              <span class="score-value">{{ (selectedQA.score * 100).toFixed(1) }}%</span>
            </div>
            <div class="methods-info" v-if="selectedQA.search_methods && selectedQA.search_methods.length > 0">
              <span class="methods-label">检索方法：</span>
              <span class="methods-value">{{ selectedQA.search_methods.join(', ') }}</span>
            </div>
            <div class="keywords-info" v-if="selectedQA.matched_keywords && selectedQA.matched_keywords.length > 0">
              <span class="keywords-label">匹配关键词：</span>
              <div class="keywords-tags">
                <span
                  v-for="keyword in selectedQA.matched_keywords"
                  :key="keyword"
                  class="keyword-tag">
                  {{ keyword }}
                </span>
              </div>
            </div>
            <div class="source-info" v-if="selectedQA.source_file">
              <span class="source-label">来源文件：</span>
              <span class="source-value">{{ selectedQA.source_file }}</span>
            </div>
          </div>
        </div>
        <div v-else>
          <p>点击节点查看问答对详情</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 导入 Vis.js 库
import { Network } from "vis-network/standalone/umd/vis-network.min.js";

export default {
  name: "QAPairView",
  props: {
    // 问答对数据直接从父组件传入
    qaPairData: {
      type: Object,
      default: null
    }
  },
  emits: ['close'],
  
  data() {
    return {
      network: null,
      selectedQA: null
    };
  },

  watch: {
    // 监听qaPairData变化，当有新数据时创建图谱
    qaPairData: {
      handler(newData) {
        if (newData) {
          this.$nextTick(() => {
            this.createQAPairGraph();
          });
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    createQAPairGraph() {
      if (!this.qaPairData || !this.qaPairData.nodes) return;

      const container = this.$refs.qaPairContainer;
      if (!container) return; // 确保DOM元素已加载
      
      // 处理边的数据
      const modifiedEdges = (this.qaPairData.edges || []).map(edge => {
        return {
          id: edge.id,
          from: edge.source,
          to: edge.target,
          label: edge.label || '',
          title: edge.label || '',
          arrows: 'to'
        };
      });

      const options = {
        height: "100%",
        width: "100%",
        nodes: {
          shape: "box",
          size: 25,
          font: {
            color: "#ffffff",
            size: 12,
            face: 'arial',
            bold: false
          },
          borderWidth: 2,
          shadow: true,
          margin: 10,
          widthConstraint: {
            maximum: 200
          }
        },
        edges: {
          width: 2,
          shadow: true,
          font: {
            size: 12,
            color: "#ffffff",
            face: 'arial',
            background: {
              enabled: true,
              color: '#092762'
            },
            strokeWidth: 0
          },
          arrows: 'to',
          smooth: { type: "continuous" }
        },
        groups: {
          main: {
            color: {
              background: '#e04141',
              border: '#941e1e'
            }
          },
          related: {
            color: {
              background: '#4169e1',
              border: '#0000cd'
            }
          }
        },
        physics: {
          enabled: true,
          barnesHut: {
            gravitationalConstant: -2000,
            springLength: 150
          }
        }
      };

      // 创建网络图实例
      this.network = new Network(
        container,
        {
          nodes: this.qaPairData.nodes.map(node => ({
            id: node.id,
            label: node.label,
            title: `问题: ${node.question}\n相似度: ${(node.score * 100).toFixed(1)}%`,
            group: node.isMainNode ? 'main' : 'related'
          })),
          edges: modifiedEdges
        },
        options
      );

      // 添加点击事件监听器
      this.network.on("click", (params) => {
        if (params.nodes.length > 0) {
          const nodeId = params.nodes[0];
          const node = this.qaPairData.nodes.find(n => n.id === nodeId);
          if (node) {
            this.selectedQA = {
              question: node.question,
              answer: node.answer,
              score: node.score,
              search_methods: node.search_methods,
              matched_keywords: node.matched_keywords,
              source_file: node.source_file
            };
          }
        }
      });

      // 自动选择第一个节点
      if (this.qaPairData.nodes.length > 0) {
        this.selectedQA = {
          question: this.qaPairData.nodes[0].question,
          answer: this.qaPairData.nodes[0].answer,
          score: this.qaPairData.nodes[0].score,
          search_methods: this.qaPairData.nodes[0].search_methods,
          matched_keywords: this.qaPairData.nodes[0].matched_keywords,
          source_file: this.qaPairData.nodes[0].source_file
        };
      }
    },


  }
};
</script>

<style scoped>
.qa-pair-view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(9, 39, 98, 0.95);
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1000;
}

.preview-header {
  height: 36px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  background-color: rgba(9, 39, 98, 0.9);
  border-bottom: 1px solid #3f63a8;
}

.preview-title {
  font-weight: bold;
  color: #13fff3;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-title::before {
  content: '💬';
  font-size: 14px;
}

.close-preview-btn {
  cursor: pointer;
  padding: 4px 8px;
  color: white;
  background-color: rgba(15, 45, 90, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 13px;
  transition: all 0.2s;
  z-index: 1010;
}

.close-preview-btn:hover {
  background-color: rgba(19, 255, 243, 0.15);
  color: #13fff3;
  border-color: #13fff3;
}

.qa-pair-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawBoard {
  flex: 7;
  min-height: 0;
  background-color: #011b53;
  border: 1px solid #083271;
}

.info-box {
  flex: 3;
  min-height: 0;
  height: auto;
  max-height: none;
  padding: 10px;
  background-color: #011b53;
  color: white;
  overflow-y: auto;
  border-top: 1px solid #083271;
}

.info-box h3 {
  margin-top: 0;
  color: #13fff3;
  margin-bottom: 15px;
  border-bottom: 1px solid #3f63a8;
  padding-bottom: 5px;
}

.info-box h4 {
  color: #4169e1;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.4;
}

.answer-content {
  margin-bottom: 15px;
}

.answer-content strong {
  color: #13fff3;
  display: block;
  margin-bottom: 5px;
}

.answer-content p {
  line-height: 1.5;
  margin: 0;
  font-size: 13px;
}

.qa-meta {
  border-top: 1px solid #3f63a8;
  padding-top: 10px;
}

.score-info, .methods-info, .keywords-info {
  margin-bottom: 8px;
  font-size: 12px;
}

.score-label, .methods-label, .keywords-label {
  color: #13fff3;
  font-weight: bold;
}

.score-value {
  color: #4169e1;
  font-weight: bold;
}

.methods-value {
  color: #ffffff;
}

.keywords-tags {
  margin-top: 5px;
}

.keyword-tag {
  display: inline-block;
  background-color: rgba(65, 105, 225, 0.3);
  color: #4169e1;
  padding: 2px 6px;
  margin: 2px;
  border-radius: 3px;
  font-size: 11px;
  border: 1px solid #4169e1;
}

.source-info {
  margin-bottom: 8px;
  font-size: 12px;
}

.source-label {
  color: #13fff3;
  font-weight: bold;
}

.source-value {
  color: #ffffff;
}
</style>

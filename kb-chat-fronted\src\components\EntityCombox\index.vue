<template>
	<el-select v-model="entity" @change="change" :filterable="true">
		<el-option
			v-for="item in entities"
			:key="item.entityNameXml.value"
			:label="item.entityNameXml.value"
			:value="item.entityNameXml.value"
		>
			<div style="display: flex">
				<div>
					<img class="mark_img"
						:class="
							item.campXml.value
								? item.campXml.value == 1
									? 'red_camp'
									: item.campXml.value == 2
									? 'blue_camp'
									: 'yellow_camp'
								: 'yellow_camp'
						"
						style="margin-right: 5px; height: 20px; width: 20px"
						:src="getMarkImg(item.markIDXml.value)"
						v-show="item.markIDXml"
					/>
				</div>
				<span style="line-height: 25px">
					{{ item.entityNameXml.value }}
				</span>
			</div>
		</el-option>
		<div slot="prefix">
			<img
				class="mark_img"
				:class="
					camp == 1 ? 'red_camp' : camp == 2 ? 'blue_camp' : 'yellow_camp'
				"
				style="margin-right: 5px; height: 20px; width: 20px; margin-top: 4px"
				:src="getMarkImg(markId)"
				v-show="markId"
			/>
		</div>
	</el-select>
</template>
<script setup>
import {ref,defineProps,defineEmits} from "vue"
const emit = defineEmits()
const entity = ref(undefined)
const camp = ref(undefined)
const markId = ref(undefined)
const props = defineProps(["entities"])
function change(value) {
  if(value) {
    const entity = props.entities.find(item => item.entityNameXml.value === value);
    camp.value = entity.campXml.value;
    markId.value = entity.markIDXml.value
    emit('change', value, entity)
  }else{
    emit('change', value)
  }
  emit('update:value', value)
}
/** 获取军标图片 */
function getMarkImg(markid) {
  if (!markid) {
    markid = 'Default'
  }
  return new URL('../../assets/images/mark/white/' + markid + '.png',import.meta.url).href
}

</script>
<style  lang="scss" scoped>
	img.mark_img {
		width: 20px;
		height: 20px;
	}
	/**红色图标 */
	img.red_camp {
		filter: invert(30%) sepia(96%) saturate(7163%) hue-rotate(357deg)
			brightness(101%) contrast(116%);
	}
	/**蓝色图标 */
	img.blue_camp {
		filter: invert(95%) sepia(162%) saturate(7425%) hue-rotate(237deg)
			brightness(181%) contrast(95%);
	}
	/**黄色图标 */
	img.yellow_camp {
		filter: invert(76%) sepia(163%) saturate(1521%) hue-rotate(403deg)
			brightness(215%) contrast(105%);
	}
</style>

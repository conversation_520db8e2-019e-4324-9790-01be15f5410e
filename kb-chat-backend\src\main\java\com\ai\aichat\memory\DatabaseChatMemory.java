package com.ai.aichat.memory;

import com.ai.aichat.model.entity.ChatMessage;
import com.ai.aichat.model.entity.Conversation;
import com.ai.aichat.service.ChatMessageService;
import com.ai.aichat.service.ConversationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于MySQL数据库的对话记忆实现
 * 实现Spring AI的ChatMemory接口，支持持久化存储对话历史
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseChatMemory implements ChatMemory {

    private final ChatMessageService chatMessageService;
    private final ConversationService conversationService;

    @Override
    public void add(String conversationId, List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }

        log.debug("Adding {} messages to conversation: {}", messages.size(), conversationId);

        // 获取或创建会话
        Long dbConversationId = getOrCreateConversation(conversationId);

        // 保存消息到数据库
        for (Message message : messages) {
            saveMessageToDatabase(dbConversationId, message);
        }

        log.debug("Successfully added {} messages to conversation: {}", messages.size(), conversationId);
    }

    @Override
    public List<Message> get(String conversationId, int lastN) {
        log.debug("Getting last {} messages from conversation: {}", lastN, conversationId);

        // 获取会话ID
        Long dbConversationId = getConversationId(conversationId);
        if (dbConversationId == null) {
            log.debug("Conversation not found: {}", conversationId);
            return new ArrayList<>();
        }

        // 从数据库获取消息
        List<ChatMessage> chatMessages = getMessagesFromDatabase(dbConversationId, lastN);

        // 转换为Spring AI Message对象
        List<Message> messages = chatMessages.stream()
                .map(this::convertToMessage)
                .collect(Collectors.toList());

        log.debug("Retrieved {} messages from conversation: {}", messages.size(), conversationId);
        return messages;
    }

    @Override
    public void clear(String conversationId) {
        log.debug("Clearing conversation: {}", conversationId);

        Long dbConversationId = getConversationId(conversationId);
        if (dbConversationId == null) {
            log.debug("Conversation not found for clearing: {}", conversationId);
            return;
        }

        // 软删除所有消息
        QueryWrapper<ChatMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("conversation_id", dbConversationId)
                   .eq("is_delete", 0);

        List<ChatMessage> messages = chatMessageService.list(queryWrapper);
        for (ChatMessage message : messages) {
            message.setIsDelete(1);
        }
        chatMessageService.updateBatchById(messages);

        log.debug("Cleared {} messages from conversation: {}", messages.size(), conversationId);
    }

    /**
     * 获取或创建会话
     */
    private Long getOrCreateConversation(String sessionId) {
        // 先尝试获取现有会话
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", sessionId)
                   .eq("is_delete", 0);

        Conversation conversation = conversationService.getOne(queryWrapper);

        if (conversation != null) {
            return conversation.getId();
        }

        // 创建新会话
        conversation = new Conversation();
        conversation.setSessionId(sessionId);
        conversation.setTitle("AI对话"); // 默认标题
        conversation.setChatType("chat"); // 默认类型
        conversation.setCreateTime(new Date());
        conversation.setUpdateTime(new Date());
        conversation.setMessageCount(0);
        conversation.setIsDelete(0);

        conversationService.save(conversation);
        log.debug("Created new conversation with ID: {} for session: {}", conversation.getId(), sessionId);

        return conversation.getId();
    }

    /**
     * 获取会话ID（不创建新会话）
     */
    private Long getConversationId(String sessionId) {
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", sessionId)
                   .eq("is_delete", 0);

        Conversation conversation = conversationService.getOne(queryWrapper);
        return conversation != null ? conversation.getId() : null;
    }

    /**
     * 保存消息到数据库并更新会话计数
     */
    private void saveMessageToDatabase(Long conversationId, Message message) {
        String role = determineRole(message);
        String content = message.getText();

        // 保存消息
        chatMessageService.saveMessage(conversationId, role, content, null, null, null);

        // 更新会话消息计数和更新时间
        updateConversationMessageCount(conversationId);
    }

    /**
     * 更新会话消息计数和更新时间
     */
    private void updateConversationMessageCount(Long conversationId) {
        try {
            Conversation conversation = conversationService.getById(conversationId);
            if (conversation != null) {
                Integer currentCount = conversation.getMessageCount() != null ? conversation.getMessageCount() : 0;
                conversation.setMessageCount(currentCount + 1);
                conversation.setUpdateTime(new Date());
                conversationService.updateById(conversation);

                log.debug("Updated message count for conversation {}: {}", conversationId, currentCount + 1);
            }
        } catch (Exception e) {
            log.error("Failed to update message count for conversation {}: {}", conversationId, e.getMessage());
        }
    }

    /**
     * 从数据库获取消息
     */
    private List<ChatMessage> getMessagesFromDatabase(Long conversationId, int lastN) {
        QueryWrapper<ChatMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("conversation_id", conversationId)
                   .eq("is_delete", 0)
                   .orderByDesc("create_time");

        if (lastN > 0) {
            queryWrapper.last("LIMIT " + lastN);
        }

        List<ChatMessage> messages = chatMessageService.list(queryWrapper);
        
        // 按时间正序排列（最早的在前面）
        messages.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));
        
        return messages;
    }

    /**
     * 确定消息角色
     */
    private String determineRole(Message message) {
        switch (message.getMessageType()) {
            case USER:
                return "user";
            case ASSISTANT:
                return "assistant";
            case SYSTEM:
                return "system";
            default:
                return "unknown";
        }
    }

    /**
     * 将数据库消息转换为Spring AI Message
     */
    private Message convertToMessage(ChatMessage chatMessage) {
        String content = chatMessage.getContent();
        String role = chatMessage.getRole();

        switch (role.toLowerCase()) {
            case "user":
                return new UserMessage(content);
            case "assistant":
                return new AssistantMessage(content);
            default:
                // 对于其他类型，默认作为用户消息处理
                return new UserMessage(content);
        }
    }
}

.container {
    color: #fff;
    /* background-color: aquamarine; */
    padding: 20px;
  }

  nav {
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  nav .route {
    color: #8799b7;
    font-size: 14px;
  }

  .route .focus {
    color: #fff;
  }

  nav .route span {
    margin: 0 5px;
  }

  main {
    /*background-color: #081c50;*/
    /* border: 1px solid #42474e; */
    padding-top: 5px;
    /* border-radius: 5px; */
  }


  .font-icon {
    width: 5px;
    height: 15px;
    background-color: #409eff;
    transform: translate(0%,0%);
    margin-right: 15px;
  }

 .card-container {
    padding: 0px;
 }

 .cards {
    margin-top: 10px;
 }

  .card-header {
   position: relative;
   display: flex;
   align-items: center
  }


  .card-footer {
    margin-top: 100px;
    margin-bottom: 10px;
    display: flex;
    justify-content: center


  }

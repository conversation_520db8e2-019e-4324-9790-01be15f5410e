package com.ai.aichat.mcp;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * MCP直接客户端测试类
 * 直接测试MCP客户端连接和工具列表
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
@DisplayName("MCP直接客户端测试")
class McpDirectClientTest {

    @BeforeEach
    void setUp() {
        log.info("开始MCP直接客户端测试");
    }

    @Test
    @DisplayName("直接测试高德地图MCP服务器工具列表")
    void testAmapMcpServerDirectly() {
        log.info("=== 直接测试高德地图MCP服务器 ===");
        
        try {
            // 使用ProcessBuilder直接调用npx命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                "npx.cmd", "-y", "@amap/amap-maps-mcp-server"
            );
            
            // 设置环境变量
            processBuilder.environment().put("AMAP_MAPS_API_KEY", "6d8c824b96d59b150e6e0adf4d886a5a");
            
            log.info("启动高德地图MCP服务器进程...");
            Process process = processBuilder.start();
            
            // 等待一段时间让服务器启动
            Thread.sleep(5000);
            
            if (process.isAlive()) {
                log.info("高德地图MCP服务器进程正在运行");
                
                // 读取输出
                try (var reader = process.inputReader()) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        log.info("MCP服务器输出: {}", line);
                    }
                }
                
                // 读取错误输出
                try (var errorReader = process.errorReader()) {
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        log.info("MCP服务器错误输出: {}", line);
                    }
                }
            } else {
                log.warn("高德地图MCP服务器进程已退出，退出码: {}", process.exitValue());
            }
            
            // 清理进程
            if (process.isAlive()) {
                process.destroyForcibly();
            }
            
        } catch (Exception e) {
            log.error("直接测试高德地图MCP服务器失败", e);
        }
    }

    @Test
    @DisplayName("检查高德地图MCP包是否存在")
    void testAmapMcpPackageExists() {
        log.info("=== 检查高德地图MCP包是否存在 ===");
        
        try {
            // 使用npm list检查包是否存在
            ProcessBuilder processBuilder = new ProcessBuilder(
                "npm", "list", "-g", "@amap/amap-maps-mcp-server"
            );
            
            log.info("检查高德地图MCP包...");
            Process process = processBuilder.start();
            
            // 等待进程完成
            int exitCode = process.waitFor();
            
            log.info("npm list 退出码: {}", exitCode);
            
            // 读取输出
            try (var reader = process.inputReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("npm输出: {}", line);
                }
            }
            
            // 读取错误输出
            try (var errorReader = process.errorReader()) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    log.info("npm错误输出: {}", line);
                }
            }
            
        } catch (Exception e) {
            log.error("检查高德地图MCP包失败", e);
        }
    }

    @Test
    @DisplayName("尝试安装高德地图MCP包")
    void testInstallAmapMcpPackage() {
        log.info("=== 尝试安装高德地图MCP包 ===");
        
        try {
            // 使用npm install安装包
            ProcessBuilder processBuilder = new ProcessBuilder(
                "npm", "install", "-g", "@amap/amap-maps-mcp-server"
            );
            
            log.info("安装高德地图MCP包...");
            Process process = processBuilder.start();
            
            // 等待进程完成
            int exitCode = process.waitFor();
            
            log.info("npm install 退出码: {}", exitCode);
            
            // 读取输出
            try (var reader = process.inputReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("npm安装输出: {}", line);
                }
            }
            
            // 读取错误输出
            try (var errorReader = process.errorReader()) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    log.info("npm安装错误输出: {}", line);
                }
            }
            
        } catch (Exception e) {
            log.error("安装高德地图MCP包失败", e);
        }
    }
}

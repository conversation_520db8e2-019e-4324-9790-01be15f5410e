package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 用户信息表
 * @TableName user
 */
@TableName(value ="user")
@Data
public class User {
    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 登录账号（唯一）
     */
    private String userAccount;

    /**
     * 加密后的密码
     */
    private String userPassword;

    /**
     * 用户显示名称
     */
    private String userName;

    /**
     * 头像URL地址
     */
    private String userAvatar;

    /**
     * 用户角色：user/admin
     */
    private String userRole;

    /**
     * 最后编辑时间
     */
    private Date editTime;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 软删除标记 0-正常 1-删除
     */
    @TableLogic
    private Integer isDelete;
}
<template>
  <div class="contaienr">
    <div class="title">知识问答助手</div>
    <main>
      <div style="display: flex; width: 100%; justify-content: flex-end">
        <div
          class="btns"
          @click="router.push('/modelManage')">
          返回
        </div>
      </div>

      <div class="main-container">
        <el-form
          label-posistion="right"
          label-width="auto">
          <el-form-item label="数据集：">
            <el-input v-model="modelInfo.data" readonly></el-input>
          </el-form-item>
          <el-form-item label="训练模型：">
            <el-input v-model="modelInfo.model" readonly></el-input>
          </el-form-item>
          <el-form-item label="基础模型：">
            <el-input v-model="modelInfo.base_model" readonly></el-input>
          </el-form-item>
          <el-form-item label="训练进度：">
            <div class="process-container">
              <div class="process">
                <div
                  class="process-loaded"
                  :style="`width: ${Number(progress.percentage)}%`">
                  <div class="process-marker">
                    <div class="processs-marker-inner"></div>
                  </div>
                </div>
              </div>
              <div class="process-info">
                {{ Number(progress.percentage).toFixed(2) }}% | {{ progress.current }}/{{ progress.total }} 
                [{{ progress.time_elapsed }}&lt;{{ progress.time_remaining }}, {{ Number(progress.speed).toFixed(2) }}it/s]
              </div>
            </div>
          </el-form-item>
        </el-form>

        <div class="charts">
          <div
            ref="lostChartRef"
            style="width: 50%; height: 600px"></div>
          <div
            ref="gradNormChartRef"
            style="width: 50%; height: 600px"></div>
        </div>

        <!-- 修改合并权重按钮容器样式 -->
        <div style="display: flex; width: 100%; justify-content: flex-end; margin-top: 20px;">
          <div
            class="btns"
            @click="showMergeDialog"
            :class="{ 'disabled': !isTrainingCompleted }"
            :title="!isTrainingCompleted ? '训练完成后才能合并权重' : '合并模型权重'"
          >
            合并模型权重
          </div>
        </div>

        <!-- 添加合并权重弹窗 -->
        <el-dialog
          title="合并模型权重"
          v-model="mergeDialogVisible"
          width="30%"
        >
          <el-descriptions :column="1" border>
            <el-descriptions-item label="基础模型">{{ modelInfo.base_model }}</el-descriptions-item>
            <el-descriptions-item label="数据集">{{ modelInfo.data }}</el-descriptions-item>
          </el-descriptions>
          <el-divider></el-divider>
          <el-form :model="mergeForm" label-width="120px">
            <el-form-item label="模型版本">
              <el-input v-model="mergeForm.version" placeholder="请输入模型版本"></el-input>
            </el-form-item>
            <el-form-item label="模型描述">
              <el-input
                type="textarea"
                v-model="mergeForm.description"
                placeholder="请输入模型描述"
              ></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="mergeDialogVisible = false">取 消</el-button>
              <el-button type="primary" @click="confirmMerge">开始合并</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
    </main>
  </div>
</template>
<script setup>
  import { useRouter } from "vue-router";
  import * as Echarts from "echarts";
  import { onMounted, ref, onUnmounted } from "vue";
  import { ElMessage, ElLoading } from "element-plus";
  const router = useRouter();

  const lostChartRef = ref();
  const gradNormChartRef = ref();
  let lossChart = null;
  let gradNormChart = null;

  const modelInfo = ref({
    model: "",
    data: "",
    base_model: ""
  });

  // 存储训练数据
  const training_metrics = ref({
    loss: [],
    grad_norm: [],
    learning_rate: [],
    epoch: []
  });

  // 添加进度信息的响应式变量
  const progress = ref({
    percentage: 0,
    current: 0,
    total: 0,
    speed: 0,
    time_elapsed: '',
    time_remaining: ''
  });

  // 添加训练完成状态
  const isTrainingCompleted = ref(false);

  const mergeDialogVisible = ref(false)
  const mergeForm = ref({
    version: '',
    description: '',
  })

  // 创建损失图表
  const createLossChart = () => {
    lossChart = Echarts.init(lostChartRef.value);
    const option = {
      title: {
        text: '训练损失图',
        textStyle: {
          color: "#fff",
        },
        left: "center",
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'value',
        name: 'Epoch',
        axisLabel: {
          color: "#fff",
        },
      },
      yAxis: {
        type: 'value',
        name: 'Loss',
        axisLabel: {
          color: "#fff",
        },
      },
      series: [{
        name: 'Loss',
        type: 'line',
        data: [],
        lineStyle: {
          color: "#37b9f3",
        },
      }]
    };
    lossChart.setOption(option);
  };

  // 创建梯度范数图表
  const createGradNormChart = () => {
    gradNormChart = Echarts.init(gradNormChartRef.value);
    const option = {
      title: {
        text: '学习率曲线图',
        textStyle: {
          color: "#fff",
        },
        left: "center",
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'value',
        name: 'Epoch',
        axisLabel: {
          color: "#fff",
        },
      },
      yAxis: {
        type: 'value',
        name: 'Learning Rate',
        axisLabel: {
          color: "#fff",
        },
      },
      series: [{
        name: 'Gradient Norm',
        type: 'line',
        data: [],
        lineStyle: {
          color: "#f39837",
        },
      }]
    };
    gradNormChart.setOption(option);
  };

  // 修改更新函数
  const updateCharts = async () => {
    try {
      const response = await fetch('/dev-api/training-log');
      const logs = await response.json();
      
      if (!Array.isArray(logs)) {
        throw new Error('Invalid training log data');
      }

      // 获取最新的日志数据
      const latestLog = logs[logs.length - 1];
      
      // 更新进度信息
      progress.value = {
        percentage: latestLog.percentage,
        current: latestLog.current_steps,
        total: latestLog.total_steps,
        speed: latestLog.current_steps / parseFloat(latestLog.elapsed_time.split(':').reduce((acc, time) => (60 * acc) + +time, 0)),
        time_elapsed: latestLog.elapsed_time,
        time_remaining: latestLog.remaining_time
      };

      // 更新损失图表
      const lossData = logs.map(log => [log.epoch, log.loss]);
      lossChart.setOption({
        series: [{
          data: lossData
        }]
      });

      // 更新学习率图表（原来的梯度范数图表改为显示学习率）
      const lrData = logs.map(log => [log.epoch, log.lr]);
      gradNormChart.setOption({
        series: [{
          data: lrData
        }]
      });

      // 检查训练是否完成
      if (latestLog.current_steps >= latestLog.total_steps) {
        // 清除所有定时器
        if (updateInterval) {
          clearInterval(updateInterval.chart);
          clearInterval(updateInterval.info);
          updateInterval = null;
        }
        // 设置训练完成状态
        isTrainingCompleted.value = true;
        ElMessage({
          message: '训练已完成！',
          type: 'success'
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error fetching training log:', error);
      return false;
    }
  };

  // 修改加载历史数据的函数
  const loadAllTrainingData = async () => {
    try {
      const response = await fetch('/dev-api/training-log');
      const logs = await response.json();
      
      if (!Array.isArray(logs) || logs.length === 0) {
        return false;
      }

      // 获取最新的日志数据
      const latestLog = logs[logs.length - 1];
      
      // 更新进度信息
      progress.value = {
        percentage: latestLog.percentage,
        current: latestLog.current_steps,
        total: latestLog.total_steps,
        speed: latestLog.current_steps / parseFloat(latestLog.elapsed_time.split(':').reduce((acc, time) => (60 * acc) + +time, 0)),
        time_elapsed: latestLog.elapsed_time,
        time_remaining: latestLog.remaining_time
      };

      // 更新损失图表
      const lossData = logs.map(log => [log.epoch, log.loss]);
      lossChart.setOption({
        series: [{
          data: lossData
        }]
      });

      // 更新学习率图表
      const lrData = logs.map(log => [log.epoch, log.lr]);
      gradNormChart.setOption({
        series: [{
          data: lrData
        }]
      });

      // 检查训练是否完成
      const isCompleted = latestLog.current_steps >= latestLog.total_steps;
      isTrainingCompleted.value = isCompleted;
      return isCompleted;
    } catch (error) {
      console.error('Error loading training data:', error);
      return false;
    }
  };

  // 修改 modelInfo 的更新逻辑
  const updateModelInfo = async () => {
    try {
      const response = await fetch('/dev-api/current-training');
      if (response.ok) {
        const info = await response.json();
        modelInfo.value = {
          model: info.model,
          data: info.dataset,
          base_model: info.base_model || ""
        };
      }
    } catch (error) {
      console.error('Error fetching training info:', error);
    }
  };

  // 定时更新数据
  let updateInterval;

  onMounted(async () => {
    createLossChart();
    createGradNormChart();
    
    // 获取训练信息
    await updateModelInfo();
    
    // 先加载所有历史数据
    const isCompleted = await loadAllTrainingData();
    
    // 只有在训练未完成时才启动定时更新
    if (!isCompleted) {
      // 图表数据每秒更新
      const chartInterval = setInterval(async () => {
        // 如果 updateCharts 返回 true，表示训练完成，清除定时器
        const isFinished = await updateCharts();
        if (isFinished) {
          clearInterval(chartInterval);
          clearInterval(infoInterval);
          updateInterval = null;
        }
      }, 1000);
      
      // 训练信息每分钟更新
      const infoInterval = setInterval(updateModelInfo, 60000);
      
      // 保存定时器引用以便清理
      updateInterval = {
        chart: chartInterval,
        info: infoInterval
      };
    }
  });

  // 修改 onUnmounted 钩子，清理所有定时器
  onUnmounted(() => {
    // 清理定时器
    if (updateInterval) {
      clearInterval(updateInterval.chart);
      clearInterval(updateInterval.info);
    }
    // 销毁图表实例
    if (lossChart) {
      lossChart.dispose();
    }
    if (gradNormChart) {
      gradNormChart.dispose();
    }
  });

  // 修改显示合并对话框的函数
  const showMergeDialog = async () => {
    if (!isTrainingCompleted.value) {
      ElMessage.warning('请等待训练完成后再合并权重');
      return;
    }
    
    // 重置表单
    mergeForm.value = {
      version: '',
      description: '',
    };
    
    mergeDialogVisible.value = true;
  };

  // 修改确认合并的函数
  const confirmMerge = async () => {
    let loading = null;
    try {
      loading = ElLoading.service({
        lock: true,
        text: '正在合并模型权重...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const response = await fetch('/dev-api/merge-weights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mergeForm.value),
      });

      const result = await response.json();
      
      if (result.success) {
        // 先关闭加载状态
        if (loading) {
          loading.close();
          loading = null;
        }
        // 然后关闭对话框并显示成功消息
        mergeDialogVisible.value = false;
        ElMessage.success(result.message || '模型权重合并成功');
        // 最后进行路由跳转
        await router.push('/modelManage');
      } else {
        throw new Error(result.error || '模型权重合并失败');
      }
    } catch (error) {
      console.error('Error merging weights:', error);
      ElMessage.error(error.message || '模型权重合并失败');
      if (loading) {
        loading.close();
      }
    }
  };
</script>
<style scoped>
  .container {
    width: 100%;
    height: 100%;
  }
  .title {
    width: 100%;
    height: 120px;
    margin: 0 auto;
    text-align: center;
    color: #fff;
    font-size: 50px;

    font-family: YouShe, serif;
    background-image: url("/assets/images/nav-bar-bg.png");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url("/assets/images/button/主页按钮-正常.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }

  /* 修改禁用按钮的样式 */
  .btns.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    filter: grayscale(100%);
  }

  .btns.disabled:hover {
    filter: grayscale(100%);
  }

  .main-container {
    padding: 10px;
    background-color: #092762;
    border: 1px solid #3f63a8;
    margin: 10px;
  }

  .process {
    position: relative;
    width: 100%;
    height: 8px;
    background-color: #2d3e6c;
    border-radius: 4px;
    overflow: hidden;
  }

  .process-loaded {
    position: absolute;
    height: 100%;
    background: linear-gradient(to right, #1256b1, #8abfdf);
    transition: width 0.3s ease;
    min-width: 0%;
    max-width: 100%;
  }

  .process-marker {
    position: absolute;
    right: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: #fff;
    border-radius: 50%;
  }

  .processs-marker-inner {
    width: 8px;
    height: 8px;
    background-color: #fff;
    margin: 3.5px;
  }

  .charts {
    width: 100%;
    display: flex;
  }

  .process-container {
    width: 100%;
  }

  .process-info {
    margin-top: 8px;
    color: #fff;
    font-family: monospace;
    font-size: 14px;
  }
</style>

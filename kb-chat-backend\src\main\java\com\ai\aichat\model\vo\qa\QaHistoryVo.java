package com.ai.aichat.model.vo.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问答对历史响应VO
 */
@Data
@Schema(description = "问答对历史")
public class QaHistoryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问答库名称
     */
    @Schema(description = "问答库名称", example = "技术问答库")
    private String kbName;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称", example = "qa_data.json")
    private String fileName;

    /**
     * 问答对列表
     */
    @Schema(description = "问答对列表")
    private List<QaPairVo> qaPairs;

    /**
     * 问答对VO
     */
    @Data
    @Schema(description = "问答对信息")
    public static class QaPairVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 指令
         */
        @Schema(description = "指令")
        private String instruction;

        /**
         * 输入
         */
        @Schema(description = "输入")
        private String input;

        /**
         * 输出
         */
        @Schema(description = "输出")
        private String output;
    }
}

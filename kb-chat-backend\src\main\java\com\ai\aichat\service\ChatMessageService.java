package com.ai.aichat.service;

import com.ai.aichat.model.entity.ChatMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【chat_message(聊天消息明细表)】的数据库操作Service
* @createDate 2025-04-25 20:56:02
*/
public interface ChatMessageService extends IService<ChatMessage> {

    /**
     * 保存消息对（用户消息和助手回复）
     * @param conversationId 会话ID
     * @param userMessage 用户消息
     * @param assistantMessage 助手回复
     * @param sources 来源信息
     * @param graphData 图谱数据
     * @param qaPairData 问答对数据
     */
    void saveMessagePair(Long conversationId, String userMessage, String assistantMessage,
                        String sources, String graphData, String qaPairData);

    /**
     * 保存单条消息
     * @param conversationId 会话ID
     * @param role 角色（user/assistant）
     * @param content 消息内容
     * @param sources 来源信息
     * @param graphData 图谱数据
     * @param qaPairData 问答对数据
     */
    void saveMessage(Long conversationId, String role, String content, String sources, String graphData, String qaPairData);

    /**
     * 保存单条消息（向后兼容方法）
     * @param conversationId 会话ID
     * @param role 角色（user/assistant）
     * @param content 消息内容
     * @param sources 来源信息
     * @param graphData 图谱数据
     */
    default void saveMessage(Long conversationId, String role, String content, String sources, String graphData) {
        saveMessage(conversationId, role, content, sources, graphData, null);
    }

    /**
     * 根据会话ID获取消息列表
     * @param conversationId 会话ID
     * @return 消息列表
     */
    List<ChatMessage> getMessagesByConversationId(Long conversationId);
}

package com.ai.aichat.model.dto.knowledge;

import com.ai.aichat.model.dto.chat.ChatRequestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 知识库聊天请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识库聊天请求")
public class KbChatRequestDto extends ChatRequestDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", required = true, example = "default")
    private String kb_name;

    /**
     * 检索方式：keyword(关键词检索)、vector(向量检索)、hybrid(混合检索)
     */
    @Schema(description = "检索方式", example = "hybrid", allowableValues = {"keyword", "vector", "hybrid"})
    private String search_type = "hybrid";

    /**
     * 检索结果数量限制
     */
    @Schema(description = "检索结果数量限制", example = "5")
    private Integer top_k = 5;

    /**
     * 向量检索相似度阈值
     */
    @Schema(description = "向量检索相似度阈值", example = "0.7")
    private Double similarity_threshold = 0.7;

    /**
     * 是否启用问答对检索
     */
    @Schema(description = "是否启用问答对检索", example = "true")
    private Boolean enable_qa_pairs = true;

    /**
     * 是否生成知识图谱
     */
    @Schema(description = "是否生成知识图谱", example = "false")
    private Boolean enable_graph = false;

    /**
     * 温度参数，控制回答的随机性
     */
    @Schema(description = "温度参数，控制回答的随机性", example = "0.7")
    private Double temperature = 0.7;

    /**
     * top_p参数，控制回答的多样性
     */
    @Schema(description = "top_p参数，控制回答的多样性", example = "0.9")
    private Double top_p = 0.9;

    /**
     * 最大生成token数量
     */
    @Schema(description = "最大生成token数量", example = "2048")
    private Integer max_new_tokens = 2048;
}

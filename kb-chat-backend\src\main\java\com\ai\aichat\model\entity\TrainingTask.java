package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 训练任务实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("training_task")
@Schema(description = "训练任务")
public class TrainingTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "任务ID")
    private Long id;

    /**
     * 模型ID
     */
    @TableField("model_id")
    @Schema(description = "模型ID")
    private Long modelId;

    /**
     * 数据集名称
     */
    @TableField("dataset_name")
    @Schema(description = "数据集名称")
    private String datasetName;

    /**
     * 训练轮次
     */
    @TableField("epochs")
    @Schema(description = "训练轮次")
    private Integer epochs;

    /**
     * 学习率
     */
    @TableField("learning_rate")
    @Schema(description = "学习率")
    private BigDecimal learningRate;

    /**
     * 训练状态：0-待开始，1-训练中，2-训练完成，3-训练失败，4-已取消
     */
    @TableField("status")
    @Schema(description = "训练状态")
    private Integer status;

    /**
     * 当前轮次
     */
    @TableField("current_epoch")
    @Schema(description = "当前轮次")
    private Integer currentEpoch;

    /**
     * 训练损失
     */
    @TableField("loss")
    @Schema(description = "训练损失")
    private BigDecimal loss;

    /**
     * 训练精度
     */
    @TableField("accuracy")
    @Schema(description = "训练精度")
    private BigDecimal accuracy;

    /**
     * 训练进度（百分比）
     */
    @TableField("progress")
    @Schema(description = "训练进度")
    private BigDecimal progress;

    /**
     * 训练日志
     */
    @TableField("training_log")
    @Schema(description = "训练日志")
    private String trainingLog;

    /**
     * 错误信息
     */
    @TableField("error_message")
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 开始时间
     */
    @TableField("start_time")
    @Schema(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    @Schema(description = "是否删除")
    private Integer isDeleted;
}

package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 聊天消息明细表
 * @TableName chat_message
 */
@TableName(value ="chat_message")
@Data
public class ChatMessage {
    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的会话ID（外键）
     */
    private Long conversationId;

    /**
     * 消息角色（user/assistant）
     */
    private String role;

    /**
     * 消息内容正文
     */
    private String content;

    /**
     * 消息引用的来源列表
     */
    private String sources;

    /**
     * 关联的图谱数据（JSON格式）
     */
    private String graphData;

    /**
     * 问答对数据（JSON格式）
     */
    private String qaPairData;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 软删除标记 0-正常 1-删除
     */
    @TableLogic
    private Integer isDelete;
}
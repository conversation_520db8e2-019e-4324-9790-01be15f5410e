import request from '@/utils/request'

// 查询应用信息列表
export function listOpenAppInfo(query) {
  return request({
    url: '/open/openAppInfo/list',
    method: 'get',
    params: query
  })
}

// 查询应用信息详细
export function getOpenAppInfo(id) {
  return request({
    url: '/open/openAppInfo/' + id,
    method: 'get'
  })
}

// 新增应用信息
export function addOpenAppInfo(data) {
  return request({
    url: '/open/openAppInfo',
    method: 'post',
    data: data
  })
}

// 修改应用信息
export function updateOpenAppInfo(data) {
  return request({
    url: '/open/openAppInfo',
    method: 'put',
    data: data
  })
}

// 删除应用信息
export function delOpenAppInfo(id) {
  return request({
    url: '/open/openAppInfo/' + id,
    method: 'delete'
  })
}



// 获取UUID用户密钥
export function generatorUserKey() {
  return request({
    url: '/open/openAppInfo/generatorUserKey',
    method: 'get'
  })
}

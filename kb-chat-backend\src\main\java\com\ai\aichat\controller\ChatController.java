package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.model.dto.chat.ChatRequestDto;
import com.ai.aichat.model.dto.knowledge.KbChatRequestDto;
import com.ai.aichat.service.ChatStreamService;
import com.ai.aichat.service.KnowledgeBaseRagService;
import com.ai.aichat.util.ChatHistoryRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;


@Tag(name = "AI问答接口")
@RequiredArgsConstructor
@RestController
public class ChatController {

    private final ChatHistoryRepository chatHistoryRepository;
    private final ChatStreamService chatStreamService;
    private final KnowledgeBaseRagService knowledgeBaseRagService;

    @Operation(summary = "直接问答")
    @PostMapping(value = "/chat",produces = "text/html;charset=utf-8")
    public Flux<String> chat(String prompt, String chatId) {
        return chatStreamService.handleDirectChat(prompt, chatId);
    }

    @Operation(summary = "SSE问答")
    @PostMapping(value = "/chat/sse")
    public ResponseEntity<Flux<String>> chatSSE(@RequestBody ChatRequestDto chatRequestDto){
        ResponseEntity<Flux<String>> response = chatStreamService.handleSSEChat(chatRequestDto);
        return response;
    }

    @Operation(summary = "知识库问答（流式响应）")
    @PostMapping(value = "/kb_chat")
    public ResponseEntity<Flux<String>> kbChatStream(@RequestBody KbChatRequestDto kbChatRequestDto) {
        return knowledgeBaseRagService.handleKbChatStream(kbChatRequestDto);
    }

    @Autowired
    private ChatClient  chatClient;

    @Operation(summary = "知识库问答")
    @PostMapping(value = "/chat/rag")
    public BaseResponse<String> ragChat(String prompt) {
        ChatResponse chatResponse = chatClient
                .prompt(prompt)
                .call()
                .chatResponse();

        String content = chatResponse.getResult().getOutput().getText();
        return ResultUtils.success(content);
    }
}

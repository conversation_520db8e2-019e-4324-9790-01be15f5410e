package com.ai.aichat.mcp;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * MCP功能测试类
 * 测试MCP工具的实际功能
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
@DisplayName("MCP功能测试")
class McpFunctionalTest {

    @Autowired
    private ChatClient chatClient;

    @BeforeEach
    void setUp() {
        log.info("开始MCP功能测试");
    }

    @Test
    @DisplayName("测试高德地图天气查询功能")
    void testAmapWeatherQuery() {
        log.info("=== 测试高德地图天气查询功能 ===");
        
        try {
            String prompt = "请帮我查询北京的天气情况";
            
            log.info("发送请求: {}", prompt);
            
            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();
            
            log.info("AI响应: {}", response.getResult().getOutput().getText());
            
            // 检查是否调用了工具
            if (response.getMetadata() != null && response.getMetadata().getUsage() != null) {
                log.info("Token使用情况: {}", response.getMetadata().getUsage());
            }
            
        } catch (Exception e) {
            log.error("天气查询测试失败", e);
        }
    }

    @Test
    @DisplayName("测试高德地图地址解析功能")
    void testAmapGeocoding() {
        log.info("=== 测试高德地图地址解析功能 ===");
        
        try {
            String prompt = "请帮我查询'北京市朝阳区望京SOHO'的经纬度坐标";
            
            log.info("发送请求: {}", prompt);
            
            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();
            
            log.info("AI响应: {}", response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("地址解析测试失败", e);
        }
    }

    @Test
    @DisplayName("测试高德地图POI搜索功能")
    void testAmapPOISearch() {
        log.info("=== 测试高德地图POI搜索功能 ===");

        try {
            String prompt = "请帮我搜索北京市的星巴克咖啡店";

            log.info("发送请求: {}", prompt);

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();

            log.info("AI响应: {}", response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("POI搜索测试失败", e);
        }
    }

    @Test
    @DisplayName("测试高德地图路径规划功能")
    void testAmapRouting() {
        log.info("=== 测试高德地图路径规划功能 ===");

        try {
            String prompt = "请帮我规划从北京天安门到北京大学的驾车路线";

            log.info("发送请求: {}", prompt);

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();

            log.info("AI响应: {}", response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("路径规划测试失败", e);
        }
    }

    @Test
    @DisplayName("测试高德地图IP定位功能")
    void testAmapIPLocation() {
        log.info("=== 测试高德地图IP定位功能 ===");

        try {
            String prompt = "请帮我查询IP地址 ******* 的位置信息";

            log.info("发送请求: {}", prompt);

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();

            log.info("AI响应: {}", response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("IP定位测试失败", e);
        }
    }

    @Test
    @DisplayName("测试综合地图功能")
    void testComprehensiveMapFunction() {
        log.info("=== 测试综合地图功能 ===");

        try {
            String prompt = "我想了解北京的天气，然后找到天安门附近的餐厅，最后规划从天安门到北京大学的路线";

            log.info("发送请求: {}", prompt);

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();

            log.info("AI响应: {}", response.getResult().getOutput().getText());
            
        } catch (Exception e) {
            log.error("综合地图功能测试失败", e);
        }
    }
}

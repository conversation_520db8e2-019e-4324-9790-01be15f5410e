spring:
  application:
    name: ai-chat
  profiles:
    active: local  # 默认激活本地开发环境
  servlet:
    multipart:
      max-file-size: 100MB      # 单个文件最大大小
      max-request-size: 100MB   # 整个请求最大大小
      enabled: true
  ai:
    ollama:
      base-url: http://localhost:11435/
      chat:
        model: qwen3:14b
      embedding:
        model: bge-m3
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode/
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: qwen-max-2025-01-25
#           model: qwen3-235b-a22b
#          model: qwen3-32b
      embedding:
        options:
          model: text-embedding-v4
          dimensions: 1024
    mcp:
      client:
        enabled: true
        type: SYNC
        toolcallback:
          enabled: true
        stdio:
          servers-configuration: classpath:mcp-servers.json
  # 主数据源 - MySQL (业务数据)
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:localhost:3306}/${MYSQL_DB:ai_chat}
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:123456}
  # Redis
  data:
    redis:
      host: 127.0.0.1
      port: 16379
      password: 123321
      # 连接池配置
      lettuce:
        pool:
          max-active: 10
          max-idle: 10
          min-idle: 1
          time-between-eviction-runs: 10s
# milvus向量存储配置
milvus:
  host: 127.0.0.1
  port: 19530
logging:
  level:
    com.ai.aichat: debug

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启SQL日志
  global-config:
    db-config:
      logic-delete-field: isDelete # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
# searchApi
search-api:
  api-key: ${search-api-key}

# 文件存储配置
file:
  storage:
    knowledge-base-root: data/knowledge_bases
    temp-dir: data/temp
    vector-db-root: data/vector_db
    max-file-size: 52428800  # 50MB
    allowed-file-types:
      - .txt
      - .md
      - .pdf
      - .doc
      - .docx
      - .xls
      - .xlsx
      - .ppt
      - .pptx
      - .csv

# 应用配置
app:
  model:
    upload-path: data/models
    export-path: data/exports
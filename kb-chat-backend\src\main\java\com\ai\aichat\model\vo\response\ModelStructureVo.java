package com.ai.aichat.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 模型结构响应VO
 */
@Data
@Schema(description = "模型结构信息")
public class ModelStructureVo {

    /**
     * 模型ID
     */
    @Schema(description = "模型ID")
    private Long id;

    /**
     * 模型版本
     */
    @Schema(description = "模型版本")
    private String version;

    /**
     * 基础模型
     */
    @Schema(description = "基础模型")
    private String base_model;

    /**
     * 数据集
     */
    @Schema(description = "数据集")
    private String dataset;

    /**
     * 模型描述
     */
    @Schema(description = "模型描述")
    private String description;

    /**
     * 模型路径
     */
    @Schema(description = "模型路径")
    private String model_path;

    /**
     * 训练状态：0-未训练，1-训练中，2-训练完成，3-训练失败
     */
    @Schema(description = "训练状态")
    private Integer training_status;

    /**
     * 训练状态描述
     */
    @Schema(description = "训练状态描述")
    private String status_desc;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}

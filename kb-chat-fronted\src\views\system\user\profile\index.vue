<template>
   <div class="profile-container">
      <el-row :gutter="24">
         <el-col :span="8" :xs="24">
            <el-card class="profile-card" shadow="hover">
               <template v-slot:header>
                 <div class="card-header">
                   <el-icon class="header-icon"><User /></el-icon>
                   <span class="header-title">个人信息</span>
                 </div>
               </template>
               <div class="profile-info">
                  <div class="avatar-section">
                     <userAvatar :user="state.user" />
                     <h3 class="user-name">{{ state.user.userName || '未设置昵称' }}</h3>
                     <el-tag :type="state.user.userRole === 'admin' ? 'danger' : 'primary'" class="role-tag">
                       {{ state.user.userRole === 'admin' ? '管理员' : '普通用户' }}
                     </el-tag>
                  </div>
                  <div class="info-list">
                     <div class="info-item">
                        <el-icon class="info-icon"><User /></el-icon>
                        <span class="info-label">用户名称</span>
                        <span class="info-value">{{ state.user.userName || '未设置' }}</span>
                     </div>
                     <div class="info-item">
                        <el-icon class="info-icon"><Key /></el-icon>
                        <span class="info-label">登录账号</span>
                        <span class="info-value">{{ state.user.userAccount }}</span>
                     </div>
                     <div class="info-item">
                        <el-icon class="info-icon"><Edit /></el-icon>
                        <span class="info-label">用户简介</span>
                        <span class="info-value">{{ state.user.userProfile || '暂无简介' }}</span>
                     </div>
                     <div class="info-item">
                        <el-icon class="info-icon"><Calendar /></el-icon>
                        <span class="info-label">创建日期</span>
                        <span class="info-value">{{ formatDate(state.user.createTime) }}</span>
                     </div>
                  </div>
               </div>
            </el-card>
         </el-col>
         <el-col :span="16" :xs="24">
            <el-card class="settings-card" shadow="hover">
               <template v-slot:header>
                 <div class="card-header">
                   <el-icon class="header-icon"><Setting /></el-icon>
                   <span class="header-title">账户设置</span>
                 </div>
               </template>
               <el-tabs v-model="activeTab" class="profile-tabs">
                  <el-tab-pane name="userinfo">
                     <template #label>
                       <span class="tab-label">
                         <el-icon><Edit /></el-icon>
                         基本资料
                       </span>
                     </template>
                     <userInfo :user="state.user" @refresh="getUser" />
                  </el-tab-pane>
                  <el-tab-pane name="resetPwd">
                     <template #label>
                       <span class="tab-label">
                         <el-icon><Lock /></el-icon>
                         修改密码
                       </span>
                     </template>
                     <resetPwd />
                  </el-tab-pane>
               </el-tabs>
            </el-card>
         </el-col>
      </el-row>
   </div>
</template>

<script setup name="Profile">
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { getUserProfile } from "@/api/system/user";
import { User, Key, Edit, Calendar, Setting, Lock } from "@element-plus/icons-vue";

const activeTab = ref("userinfo");
const state = reactive({
  user: {}
});

function getUser() {
  getUserProfile().then(response => {
    if (response.code === 0) {
      state.user = response.data;
    }
  }).catch(error => {
    console.error('获取用户信息失败:', error);
  });
};

function formatDate(dateStr) {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN');
}

onMounted(() => {
  getUser();
});
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 84px);
}

.profile-card, .settings-card {
  border-radius: 12px;
  border: none;
  margin-bottom: 20px;

  :deep(.el-card__header) {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-bottom: 1px solid #e4e7ed;
    border-radius: 12px 12px 0 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .header-icon {
    margin-right: 8px;
    font-size: 18px;
    color: #409eff;
  }

  .header-title {
    font-size: 16px;
  }
}

.profile-info {
  padding: 20px 0;
}

.avatar-section {
  text-align: center;
  margin-bottom: 30px;

  .user-name {
    margin: 15px 0 10px 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
  }

  .role-tag {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 20px;
  }
}

.info-list {
  .info-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .info-icon {
      margin-right: 12px;
      color: #909399;
      font-size: 16px;
      width: 20px;
    }

    .info-label {
      color: #606266;
      font-weight: 500;
      min-width: 80px;
      margin-right: 15px;
    }

    .info-value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.profile-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 30px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background-color: #e4e7ed;
  }

  :deep(.el-tabs__item) {
    font-weight: 500;

    &.is-active {
      color: #409eff;
    }
  }

  .tab-label {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 6px;
    }
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }

  .profile-card {
    margin-bottom: 15px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start !important;

    .info-label {
      margin-bottom: 5px;
      margin-right: 0;
    }
  }
}
</style>

package com.ai.aichat.service;

import com.ai.aichat.model.dto.knowledge.KbChatRequestDto;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Flux;

/**
 * 知识库RAG问答服务接口
 */
public interface KnowledgeBaseRagService {

    /**
     * 知识库问答（流式响应）
     * @param prompt 用户问题
     * @param kbName 知识库名称
     * @param chatId 会话ID
     * @return 流式响应
     */
    Flux<String> kbChatStream(String prompt, String kbName, String chatId);

    /**
     * 知识库问答（非流式响应）
     * @param prompt 用户问题
     * @param kbName 知识库名称
     * @return 回答内容
     */
    String kbChat(String prompt, String kbName);

    /**
     * 处理知识库聊天流式请求
     * @param kbChatRequestDto 知识库聊天请求DTO
     * @return 响应实体
     */
    ResponseEntity<Flux<String>> handleKbChatStream(KbChatRequestDto kbChatRequestDto);
}

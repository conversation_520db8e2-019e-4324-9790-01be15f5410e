package com.ai.aichat.service;

import com.ai.aichat.model.dto.qa.*;
import com.ai.aichat.model.vo.qa.QaHistoryVo;
import com.ai.aichat.model.vo.qa.QaListVo;

/**
 * 问答对服务接口
 */
public interface QaService {

    /**
     * 获取问答库列表
     *
     * @return 问答库列表
     */
    QaListVo listQa();

    /**
     * 创建问答库
     *
     * @param dto 创建请求
     * @return 创建结果
     */
    String createQa(QaBaseCreateDto dto);

    /**
     * 更新问答库
     *
     * @param dto 更新请求
     * @return 更新结果
     */
    String updateQa(QaBaseUpdateDto dto);

    /**
     * 删除问答库
     *
     * @param dto 删除请求
     * @return 删除结果
     */
    String deleteQaBase(QaBaseDeleteDto dto);

    /**
     * 获取问答对历史
     *
     * @param name 问答库名称
     * @param fileName 文件名称
     * @return 问答对历史
     */
    QaHistoryVo getQaHistory(String name, String fileName);

    /**
     * 删除问答对文件
     *
     * @param name 问答库名称
     * @param fileName 文件名称
     * @return 删除结果
     */
    String deleteQa(String name, String fileName);

    /**
     * 保存问答对
     *
     * @param dto 保存请求
     * @return 保存结果
     */
    String saveChat(SaveChatDto dto);


}

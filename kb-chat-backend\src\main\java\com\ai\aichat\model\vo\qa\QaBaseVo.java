package com.ai.aichat.model.vo.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 问答库响应VO
 */
@Data
@Schema(description = "问答库信息")
public class QaBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问答库名称
     */
    @Schema(description = "问答库名称", example = "技术问答库")
    private String kbName;

    /**
     * 问答库描述
     */
    @Schema(description = "问答库描述", example = "存储技术相关的问答对")
    private String description;

    /**
     * 是否有问答对文件
     */
    @Schema(description = "是否有问答对文件", example = "true")
    private Boolean hasQaFiles;

    /**
     * 问答对文件列表
     */
    @Schema(description = "问答对文件列表")
    private List<String> qaFiles;

    /**
     * 最新文件日期
     */
    @Schema(description = "最新文件日期")
    private String latestFileDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;
}

<template>
  <div class="user-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <el-icon class="title-icon"><UserFilled /></el-icon>
          用户管理
        </h2>
        <p class="page-description">管理系统用户账户，包括用户信息的查看、编辑和权限设置</p>
      </div>
    </div>

    <!-- 搜索卡片 -->
    <el-card class="search-card" shadow="hover" v-show="showSearch">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Search /></el-icon>
          <span class="header-title">搜索筛选</span>
          <el-button
            type="text"
            @click="showSearch = !showSearch"
            class="collapse-btn">
            <el-icon><ArrowUp /></el-icon>
          </el-button>
        </div>
      </template>
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="用户名称" prop="userName">
              <el-input
                v-model="queryParams.userName"
                placeholder="请输入用户名称"
                clearable
                prefix-icon="User"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="登录账号" prop="userAccount">
              <el-input
                v-model="queryParams.userAccount"
                placeholder="请输入登录账号"
                clearable
                prefix-icon="Key"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户角色" prop="userRole">
              <el-select
                v-model="queryParams.userRole"
                placeholder="请选择用户角色"
                clearable
                style="width: 100%"
              >
                <el-option label="普通用户" value="user">
                  <span style="float: left">普通用户</span>
                  <el-tag size="small" type="primary" style="float: right">USER</el-tag>
                </el-option>
                <el-option label="管理员" value="admin">
                  <span style="float: left">管理员</span>
                  <el-tag size="small" type="danger" style="float: right">ADMIN</el-tag>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-actions">
            <el-button type="primary" @click="handleQuery" size="default">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetQuery" size="default">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="hover">
      <div class="toolbar-content">
        <div class="toolbar-left">
          <el-button
            type="primary"
            @click="handleAdd"
            size="default">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
          <el-button
            type="danger"
            :disabled="multiple"
            @click="handleDelete"
            size="default">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button
            type="success"
            @click="handleExport"
            size="default">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-tooltip content="刷新数据" placement="top">
            <el-button
              circle
              @click="getList"
              size="default">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top">
            <el-button
              circle
              @click="showSearch = !showSearch"
              size="default">
              <el-icon><Search /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        class="user-table"
        stripe
        border
        :header-cell-style="{ background: '#f8f9fa', color: '#495057' }">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="用户ID" align="center" prop="id" width="80" />
        <el-table-column label="登录账号" align="center" prop="userAccount" min-width="120">
          <template #default="scope">
            <div class="user-account">
              <el-icon class="account-icon"><Key /></el-icon>
              <span>{{ scope.row.userAccount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户名称" align="center" prop="userName" min-width="120">
          <template #default="scope">
            <div class="user-name">
              <el-avatar :size="32" :src="scope.row.userAvatar" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="name-text">{{ scope.row.userName || '未设置' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户简介" align="center" prop="userProfile" min-width="150" :show-overflow-tooltip="true">
          <template #default="scope">
            <span class="user-profile">{{ scope.row.userProfile || '暂无简介' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="用户角色" align="center" prop="userRole" width="120">
          <template #default="scope">
            <el-tag
              :type="scope.row.userRole === 'admin' ? 'danger' : 'primary'"
              size="default"
              effect="light">
              <el-icon class="role-icon">
                <component :is="scope.row.userRole === 'admin' ? 'Avatar' : 'User'" />
              </el-icon>
              {{ scope.row.userRole === 'admin' ? '管理员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <div class="create-time">
              <el-icon class="time-icon"><Clock /></el-icon>
              <span>{{ formatDate(scope.row.createTime) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleUpdate(scope.row)"
                :icon="Edit">
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
                v-if="scope.row.id !== 1"
                :icon="Delete">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </el-card>

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="userAccount">
              <el-input v-model="form.userAccount" placeholder="请输入登录账号" maxlength="50" :disabled="form.id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="用户简介" prop="userProfile">
              <el-input 
                v-model="form.userProfile" 
                type="textarea" 
                :rows="4"
                placeholder="请输入用户简介" 
                maxlength="500" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户角色" prop="userRole">
              <el-select v-model="form.userRole" placeholder="请选择用户角色">
                <el-option label="普通用户" value="user" />
                <el-option label="管理员" value="admin" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!form.id">
            <el-form-item label="用户密码" prop="userPassword">
              <el-input v-model="form.userPassword" placeholder="请输入用户密码" type="password" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { ref, reactive, onMounted, getCurrentInstance, toRefs } from "vue";
import { listUser, getUser, delUser, addUser, updateUser } from "@/api/system/user";
import Pagination from "@/components/Pagination/index.vue";
import {
  UserFilled,
  Search,
  ArrowUp,
  Plus,
  Delete,
  Download,
  Refresh,
  Key,
  User,
  Avatar,
  Clock,
  Edit
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";

const { proxy } = getCurrentInstance();

const userList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    userAccount: undefined,
    userRole: undefined
  },
  rules: {
    userAccount: [{ required: true, message: "登录账号不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
    userPassword: [{ required: true, message: "用户密码不能为空", trigger: "blur" }],
    userRole: [{ required: true, message: "用户角色不能为空", trigger: "change" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户列表 */
function getList() {
  loading.value = true;
  listUser(queryParams.value).then(response => {
    if (response.code === 0) {
      userList.value = response.data.records || [];
      total.value = response.data.total || 0;
    } else {
      userList.value = [];
      total.value = 0;
      proxy.$modal.msgError(response.message || '获取用户列表失败');
    }
  }).catch(error => {
    console.error('获取用户列表失败:', error);
    userList.value = [];
    total.value = 0;
    proxy.$modal.msgError('获取用户列表失败');
  }).finally(() => {
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.$refs.queryRef.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const userId = row.id || ids.value;
  form.value = { ...row };
  open.value = true;
  title.value = "修改用户";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateUser(form.value).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message || "修改失败");
          }
        }).catch(() => {
          proxy.$modal.msgError("修改失败");
        });
      } else {
        addUser(form.value).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message || "新增失败");
          }
        }).catch(() => {
          proxy.$modal.msgError("新增失败");
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.id || ids.value;
  const userName = row.userName || '选中的用户';
  proxy.$modal.confirm(`是否确认删除用户"${userName}"？`).then(function() {
    return delUser(userIds);
  }).then((response) => {
    if (response.code === 0) {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    } else {
      proxy.$modal.msgError(response.message || "删除失败");
    }
  }).catch(() => {
    proxy.$modal.msgError("删除失败");
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    userAccount: undefined,
    userName: undefined,
    userProfile: undefined,
    userRole: "user",
    userPassword: undefined
  };
  proxy.$refs.userRef?.resetFields();
}

/** 格式化日期 */
function formatDate(dateStr) {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN');
}

/** 导出数据 */
function handleExport() {
  ElMessage.info('导出功能开发中...');
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.user-management-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;

  .header-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30px;
    border-radius: 12px;
    color: white;

    .page-title {
      display: flex;
      align-items: center;
      margin: 0 0 10px 0;
      font-size: 28px;
      font-weight: 600;

      .title-icon {
        margin-right: 12px;
        font-size: 32px;
      }
    }

    .page-description {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
      line-height: 1.5;
    }
  }
}

.search-card, .toolbar-card, .table-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;

  :deep(.el-card__header) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .header-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 18px;
  }

  .header-title {
    font-weight: 600;
    color: #303133;
    font-size: 16px;
  }

  .collapse-btn {
    padding: 4px;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

.search-form {
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 8px;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      border-radius: 8px;
    }
  }
}

.search-actions {
  text-align: center;
  margin-top: 10px;

  .el-button {
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 500;

    + .el-button {
      margin-left: 12px;
    }
  }
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .toolbar-left {
    display: flex;
    gap: 12px;

    .el-button {
      border-radius: 8px;
      font-weight: 500;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 8px;

    .el-button {
      border-radius: 50%;
      width: 36px;
      height: 36px;
      padding: 0;
    }
  }
}

.user-table {
  :deep(.el-table__header) {
    th {
      background: #f8f9fa !important;
      color: #495057 !important;
      font-weight: 600;
      border-bottom: 2px solid #dee2e6;
    }
  }

  :deep(.el-table__row) {
    &:hover {
      background: #f8f9ff !important;
    }
  }
}

.user-account {
  display: flex;
  align-items: center;
  justify-content: center;

  .account-icon {
    margin-right: 6px;
    color: #909399;
  }
}

.user-name {
  display: flex;
  align-items: center;
  justify-content: center;

  .user-avatar {
    margin-right: 8px;
  }

  .name-text {
    font-weight: 500;
  }
}

.user-profile {
  color: #606266;
  font-style: italic;
}

.role-icon {
  margin-right: 4px;
}

.create-time {
  display: flex;
  align-items: center;
  justify-content: center;

  .time-icon {
    margin-right: 6px;
    color: #909399;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;

  .el-button {
    border-radius: 6px;
    font-weight: 500;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .user-management-container {
    padding: 10px;
  }

  .page-header .header-content {
    padding: 20px;

    .page-title {
      font-size: 24px;
    }

    .page-description {
      font-size: 14px;
    }
  }

  .toolbar-content {
    flex-direction: column;
    gap: 15px;

    .toolbar-left {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .search-form {
    :deep(.el-col) {
      margin-bottom: 10px;
    }
  }
}
</style>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登录账号" prop="userAccount">
        <el-input
          v-model="queryParams.userAccount"
          placeholder="请输入登录账号"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户角色" prop="userRole">
        <el-select
          v-model="queryParams.userRole"
          placeholder="用户角色"
          clearable
          style="width: 240px"
        >
          <el-option label="普通用户" value="user" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增用户</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除用户</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Search"
          @click="showSearch = !showSearch"
        >{{ showSearch ? '隐藏搜索' : '显示搜索' }}</el-button>
      </el-col>
    </el-row>

    <!-- 用户表格 -->
    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户ID" align="center" prop="id" width="80" />
      <el-table-column label="登录账号" align="center" prop="userAccount" :show-overflow-tooltip="true" />
      <el-table-column label="用户名称" align="center" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column label="用户简介" align="center" prop="userProfile" :show-overflow-tooltip="true" />
      <el-table-column label="用户角色" align="center" prop="userRole" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.userRole === 'admin' ? 'danger' : 'primary'">
            {{ scope.row.userRole === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ formatDate(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-if="scope.row.id !== 1">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="userAccount">
              <el-input v-model="form.userAccount" placeholder="请输入登录账号" maxlength="50" :disabled="form.id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="用户简介" prop="userProfile">
              <el-input 
                v-model="form.userProfile" 
                type="textarea" 
                :rows="4"
                placeholder="请输入用户简介" 
                maxlength="500" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户角色" prop="userRole">
              <el-select v-model="form.userRole" placeholder="请选择用户角色">
                <el-option label="普通用户" value="user" />
                <el-option label="管理员" value="admin" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!form.id">
            <el-form-item label="用户密码" prop="userPassword">
              <el-input v-model="form.userPassword" placeholder="请输入用户密码" type="password" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { listUser, getUser, delUser, addUser, updateUser } from "@/api/system/user";
import Pagination from "@/components/Pagination/index.vue";

const { proxy } = getCurrentInstance();

const userList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    current: 1,
    pageSize: 10,
    userName: undefined,
    userAccount: undefined,
    userRole: undefined
  },
  rules: {
    userAccount: [{ required: true, message: "登录账号不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
    userPassword: [{ required: true, message: "用户密码不能为空", trigger: "blur" }],
    userRole: [{ required: true, message: "用户角色不能为空", trigger: "change" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户列表 */
function getList() {
  loading.value = true;
  listUser(queryParams.value).then(response => {
    if (response.code === 0) {
      userList.value = response.data.records || [];
      total.value = response.data.total || 0;
    } else {
      userList.value = [];
      total.value = 0;
      proxy.$modal.msgError(response.message || '获取用户列表失败');
    }
  }).catch(error => {
    console.error('获取用户列表失败:', error);
    userList.value = [];
    total.value = 0;
    proxy.$modal.msgError('获取用户列表失败');
  }).finally(() => {
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.$refs.queryRef.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const userId = row.id || ids.value;
  form.value = { ...row };
  open.value = true;
  title.value = "修改用户";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateUser(form.value).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message || "修改失败");
          }
        }).catch(() => {
          proxy.$modal.msgError("修改失败");
        });
      } else {
        addUser(form.value).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.message || "新增失败");
          }
        }).catch(() => {
          proxy.$modal.msgError("新增失败");
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.id || ids.value;
  const userName = row.userName || '选中的用户';
  proxy.$modal.confirm(`是否确认删除用户"${userName}"？`).then(function() {
    return delUser(userIds);
  }).then((response) => {
    if (response.code === 0) {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    } else {
      proxy.$modal.msgError(response.message || "删除失败");
    }
  }).catch(() => {
    proxy.$modal.msgError("删除失败");
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    userAccount: undefined,
    userName: undefined,
    userProfile: undefined,
    userRole: "user",
    userPassword: undefined
  };
  proxy.$refs.userRef?.resetFields();
}

/** 格式化日期 */
function formatDate(dateStr) {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN');
}

getList();
</script>

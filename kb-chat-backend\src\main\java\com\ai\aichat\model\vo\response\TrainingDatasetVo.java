package com.ai.aichat.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 训练数据集响应VO
 */
@Data
@Schema(description = "训练数据集信息")
public class TrainingDatasetVo {

    /**
     * 数据集ID
     */
    @Schema(description = "数据集ID")
    private Long id;

    /**
     * 数据集名称
     */
    @Schema(description = "数据集名称")
    private String name;

    /**
     * 数据集描述
     */
    @Schema(description = "数据集描述")
    private String description;

    /**
     * 数据集文件路径
     */
    @Schema(description = "数据集文件路径")
    private String filePath;

    /**
     * 数据集大小（字节）
     */
    @Schema(description = "数据集大小")
    private Long fileSize;

    /**
     * 数据集大小（可读格式）
     */
    @Schema(description = "数据集大小（可读格式）")
    private String fileSizeReadable;

    /**
     * 数据条数
     */
    @Schema(description = "数据条数")
    private Integer dataCount;

    /**
     * 数据集状态：0-未处理，1-处理中，2-处理完成，3-处理失败
     */
    @Schema(description = "数据集状态")
    private Integer status;

    /**
     * 数据集状态描述
     */
    @Schema(description = "数据集状态描述")
    private String statusDesc;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}

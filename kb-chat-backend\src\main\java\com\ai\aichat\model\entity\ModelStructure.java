package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

/**
 * 模型结构实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("model_structure")
@Schema(description = "模型结构")
public class ModelStructure implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "模型ID")
    private Long id;

    /**
     * 模型版本
     */
    @TableField("version")
    @Schema(description = "模型版本")
    private String version;

    /**
     * 基础模型
     */
    @TableField("base_model")
    @Schema(description = "基础模型")
    private String baseModel;

    /**
     * 数据集
     */
    @TableField("dataset")
    @Schema(description = "数据集")
    private String dataset;

    /**
     * 模型描述
     */
    @TableField("description")
    @Schema(description = "模型描述")
    private String description;

    /**
     * 模型路径
     */
    @TableField("model_path")
    @Schema(description = "模型路径")
    private String modelPath;

    /**
     * 训练状态：0-未训练，1-训练中，2-训练完成，3-训练失败
     */
    @TableField("training_status")
    @Schema(description = "训练状态")
    private Integer trainingStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    @Schema(description = "是否删除")
    private Integer isDeleted;
}

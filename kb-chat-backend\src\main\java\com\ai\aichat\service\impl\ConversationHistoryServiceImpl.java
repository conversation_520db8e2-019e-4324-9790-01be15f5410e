package com.ai.aichat.service.impl;

import com.ai.aichat.model.entity.ChatMessage;
import com.ai.aichat.model.entity.Conversation;
import com.ai.aichat.model.vo.response.*;
import com.ai.aichat.service.ChatMessageService;
import com.ai.aichat.service.ConversationHistoryService;
import com.ai.aichat.service.ConversationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 会话历史服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConversationHistoryServiceImpl implements ConversationHistoryService {

    private final ConversationService conversationService;
    private final ChatMessageService chatMessageService;
    private final ObjectMapper objectMapper;

    @Override
    public ConversationListVo getConversations(Integer page, Integer pageSize, String chatType, String dateRange) {
        // 构建查询条件
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0); // 只查询未删除的会话

        // 根据聊天类型过滤
        if (chatType != null && !chatType.trim().isEmpty()) {
            if ("chat".equals(chatType)) {
                queryWrapper.eq("chat_type", "chat");
            } else if ("kb_chat".equals(chatType)) {
                queryWrapper.like("chat_type", "kb_chat");
            }
        }

        // 日期范围过滤（如果需要的话，这里可以扩展）
        // if (dateRange != null && !dateRange.trim().isEmpty()) {
        //     // 解析日期范围并添加条件
        // }

        // 按更新时间倒序排列
        queryWrapper.orderByDesc("update_time");

        // 分页查询
        Page<Conversation> pageRequest = new Page<>(page, pageSize);
        IPage<Conversation> conversationPage = conversationService.page(pageRequest, queryWrapper);

        // 转换为VO - 直接使用数据库字段，避免额外查询
        List<ConversationVo> conversationVos = conversationPage.getRecords().stream()
                .map(this::convertToConversationVo)
                .collect(Collectors.toList());

        // 构建分页信息
        ConversationListVo.PaginationVo pagination = new ConversationListVo.PaginationVo();
        pagination.setPage(page);
        pagination.setPage_size(pageSize);
        pagination.setTotal_records(conversationPage.getTotal());
        pagination.setTotal_conversations(conversationPage.getTotal()); // 前端兼容字段
        pagination.setTotal_pages((int) conversationPage.getPages());
        pagination.setHas_more(conversationPage.getCurrent() < conversationPage.getPages());

        ConversationListVo result = new ConversationListVo();
        result.setConversations(conversationVos);
        result.setPagination(pagination);
        return result;
    }

    @Override
    public ChatHistoryListVo getChatHistory(String sessionId) {
        // 根据sessionId查找会话
        QueryWrapper<Conversation> conversationQuery = new QueryWrapper<>();
        conversationQuery.eq("session_id", sessionId)
                        .eq("is_delete", 0);

        Conversation conversation = conversationService.getOne(conversationQuery);

        if (conversation == null) {
            log.warn("会话不存在: {}", sessionId);
            ConversationListVo.PaginationVo emptyPagination = new ConversationListVo.PaginationVo();
            emptyPagination.setPage(1);
            emptyPagination.setPage_size(0);
            emptyPagination.setTotal_records(0L);
            emptyPagination.setTotal_conversations(0L);
            emptyPagination.setTotal_pages(0);
            emptyPagination.setHas_more(false);
            return new ChatHistoryListVo(List.of(), emptyPagination);
        }

        // 获取该会话的所有消息
        List<ChatMessage> chatMessages = chatMessageService.getMessagesByConversationId(conversation.getId());

        // 转换为VO
        List<ChatHistoryVo> historyVos = chatMessages.stream()
                .map(message -> convertToChatHistoryVo(message, conversation))
                .collect(Collectors.toList());

        // 构建分页信息（历史记录通常一次性返回所有数据）
        ConversationListVo.PaginationVo pagination = new ConversationListVo.PaginationVo();
        pagination.setPage(1);
        pagination.setPage_size(historyVos.size());
        pagination.setTotal_records((long) historyVos.size());
        pagination.setTotal_conversations((long) historyVos.size());
        pagination.setTotal_pages(1);
        pagination.setHas_more(false);

        return new ChatHistoryListVo(historyVos, pagination);
    }

    @Override
    @Transactional
    public Boolean deleteConversation(String sessionId) {
        try {
            // 查找会话
            QueryWrapper<Conversation> conversationQuery = new QueryWrapper<>();
            conversationQuery.eq("session_id", sessionId)
                            .eq("is_delete", 0);

            Conversation conversation = conversationService.getOne(conversationQuery);

            if (conversation == null) {
                log.warn("要删除的会话不存在: {}", sessionId);
                return false;
            }

            // 软删除会话 - 使用MyBatis-Plus标准逻辑删除
            boolean conversationDeleted = conversationService.removeById(conversation.getId());

            // 软删除相关消息 - 使用MyBatis-Plus标准逻辑删除
            QueryWrapper<ChatMessage> messageQuery = new QueryWrapper<>();
            messageQuery.eq("conversation_id", conversation.getId())
                       .eq("is_delete", 0);

            List<ChatMessage> messages = chatMessageService.list(messageQuery);
            boolean messagesDeleted = true;
            for (ChatMessage message : messages) {
                boolean deleted = chatMessageService.removeById(message.getId());
                messagesDeleted = messagesDeleted && deleted;
            }

            log.info("删除会话成功: sessionId={}, conversationDeleted={}, messagesDeleted={}", 
                    sessionId, conversationDeleted, messagesDeleted);

            return conversationDeleted && messagesDeleted;
        } catch (Exception e) {
            log.error("删除会话失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public Boolean updateConversationTitle(String sessionId, String title) {
        try {
            // 查找会话
            QueryWrapper<Conversation> conversationQuery = new QueryWrapper<>();
            conversationQuery.eq("session_id", sessionId)
                            .eq("is_delete", 0);

            Conversation conversation = conversationService.getOne(conversationQuery);

            if (conversation == null) {
                log.warn("要更新标题的会话不存在: {}", sessionId);
                return false;
            }

            // 更新标题
            conversation.setTitle(title);
            boolean updated = conversationService.updateById(conversation);

            log.info("更新会话标题成功: sessionId={}, newTitle={}", sessionId, title);

            return updated;
        } catch (Exception e) {
            log.error("更新会话标题失败: sessionId={}, title={}", sessionId, title, e);
            return false;
        }
    }

    /**
     * 转换Conversation实体为ConversationVo
     */
    private ConversationVo convertToConversationVo(Conversation conversation) {
        ConversationVo vo = new ConversationVo();
        vo.setSession_id(conversation.getSessionId());
        vo.setChat_type(conversation.getChatType());
        vo.setKb_name(conversation.getKbName());
        vo.setCreate_time(conversation.getCreateTime());
        vo.setUpdate_time(conversation.getUpdateTime());

        // 直接使用数据库中的字段，避免额外查询
        vo.setTitle(conversation.getTitle() != null && !conversation.getTitle().trim().isEmpty()
                    ? conversation.getTitle() : "新对话");
        vo.setMessage_count(conversation.getMessageCount() != null ? conversation.getMessageCount() : 0);

        return vo;
    }

    /**
     * 转换ChatMessage实体为ChatHistoryVo
     */
    private ChatHistoryVo convertToChatHistoryVo(ChatMessage message, Conversation conversation) {
        ChatHistoryVo vo = new ChatHistoryVo();
        vo.setId(message.getId());
        vo.setRole(message.getRole());
        vo.setContent(message.getContent());
        vo.setGraph_data(message.getGraphData());
        vo.setQa_pair_data(message.getQaPairData());
        vo.setCreate_time(message.getCreateTime());
        vo.setSession_id(conversation.getSessionId());
        vo.setChat_type(conversation.getChatType());
        vo.setKb_name(conversation.getKbName());

        // 解析sources JSON字符串
        if (message.getSources() != null && !message.getSources().trim().isEmpty()) {
            try {
                List<ChatHistoryVo.SourceVo> sources = objectMapper.readValue(
                    message.getSources(), 
                    new TypeReference<List<ChatHistoryVo.SourceVo>>() {}
                );
                vo.setSources(sources);
            } catch (Exception e) {
                log.warn("解析消息sources失败: messageId={}", message.getId(), e);
                vo.setSources(List.of());
            }
        } else {
            vo.setSources(List.of());
        }

        return vo;
    }
}

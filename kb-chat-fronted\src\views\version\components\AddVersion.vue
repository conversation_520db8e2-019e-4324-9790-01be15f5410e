<template>
  <div>
    <el-dialog
      class="ask-dialog"
      v-model="addVersionShow">
      <template #header>
        <div class="header">添加新版本</div>
      </template>

      <el-form
        :model="formData"
        ref="formRef"
        :rules="rules"
        label-posistion="right"
        label-width="auto">
        <el-form-item
          label="版本号："
          prop="version">
          <el-input v-model="formData.version"></el-input>
        </el-form-item>
        <el-form-item
          label="更新内容："
          prop="info">
          <el-input
            v-model="formData.info"
            resize="none"
            type="textarea"
            rows="5"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <div
          class="btns"
          @click="submit(formRef)">
          确定
        </div>
        <div
          class="btns"
          @click="addVersionShow = false">
          取消
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
  import { useDialogStore } from "/src/store/dialog.js";
  import { storeToRefs } from "pinia";
  import { ref, reactive } from "vue";

  const { addVersionShow } = storeToRefs(useDialogStore());

  const formRef = ref();

  //表单数据
  const formData = ref({});

  //表单校验
  const rules = reactive({
    version: [{ required: true, message: "请输入内容", trigger: "blur" }],
    info: [{ required: false, message: "请输入内容", trigger: "blur" }],
  });

  //提交表单
  const submit = async (ref) => {
    if (!ref) {
      return;
    }

    await ref.validate(async (valid, fields) => {
      if (valid) {
        addVersionShow.value = false;
      } else {
        ElMessage({
          message: "表单格式错误！",
          type: "error",
        });
      }
    });
  };
</script>
<style scoped>
  .btns {
    display: inline-block;
    width: 140px;
    height: 30px;
    text-align: center;
    background-image: url("/assets/images/button/主页按钮-正常.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #b1c5da;
    font-size: 14px;
    line-height: 30px;
    margin-right: 10px;
  }

  .btns:hover {
    cursor: pointer;
    filter: contrast(150%) brightness(120%);
  }
</style>
<style>
  .ask-dialog {
    width: 600px;
  }

  .ask-dialog .el-dialog__header {
    margin-right: 0;
    background: linear-gradient(to right, #298bfd, #053477);
    color: #fff;
    padding: 10px;
  }

  .ask-dialog .el-dialog__headerbtn {
    top: -5px;
  }
</style>

package com.ai.aichat.model.vo.response;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

/**
 * 会话信息VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会话信息")
public class ConversationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "chat_123456")
    private String session_id;

    /**
     * 会话标题
     */
    @Schema(description = "会话标题", example = "关于AI的讨论")
    private String title;

    /**
     * 聊天类型
     */
    @Schema(description = "聊天类型", example = "chat")
    private String chat_type;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", example = "default")
    private String kb_name;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date create_time;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date update_time;

    /**
     * 消息数量
     */
    @Schema(description = "消息数量", example = "10")
    private Integer message_count;
}

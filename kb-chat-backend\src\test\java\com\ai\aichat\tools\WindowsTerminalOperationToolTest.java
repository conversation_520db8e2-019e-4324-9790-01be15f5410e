package com.ai.aichat.tools;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class WindowsTerminalOperationToolTest {
    @Test
    public void testExecuteTerminalCommand() {
        WindowsTerminalOperationTool tool = new WindowsTerminalOperationTool();
        String command = "dir";
        String result = tool.executeTerminalCommand(command);
        assertNotNull(result);
    }
}
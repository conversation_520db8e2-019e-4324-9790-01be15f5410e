import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/register'];

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        useUserStore().getInfo().then(() => {
          isRelogin.show = false
          // 检查路由权限
          if (to.meta && to.meta.roles) {
            const hasRole = useUserStore().roles.some(role => to.meta.roles.includes(role))
            if (hasRole) {
              next()
            } else {
              next({ path: '/401' })
            }
          } else {
            next()
          }
        }).catch(err => {
          // 获取用户信息失败，清除本地状态
          useUserStore().clearUserInfo();
          console.error('获取用户信息失败:', err);
          next({ path: '/login' });
        })
      } else {
        // 检查路由权限
        if (to.meta && to.meta.roles) {
          const hasRole = useUserStore().roles.some(role => to.meta.roles.includes(role))
          if (hasRole) {
            next()
          } else {
            next({ path: '/401' })
          }
        } else {
          next()
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()

})

<template>
  <div>
    <!-- New Dialog for Knowledge Base Lineage -->
    <el-dialog
      v-model="lineageDialogShow"
      title="知识库谱系化"
      width="80%"
      @open="fetchAndRenderLineage"
      @close="handleLineageDialogClose">
      <div v-loading="loadingGraphData">
        <div
          class="drawBoard"
          ref="graphContainer"></div>
      </div>
       <!-- CRUD Operations Placeholder -->
      <div style="margin-top: 20px;">
        <h3>知识库谱系操作</h3>
        <el-button type="primary" @click="openAddNodeDialog">添加知识库节点</el-button>
        <el-button type="warning" @click="openEditNodeDialog" :disabled="!selectedItem || selectedItem.source">修改节点</el-button>
        <el-button type="danger" @click="confirmDeleteNode" :disabled="!selectedItem || selectedItem.source">删除节点</el-button>
         <el-button type="info" @click="openLinkNodesDialog" :disabled="!selectedItem || selectedItem.source">连接节点</el-button>

        <!-- Add Node Dialog -->
        <el-dialog
          v-model="addNodeDialogShow"
          title="添加知识库节点"
          width="30%"
          append-to-body>
          <el-form :model="newNodeForm">
            <el-form-item label="节点名称">
              <el-input v-model="newNodeForm.label"></el-input>
            </el-form-item>
            <el-form-item label="描述">
              <el-input type="textarea" v-model="newNodeForm.description"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="addNodeDialogShow = false">取消</el-button>
              <el-button type="primary" @click="addKnowledgeBaseNode">确定</el-button>
            </span>
          </template>
        </el-dialog>

        <!-- Edit Node Dialog -->
        <el-dialog
          v-model="editNodeDialogShow"
          title="修改知识库节点"
          width="30%"
          append-to-body>
          <el-form :model="editNodeForm">
            <el-form-item label="节点名称">
              <el-input v-model="editNodeForm.label"></el-input>
            </el-form-item>
             <el-form-item label="描述">
              <el-input type="textarea" v-model="editNodeForm.description"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="editNodeDialogShow = false">取消</el-button>
              <el-button type="primary" @click="editKnowledgeBaseNode">确定</el-button>
            </span>
          </template>
        </el-dialog>

         <!-- Link Nodes Dialog -->
        <el-dialog
          v-model="linkNodesDialogShow"
          title="连接知识库节点"
          width="30%"
          append-to-body>
          <el-form :model="linkNodesForm">
            <el-form-item label="来源节点 (ID)">
              <el-input v-model="linkNodesForm.from"></el-input>
            </el-form-item>
            <el-form-item label="目标节点 (ID)">
              <el-input v-model="linkNodesForm.to"></el-input>
            </el-form-item>
            <el-form-item label="关系标签">
              <el-input v-model="linkNodesForm.label"></el-input>
            </el-form-item>
           <el-form-item label="关系描述">
              <el-input type="textarea" v-model="linkNodesForm.description"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="linkNodesDialogShow = false">取消</el-button>
              <el-button type="primary" @click="linkKnowledgeBases">确定</el-button>
            </span>
          </template>
        </el-dialog>

      </div>
    </el-dialog>

   <!-- Node/Edge Detail Dialog (Copied from KnowledgeGraph.vue) -->
  <el-dialog
    v-model="detailDialogVisible"
    :title="detailTitle"
    width="40%"
    append-to-body>
    <div v-if="selectedItem">
      <p><strong>ID:</strong> {{ selectedItem.id }}</p>
      <p><strong>标签:</strong> {{ selectedItem.label }}</p>
      <div v-if="selectedItem.description">
        <p><strong>描述:</strong> {{ selectedItem.description }}</p>
      </div>
      <div v-if="selectedItem.entity_type">
         <p><strong>类型:</strong> {{ selectedItem.entity_type }}</p>
      </div>
      <div v-if="selectedItem.source">
        <p><strong>来源节点:</strong> {{ selectedItem.source }}</p>
        <p><strong>目标节点:</strong> {{ selectedItem.target }}</p>
      </div>
      <div v-if="selectedItem.weight">
        <p><strong>权重:</strong> {{ selectedItem.weight }}</p>
      </div>
       <div v-if="selectedItem.keywords">
        <p><strong>关键词:</strong> {{ selectedItem.keywords }}</p>
      </div>
      <!-- Add more details as needed -->
    </div>
  </el-dialog>

  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch, nextTick } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { useDialogStore } from "/src/store/dialog.js";
  import { storeToRefs } from "pinia";
  // 导入 Vis.js 库
  import { Network } from "vis-network/standalone/umd/vis-network.min.js";

  const dialogStore = useDialogStore();
  const { lineageDialogShow } = storeToRefs(dialogStore);


  // Added for graph visualization
  const graphContainer = ref(null);
  const currentNodesData = ref([]);
  const currentEdgesData = ref([]);
  const loadingGraphData = ref(false);

  // Added for detail dialog
  const detailDialogVisible = ref(false);
  const selectedItem = ref(null);
  const detailTitle = ref('');

   // Added for CRUD operations
  const addNodeDialogShow = ref(false);
  const newNodeForm = ref({});
  const editNodeDialogShow = ref(false);
  const editNodeForm = ref({});
  const linkNodesDialogShow = ref(false);
  const linkNodesForm = ref({});

   // Fetch lineage data from backend (Placeholder)
  const fetchLineageData = async () => {
    console.log('Fetching lineage data...');
    // TODO: Replace with actual API call to get knowledge base graph data
    // This API should return nodes (knowledge bases) and edges (relationships)
    // Example placeholder data:
     const nodesData = [
      { "id": 1, "label": "海上无人作战知识库", "description": "海上无人作战相关知识库" },
      { "id": 2, "label": "网络信息知识库", "description": "网络信息相关知识库" },
      { "id": 3, "label": "法规政策知识库", "description": "法规制度相关知识库" },
      { "id": 4, "label": "作战概念理论成果知识库", "description": "作战概念理论成果相关知识库" },
      { "id": 5, "label": "训练方案-态势分析知识库", "description": "训练方案-态势分析相关知识库" },
      { "id": 6, "label": "武器装备知识库", "description": "武器装备相关知识库" },
      { "id": 7, "label": "军事历史知识库", "description": "军事历史知识相关知识库" },
      { "id": 8, "label": "武器装备", "description": "装备知识咨询知识库" },
      { "id": 9, "label": "情报咨询", "description": "情报咨询知识库" },
      { "id": 10, "label": "法规制度", "description": "法规政策咨询知识库" }
    ];
     const edgesData = [
      { "id": "e1-4", "from": 1, "to": 4, "label": "关联", "description": "海上无人作战与作战概念理论关联" },
      { "id": "e6-1", "from": 6, "to": 1, "label": "支持", "description": "武器装备支持海上无人作战" },
      { "id": "e3-10", "from": 3, "to": 10, "label": "是基础", "description": "法规制度是法规政策咨询的基础" },
      { "id": "e9-2", "from": 9, "to": 2, "label": "依赖于", "description": "情报咨询依赖于网络信息" },
      { "id": "e5-4", "from": 5, "to": 4, "label": "应用", "description": "训练方案应用作战概念" },
      { "id": "e8-6", "from": 8, "to": 6, "label": "基于", "description": "装备知识咨询基于武器装备信息" },
      { "id": "e1-2", "from": 1, "to": 2, "label": "相关", "description": "海上无人作战与网络信息相关" },
      { "id": "e4-7", "from": 4, "to": 7, "label": "借鉴", "description": "作战概念借鉴军事历史知识" }
    ];
     return { nodes: nodesData, edges: edgesData };
  };

  // Fetch and render lineage data
  const fetchAndRenderLineage = async () => {
     loadingGraphData.value = true;
     try {
        const data = await fetchLineageData(); // Call the placeholder fetching function
        currentNodesData.value = data.nodes || [];
        currentEdgesData.value = data.edges || [];

        nextTick(() => {
          createGraph(currentNodesData.value, currentEdgesData.value);
        });
     } catch (error) {
        console.error('Failed to fetch lineage data:', error);
        ElMessage.error('获取知识库谱系数据失败');
     } finally {
       loadingGraphData.value = false;
     }
  };

  // Create the Vis.js graph (Copied and adapted from KnowledgeGraph.vue)
  const createGraph = (nodesData, edgesData) => {
    const nodeConnections = {};
    edgesData.forEach((edge) => {
      nodeConnections[edge.from] = (nodeConnections[edge.from] || 0) + 1;
      nodeConnections[edge.to] = (nodeConnections[edge.to] || 0) + 1;
    });

    const updatedNodesData = nodesData.map((node) => ({
      ...node,
      size: (nodeConnections[node.id] || 0) * 5 + 10,
      label: node.label,
    }));

    const data = {
      nodes: updatedNodesData,
      edges: edgesData,
    };

    const options = {
      height: "700px",
      width: "100%",
      nodes: {
        shape: "dot",
        font: {
          color: "#ffffff",
        },
      },
      edges: {
        smooth: { type: "continuous" },
        width: 2,
        color: "#848484",
        arrows: 'to', // Assuming directed graph
      },
      physics: {
        enabled: true,
      },
      interaction: {
        navigationButtons: true,
        keyboard: true
      },
    };

    const container = graphContainer.value;
    if (!container) {
      console.error("Graph container not found");
      return;
    }
    container.innerHTML = ''; // Clear previous graph

     // Map edge data to Vis.js format (source -> from, target -> to)
    const mappedEdgesData = edgesData.map(edge => ({
      id: edge.id,
      from: edge.source || edge.from, // Use source or from
      to: edge.target || edge.to,     // Use target or to
      label: edge.label || edge.description || '', // Use label or description as edge label
      title: edge.description || edge.label || '', // Use description or label as hover title
      weight: edge.weight,
      keywords: edge.keywords,
      source_id: edge.source_id,
      arrows: 'to', // Assuming directed graph
      // Copy other relevant edge properties if any
      ...edge
    }));

    const network = new Network(container, {
      nodes: updatedNodesData,
      edges: mappedEdgesData, // Use the mapped edge data
    }, options);

    // Add click event listener to nodes and edges
    network.on("click", function (params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            const node = updatedNodesData.find(n => n.id === nodeId);
            if (node) {
                selectedItem.value = node;
                detailTitle.value = `节点详情: ${node.label}`;
                detailDialogVisible.value = true;
            }
        } else if (params.edges.length > 0) {
            const edgeId = params.edges[0];
            // Find the original edge data (before mapping to Vis.js format)
            const edge = edgesData.find(e => e.id === edgeId);
             if (edge) {
                selectedItem.value = edge;
                detailTitle.value = `关系详情: ${edge.label || edge.id}`;
                detailDialogVisible.value = true;
            }
        }
    });
  };

  // Handle lineage dialog close
  const handleLineageDialogClose = () => {
    // Clear graph data when dialog is closed
    currentNodesData.value = [];
    currentEdgesData.value = [];
    selectedItem.value = null;
    if (graphContainer.value) {
      graphContainer.value.innerHTML = ''; // Clear the graph from the container
    }
  };

  // CRUD Operation Functions

  const openAddNodeDialog = () => {
    newNodeForm.value = {}; // Clear form
    addNodeDialogShow.value = true;
  };

  const addKnowledgeBaseNode = async () => {
    console.log('Adding node:', newNodeForm.value);
    // Use the same API as LocalLoad.vue for adding knowledge base
    try {
      const response = await fetch('/dev-api/create_knowledge_base', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          kb_name: newNodeForm.value.label,
          description: newNodeForm.value.description || ''
        })
      });

      const result = await response.json();

      if (result.code !== 1) {
        throw new Error(result.msg || '添加知识库失败');
      }

      ElMessage.success(result.msg || '知识库添加成功');
      addNodeDialogShow.value = false;
      await fetchAndRenderLineage(); // Refresh graph after adding
      // TODO: Notify LocalLoad.vue to refresh its list

    } catch (error) {
      console.error('添加知识库失败:', error);
      ElMessage.error(error.message || '添加知识库失败');
    }
  };

   const openEditNodeDialog = () => {
     // Open edit dialog only if a node is selected
     if (selectedItem.value && !selectedItem.value.source) {
        editNodeForm.value = { ...selectedItem.value }; // Populate form with selected node data
        editNodeDialogShow.value = true;
     }
   };

  const editKnowledgeBaseNode = async () => {
    console.log('Editing node:', editNodeForm.value);
    // Use the same API as LocalLoad.vue for editing knowledge base
    try {
      let submitData = {
        kb_name: selectedItem.value.label, // Original knowledge base name
        description: editNodeForm.value.description || '' // New description
      };

      // Only add new_kb_name if the label has changed
      if (editNodeForm.value.label !== selectedItem.value.label) {
         // Basic validation for new name
        if (!editNodeForm.value.label || editNodeForm.value.label.trim() === '') {
           ElMessage.error('知识库名称不能为空');
           return;
         }
        submitData.new_kb_name = editNodeForm.value.label;
      }

      const response = await fetch('/dev-api/update_knowledge_base', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      const result = await response.json();

       if (result.code !== 1) {
        throw new Error(result.msg || '修改知识库失败');
      }

      ElMessage.success(result.msg || '知识库修改成功');
      editNodeDialogShow.value = false;
      await fetchAndRenderLineage(); // Refresh graph after editing
      // TODO: Notify LocalLoad.vue to refresh its list

    } catch (error) {
      console.error('修改知识库失败:', error);
      ElMessage.error(error.message || '修改知识库失败');
    }
  };

  const confirmDeleteNode = () => {
     if (selectedItem.value && !selectedItem.value.source) { // Only allow deleting nodes
        ElMessageBox.confirm(
          `确定要删除知识库节点 "${selectedItem.value.label}" 吗？`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          deleteKnowledgeBaseNode(selectedItem.value.id, selectedItem.value.label); // Pass ID and label for API call
        }).catch(() => {
          // User canceled deletion
          ElMessage.info('删除已取消');
        });
     }
  };

  const deleteKnowledgeBaseNode = async (nodeId, nodeLabel) => {
    console.log('Deleting node with ID:', nodeId, 'and label:', nodeLabel);
    // Use the same API as LocalLoad.vue for deleting knowledge base
    try {
      const response = await fetch(
        "/dev-api/delete_knowledge_base",
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            kb_name: nodeLabel, // Use the knowledge base name for deletion
          }),
        }
      );

      const data = await response.json();
      if (data.code !== 1) {
        throw new Error(data.msg || "删除失败");
      }

      ElMessage.success(data.msg || "删除成功");
      selectedItem.value = null; // Deselect item after deletion
      await fetchAndRenderLineage(); // Refresh graph after deletion
      // TODO: Notify LocalLoad.vue to refresh its list

    } catch (error) {
       console.error("删除知识库失败：", error);
       ElMessage.error("删除知识库失败：" + error.message);
    }
  };

   const openLinkNodesDialog = () => {
      if (selectedItem.value && !selectedItem.value.source) { // Pre-fill 'from' if a node is selected
         linkNodesForm.value = { from: selectedItem.value.id, to: '', label: '', description: '' };
      } else {
         linkNodesForm.value = { from: '', to: '', label: '', description: '' };
      }
      linkNodesDialogShow.value = true;
   };

  const linkKnowledgeBases = async () => {
    console.log('Linking nodes:', linkNodesForm.value);
    // TODO: Implement API call to create a link (edge) between knowledge bases
    // The API should accept from_node_id, to_node_id, relationship_label, relationship_description
    // You will need a new backend API for creating relationships.
    // After successful API call, refetch and render the graph:
    // await fetchAndRenderLineage();
    linkNodesDialogShow.value = false;
    ElMessage.success('连接节点功能待实现');
  };

</script>

<style scoped>
/* Styles for the visualization dialog content (Copied from KnowledgeGraph.vue) */
  .drawBoard {
    width: 100%;
    height: 700px; /* Fixed height for the graph area */
    background-color: #011b53;
    border: none;
    box-sizing: border-box;
  }

/* Add some basic styling for the detail dialog content */
/* These styles should probably be global or in a shared file, but adding here for now */
</style>

<style>
/* Add some basic styling for the detail dialog content */
.el-dialog__body strong {
  color: #13fff3;
}
.el-dialog__body p {
  margin-bottom: 8px;
  color: #ccc;
}
</style> 
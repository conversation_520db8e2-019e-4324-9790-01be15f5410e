package com.ai.aichat.model.vo.response;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 知识库文件信息VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "知识库文件信息")
public class KnowledgeBaseFileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "1")
    private Long id;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", example = "default")
    private String kb_name;

    /**
     * 文件名（显示名称）
     */
    @Schema(description = "文件名", example = "我的文档.pdf")
    private String name;

    /**
     * 文件唯一标识名
     */
    @Schema(description = "文件唯一标识名", example = "uuid123.pdf")
    private String file_name;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小", example = "1048576")
    private Long size;

    /**
     * 文件大小（格式化）
     */
    @Schema(description = "文件大小（格式化）", example = "1.0 MB")
    private String file_size_formatted;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型", example = ".pdf")
    private String file_type;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态", example = "completed")
    private String status;

    /**
     * 处理进度
     */
    @Schema(description = "处理进度", example = "100")
    private Integer progress;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String error_message;

    /**
     * 创建时间（时间戳）
     */
    @Schema(description = "创建时间", example = "2025-06-08 10:30:00")
    private String upload_time;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-06-08 15:45:00")
    private String updated_at;
}

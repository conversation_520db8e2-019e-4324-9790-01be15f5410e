package com.ai.aichat.controller;

import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.model.vo.response.KnowledgeBaseVo;
import com.ai.aichat.service.KnowledgeBaseService;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.common.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "知识库管理", description = "知识库相关接口")
@RequiredArgsConstructor
@RestController
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;

    @Operation(summary = "获取知识库名称列表（知识库问答时）")
    @GetMapping("/list_knowledge_bases")
    public BaseResponse<List<String>> listKnowledgeBases() {
        List<String> knowledgeBaseNames = knowledgeBaseService.listKnowledgeBaseNames();
        return ResultUtils.success(knowledgeBaseNames);
    }

    @Operation(summary = "获取所有知识库的详细信息")
    @GetMapping("/get_all_knowledge_bases")
    public BaseResponse<List<KnowledgeBaseVo>> getAllKnowledgeBases() {
        List<KnowledgeBaseVo> knowledgeBases = knowledgeBaseService.getAllKnowledgeBases();
        return ResultUtils.success(knowledgeBases);
    }

    @Operation(summary = "创建知识库")
    @PostMapping("/create_knowledge_base")
    public BaseResponse<Boolean> createKnowledgeBase(@RequestBody Map<String, Object> request) {
            String kbName = (String) request.get("kb_name");
            String description = (String) request.get("description");

            log.info("创建知识库: {}, 描述: {}", kbName, description);
        Boolean b = knowledgeBaseService.createKnowledgeBase(kbName.trim(), description);
        return ResultUtils.success(b);
    }

    @Operation(summary = "删除知识库")
    @DeleteMapping("/delete_knowledge_base/{id}")
    @Transactional
    public BaseResponse<Boolean> deleteKnowledgeBase(@PathVariable("id") Long id) {
        ThrowUtils.throwIf(id == null, ErrorCode.PARAMS_ERROR, "知识库ID不能为空");
        Boolean b = knowledgeBaseService.deleteKnowledgeBase(id);
        return ResultUtils.success(b);
    }

    @Operation(summary = "更新知识库")
    @PutMapping("/update_knowledge_base")
    @Transactional
    public BaseResponse<Boolean> updateKnowledgeBase(@RequestBody Map<String, Object> request) {
        Long kbId = request.get("kb_id") != null ? Long.parseLong(request.get("kb_id").toString()) : null;
        ThrowUtils.throwIf(kbId == null, ErrorCode.PARAMS_ERROR, "知识库ID不能为空");

        String newKbName = (String) request.get("new_kb_name");
        String description = (String) request.get("description");

        Boolean result = knowledgeBaseService.updateKnowledgeBase(kbId, newKbName, description);
        return ResultUtils.success(result);
    }
}

package com.ai.aichat.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件工具类
 */
@Slf4j
public class FileUtils {

    /**
     * 支持的文件类型
     */
    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList(
        ".txt", ".md", ".pdf", ".doc", ".docx", 
        ".xls", ".xlsx", ".ppt", ".pptx", ".csv"
    );

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex).toLowerCase();
    }

    /**
     * 检查文件类型是否支持
     */
    public static boolean isAllowedFileType(String fileName) {
        String extension = getFileExtension(fileName);
        return ALLOWED_FILE_TYPES.contains(extension);
    }

    /**
     * 生成唯一文件名
     */
    public static String generateUniqueFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + extension;
    }

    /**
     * 计算文件MD5值
     */
    public static String calculateMD5(MultipartFile file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = file.getBytes();
            byte[] digest = md.digest(bytes);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算文件MD5失败", e);
            return null;
        }
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        DecimalFormat df = new DecimalFormat("#,##0.#");
        return df.format(size / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    /**
     * 保存文件到指定路径
     */
    public static void saveFile(MultipartFile file, String filePath) throws IOException {
        Path path = Paths.get(filePath);
        
        // 创建目录（如果不存在）
        Files.createDirectories(path.getParent());
        
        // 保存文件
        file.transferTo(path.toFile());
    }

    /**
     * 删除文件
     */
    public static boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            return Files.deleteIfExists(path);
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     */
    public static boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    /**
     * 获取文件大小
     */
    public static long getFileSize(String filePath) {
        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", filePath, e);
            return 0;
        }
    }

    /**
     * 验证文件名安全性
     */
    public static boolean isValidFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含非法字符
        String[] illegalChars = {"/", "\\", ":", "*", "?", "\"", "<", ">", "|"};
        for (String illegalChar : illegalChars) {
            if (fileName.contains(illegalChar)) {
                return false;
            }
        }
        
        // 检查文件名长度
        return fileName.length() <= 255;
    }

    /**
     * 清理文件名（移除非法字符）
     */
    public static String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "";
        }
        
        // 替换非法字符为下划线
        String sanitized = fileName.replaceAll("[/\\\\:*?\"<>|]", "_");
        
        // 限制长度
        if (sanitized.length() > 255) {
            String extension = getFileExtension(sanitized);
            String nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
            sanitized = nameWithoutExt.substring(0, 255 - extension.length()) + extension;
        }
        
        return sanitized;
    }

    /**
     * 读取文本文件内容
     */
    public static String readFileContent(String filePath) {
        try {
            return Files.readString(Paths.get(filePath));
        } catch (IOException e) {
            log.error("读取文件内容失败: {}", filePath, e);
            return "";
        }
    }
}

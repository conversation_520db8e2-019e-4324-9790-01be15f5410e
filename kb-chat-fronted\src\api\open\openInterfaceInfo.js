import request from '@/utils/request'

// 查询接口信息列表
export function listOpenInterfaceInfo(query) {
  return request({
    url: '/open/openInterfaceInfo/list',
    method: 'get',
    params: query
  })
}

// 查询接口信息详细
export function getOpenInterfaceInfo(id) {
  return request({
    url: '/open/openInterfaceInfo/' + id,
    method: 'get'
  })
}

// 新增接口信息
export function addOpenInterfaceInfo(data) {
  return request({
    url: '/open/openInterfaceInfo',
    method: 'post',
    data: data
  })
}

// 修改接口信息
export function updateOpenInterfaceInfo(data) {
  return request({
    url: '/open/openInterfaceInfo',
    method: 'put',
    data: data
  })
}

// 删除接口信息
export function delOpenInterfaceInfo(id) {
  return request({
    url: '/open/openInterfaceInfo/' + id,
    method: 'delete'
  })
}

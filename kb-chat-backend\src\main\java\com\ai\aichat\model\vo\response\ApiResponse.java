package com.ai.aichat.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * API统一响应格式
 */
@Data
@Schema(description = "API响应")
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码：1-成功，0-失败
     */
    @Schema(description = "响应状态码", example = "1", allowableValues = {"0", "1"})
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "success")
    private String msg;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;

    public ApiResponse() {}

    public ApiResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(1, "success", data);
    }

    /**
     * 成功响应（带自定义消息）
     */
    public static <T> ApiResponse<T> success(T data, String msg) {
        return new ApiResponse<>(1, msg, data);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String msg) {
        return new ApiResponse<>(0, msg, null);
    }

    /**
     * 失败响应（带数据）
     */
    public static <T> ApiResponse<T> error(String msg, T data) {
        return new ApiResponse<>(0, msg, data);
    }
}

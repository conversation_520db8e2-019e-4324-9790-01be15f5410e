package com.ai.aichat.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 训练任务响应VO
 */
@Data
@Schema(description = "训练任务信息")
public class TrainingTaskVo {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    private Long id;

    /**
     * 模型ID
     */
    @Schema(description = "模型ID")
    private Long modelId;

    /**
     * 数据集名称
     */
    @Schema(description = "数据集名称")
    private String datasetName;

    /**
     * 训练轮次
     */
    @Schema(description = "训练轮次")
    private Integer epochs;

    /**
     * 学习率
     */
    @Schema(description = "学习率")
    private BigDecimal learningRate;

    /**
     * 训练状态：0-待开始，1-训练中，2-训练完成，3-训练失败，4-已取消
     */
    @Schema(description = "训练状态")
    private Integer status;

    /**
     * 训练状态描述
     */
    @Schema(description = "训练状态描述")
    private String statusDesc;

    /**
     * 当前轮次
     */
    @Schema(description = "当前轮次")
    private Integer currentEpoch;

    /**
     * 训练损失
     */
    @Schema(description = "训练损失")
    private BigDecimal loss;

    /**
     * 训练精度
     */
    @Schema(description = "训练精度")
    private BigDecimal accuracy;

    /**
     * 训练进度（百分比）
     */
    @Schema(description = "训练进度")
    private BigDecimal progress;

    /**
     * 训练日志
     */
    @Schema(description = "训练日志")
    private String trainingLog;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}

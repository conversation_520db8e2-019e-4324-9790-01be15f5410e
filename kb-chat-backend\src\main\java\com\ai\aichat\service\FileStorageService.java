package com.ai.aichat.service;

import java.io.File;
import java.util.List;

/**
 * 文件存储服务接口
 */
public interface FileStorageService {

    /**
     * 创建知识库文件夹结构
     * @param kbName 知识库名称
     * @return 是否创建成功
     */
    Boolean createKnowledgeBaseDirectories(String kbName);

    /**
     * 删除知识库文件夹及其内容
     * @param kbName 知识库名称
     * @return 是否删除成功
     */
    Boolean deleteKnowledgeBaseDirectories(String kbName);

    /**
     * 重命名知识库文件夹
     * @param oldName 旧名称
     * @param newName 新名称
     * @return 是否重命名成功
     */
    Boolean renameKnowledgeBaseDirectory(String oldName, String newName);

    /**
     * 检查知识库文件夹是否存在
     * @param kbName 知识库名称
     * @return 是否存在
     */
    Boolean knowledgeBaseDirectoryExists(String kbName);

    /**
     * 获取知识库中的文件列表
     * @param kbName 知识库名称
     * @return 文件列表
     */
    List<File> getKnowledgeBaseFiles(String kbName);

    /**
     * 获取知识库文件数量
     * @param kbName 知识库名称
     * @return 文件数量
     */
    Integer getKnowledgeBaseFileCount(String kbName);

    /**
     * 获取知识库大小（字节）
     * @param kbName 知识库名称
     * @return 大小
     */
    Long getKnowledgeBaseSize(String kbName);

    /**
     * 清空知识库文件夹内容
     * @param kbName 知识库名称
     * @return 是否清空成功
     */
    Boolean clearKnowledgeBaseContent(String kbName);
}

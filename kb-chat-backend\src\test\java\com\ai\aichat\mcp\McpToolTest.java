//package com.ai.aichat.mcp;
//
//import com.ai.aichat.love.LoveApp;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Assertions;
//import org.springframework.ai.tool.ToolCallbackProvider;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.util.Arrays;
//
///**
// * MCP工具测试类
// * 测试MCP工具的具体功能和调用
// */
//@Slf4j
//@SpringBootTest
//@ActiveProfiles("local")
//@DisplayName("MCP工具测试")
//class McpToolTest {
//
//    @Resource
//    @Qualifier("localToolCallbackProvider")
//    private ToolCallbackProvider toolCallbackProvider;
//
//    @Resource
//    private LoveApp loveApp;
//
//    @BeforeEach
//    void setUp() {
//        log.info("开始MCP工具测试");
//    }
//
//    @Test
//    @DisplayName("测试工具列表获取")
//    void testGetToolList() {
//        var tools = toolCallbackProvider.getToolCallbacks();
//
//        Assertions.assertNotNull(tools, "工具列表不应为空");
//        Assertions.assertTrue(tools.length > 0, "应该至少有一个工具");
//
//        log.info("可用工具数量: {}", tools.length);
//        log.info("工具列表获取测试通过");
//    }
//
//    @Test
//    @DisplayName("测试MCP功能通过LoveApp")
//    void testMcpFunctionality() {
//        Assertions.assertNotNull(loveApp, "LoveApp应该已初始化");
//
//        // 测试MCP功能是否可以正常调用
//        String testMessage = "北京有哪些适合约会的地方？";
//        String testChatId = "test-mcp-" + System.currentTimeMillis();
//
//        try {
//            String response = loveApp.doChatWithMcp(testMessage, testChatId);
//            Assertions.assertNotNull(response, "MCP响应不应为空");
//            Assertions.assertFalse(response.trim().isEmpty(), "MCP响应内容不应为空");
//            log.info("MCP功能测试通过，响应长度: {}", response.length());
//        } catch (Exception e) {
//            log.error("MCP功能测试失败", e);
//            Assertions.fail("MCP功能测试失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    @DisplayName("测试工具配置完整性")
//    void testToolConfigurationIntegrity() {
//        var tools = toolCallbackProvider.getToolCallbacks();
//
//        Assertions.assertTrue(tools.length > 0, "应该配置了至少一个工具");
//        log.info("工具配置完整性检查通过，共配置{}个工具", tools.length);
//    }
//
//}

package com.ai.aichat.model.vo.response;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 聊天历史记录列表响应VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "聊天历史记录列表响应")
public class ChatHistoryListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 历史记录列表
     */
    @Schema(description = "历史记录列表")
    private List<ChatHistoryVo> records;

    /**
     * 分页信息
     */
    @Schema(description = "分页信息")
    private ConversationListVo.PaginationVo pagination;
}

<template>
  <div class="chat-container chat-window" :class="{'preview-active': showFilePreview}">
    <!-- 对话内容区域 -->
    <div class="chat-main-area">
      <div
        class="talk-window"
        ref="talkWindow"
        @scroll="handleScroll">
        <!-- 聊天消息 -->
        <div class="messages-container">
          <div
            v-for="item in formattedTalkInfo"
            :key="item.id + '-' + item.renderKey"
            class="message-row"
            :class="item.type === 'ai' ? 'ai-message' : 'user-message'">
            <div class="message-avatar">
              <img
                :src="
                  item.type === 'ai'
                    ? '/src/assets/images/AI.png'
                    : '/src/assets/images/用户.png'
                " />
            </div>
            <div class="message-bubble" v-if="!item.isEditing">
              <div class="message-content">
                <div v-if="item.type === 'ai'" class="message-header">
                  <span class="sender-name">AI助手</span>
                  <div
                    class="edit-icon"
                    @click="startEdit(item)">
                    🖊
                  </div>
                </div>
                
                <div class="message-body">
                  <div v-if="!item.isEditing">
                    <div v-if="hasThinkingContent(item.info)" class="thinking-section">
                      <el-collapse>
                        <el-collapse-item>
                          <template #title>
                            <span class="thinking-title">深度思考过程</span>
                          </template>
                          <div class="markdown-body thinking-content" v-html="renderThinkingContent(item.info)"></div>
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                    <div class="markdown-body" v-html="renderFinalContent(item.info)"></div>
                    
                    <!-- 所有知识库问答的AI回复都显示调用大模型按钮 -->
                    <div v-if="item.type === 'ai' && !item.isUsingLLM && props.selectedKb" class="llm-query-btn">
                      <el-button type="primary" @click="queryLLM(item)" :loading="item.isQuerying">
                        <i class="el-icon-magic-stick"></i>
                        通过大模型探索
                      </el-button>
                    </div>
                    
                    <!-- 引用来源 -->
                    <div
                      v-if="hasValidSources(item.sources) || hasGraphData(item) || hasQAPairData(item)"
                      class="sources">
                      <p class="source-title">
                        <i class="el-icon-document"></i> 参考来源：
                      </p>
                      <ul class="source-list">
                        <!-- 文档来源 -->
                        <li
                          v-for="(source, sourceIndex) in getUniqueSources(item.sources)"
                          :key="sourceIndex"
                          class="source-item"
                          :class="source.file_source === activeFileUrl ? 'active-source' : ''"
                          @click="handleSourceClick(source, formattedTalkInfo.findIndex(msg => msg.id === item.id))">
                          <span class="source-icon">📄</span>
                          <span class="source-text">{{ formatSourceTitle(source.title) }}</span>
                          <span class="view-indicator">查看 <i class="el-icon-view"></i></span>
                        </li>

                        <!-- 问答对来源 - 新增 -->
                        <li
                          v-if="hasQAPairData(item)"
                          class="source-item qa-pair-source-item"
                          :class="{'active-source': showQAPairView && activeQAPairMessageId === item.id}"
                          @click="handleQAPairClick(item)">
                          <span class="source-icon">💬</span>
                          <span class="source-text">相关问答对 ({{ getQAPairCount(item) }}个)</span>
                          <span class="view-indicator">查看 <i class="el-icon-view"></i></span>
                        </li>



                        <!-- 知识图谱来源 - 以文件形式显示 -->
                        <li
                          v-if="hasGraphData(item)"
                          class="source-item graph-source-item"
                          :class="{'active-source': showGraphView && activeGraphMessageId === item.id}"
                          @click="handleGraphClick(item)">
                          <span class="source-icon">📊</span>
                          <span class="source-text">关联知识图谱</span>
                          <span class="view-indicator">查看 <i class="el-icon-view"></i></span>
                        </li>
                      </ul>
                      <!-- 添加跳转到高亮的按钮 -->
                      <div v-if="showFilePreview && item.sources && item.sources.length > 0" class="highlight-jump-btn">
                        <div class="highlight-navigation">
                          <el-button type="info" size="small" @click="scrollToHighlight('prev')" :disabled="!hasHighlights">
                            <i class="el-icon-arrow-left"></i> 上一处
                          </el-button>
                          <span class="highlight-counter" v-if="hasHighlights">
                            {{ currentHighlightIndex + 1 }} / {{ totalHighlights }}
                          </span>
                          <span class="highlight-counter" v-else>
                            0 / 0
                          </span>
                          <el-button type="info" size="small" @click="scrollToHighlight('next')" :disabled="!hasHighlights">
                            下一处 <i class="el-icon-arrow-right"></i>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 编辑区域移到外面，不被气泡包裹 -->
            <div
              v-if="item.isEditing"
              class="editing-area">
              <textarea
                v-model="item.editingInfo"
                class="edit-textarea"
                @blur="saveEdit(item)"
                @input="autoAdjustEditHeight($event.target)">
              </textarea>
              <div class="editing-controls">
                <el-button
                  @click="saveEdit(item)"
                  size="small"
                  class="save-button"
                  >保存</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右边的文件预览，使用固定定位避免影响主布局 -->
    <div v-if="showFilePreview" class="file-preview-overlay">
      <div class="file-preview-container">
        <div class="preview-header">
          <div class="preview-title">参考文档</div>
          <div class="close-preview-btn" @click.stop="closePreview">
            <i class="el-icon-close"></i>
            <span>关闭预览</span>
          </div>
        </div>
        <FilePreview
          v-model="showFilePreview"
          @fileUrlChange="url => activeFileUrl = url"
          ref="filePreviewRef"
        />
      </div>
    </div>

    <!-- 右边的问答对预览 - 新增部分 -->
    <div v-if="showQAPairView" class="qa-pair-view-overlay">
      <QAPairView
        :qaPairData="currentQAPairData"
        @close="closeQAPairView"
      />
    </div>

    <!-- 右边的图谱预览 - 新增部分 -->
    <div v-if="showGraphView" class="graph-view-overlay">
      <GraphView
        :graphData="currentGraphData"
        @close="closeGraphView"
      />
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, nextTick, onMounted, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import { marked } from 'marked'; // 导入 marked
  import hljs from 'highlight.js'; // 导入 highlight.js
  import 'highlight.js/styles/atom-one-dark.css'; // 导入代码高亮样式
  import FilePreview from './FilePreview.vue';
  import GraphView from './GraphView.vue'; // 导入图谱组件
  import QAPairView from './QAPairView.vue'; // 导入问答对组件
  import { ElCollapse, ElCollapseItem } from 'element-plus'
  import { kbChatStream, chatStream } from '@/api/knowledgeManage/chat';

  const props = defineProps({
    selectedKb: {
      type: String,
      required: true,
    },
    sessionId: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['update:talkInfo', 'preview-change', 'session-created']);

  const talkWindow = ref(null);
  const userScrolling = ref(false);
  const inputValue = ref('');
  const loading = ref(false);
  const talkInfo = ref([]);
  const showFilePreview = ref(false);
  const filePreviewRef = ref(null);

  // 添加高亮导航相关变量
  const currentHighlightIndex = ref(0);
  const totalHighlights = ref(0);
  const hasHighlights = ref(false);
  
  // 图谱相关变量 - 新增
  const showGraphView = ref(false);
  const currentGraphData = ref(null);
  const activeGraphMessageId = ref(null);

  // 问答对相关变量 - 新增
  const showQAPairView = ref(false);
  const currentQAPairData = ref(null);
  const activeQAPairMessageId = ref(null);

  // 添加 session_id 变量
  const currentSessionId = ref('');

  // 添加一个变量来强制更新key
  const renderKey = ref(0);

  // 添加聊天类型变量
  const chatType = ref('chat'); // 默认为普通对话

  // 添加一个标志，表示是否正在加载历史会话
  const isLoadingHistory = ref(false);

  // 添加一个变量来记录当前会话所使用的知识库名称
  const currentSessionKb = ref('');

  // 增加更新渲染key的方法
  const forceRender = () => {
    renderKey.value++;
  };

  // 将talkInfo转换为带有唯一key的对象
  const formattedTalkInfo = computed(() => {
    return talkInfo.value.map(item => ({
      ...item,
      renderKey: renderKey.value // 添加渲染key
    }));
  });

  // 监视props中的sessionId变化
  watch(
    () => props.sessionId,
    (newSessionId) => {
      // console.log('ChatWindow接收到新的sessionId:', newSessionId);
      if (newSessionId) {
        currentSessionId.value = newSessionId;
      }
    },
    { immediate: true }
  );

  // 滚动相关方法
  const scrollToBottom = async () => {
    // 先强制更新渲染key
    forceRender();
    
    // 滚动到消息列表底部，并增加重试机制确保UI更新后执行
    const doScroll = (retryCount = 0) => {
      nextTick(() => {
        if (talkWindow.value) {
          talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
          
          // 确认是否真的滚动到底部，如果没有且未超过重试次数，则重试
          if (talkWindow.value.scrollHeight - talkWindow.value.scrollTop > talkWindow.value.clientHeight + 10 && retryCount < 5) {
            // console.log('滚动尚未到底部，重试...', retryCount + 1);
            setTimeout(() => doScroll(retryCount + 1), 100 * (retryCount + 1)); // 指数退避
          }
        }
      });
    };
    
    // 先立即滚动一次，然后再次尝试（通常在DOM更新后）
    if (talkWindow.value) {
      talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
    }
    
    // 延迟执行，确保DOM已更新
    setTimeout(() => doScroll(), 50);
  };

  const handleScroll = (e) => {
    const element = e.target;
    const isAtBottom =
      element.scrollHeight - element.scrollTop - element.clientHeight < 50;
    userScrolling.value = !isAtBottom;
  };

  // 监听聊天记录变化
  watch(
    () => [...talkInfo.value],
    async (newTalkInfo) => {
      // console.log('聊天记录已更新，新条目数量:', newTalkInfo.length);
      await scrollToBottom();
      emit('update:talkInfo', newTalkInfo);
    },
    { deep: true }
  );

  // 单独监听talkInfo的整体替换
  watch(
    talkInfo,
    (newTalkInfo) => {
      // console.log('talkInfo被整体替换，新消息数量:', newTalkInfo.length);
      scrollToBottom();
    },
    { deep: true }
  );

  // 监听 selectedKb 的变化
  watch(
    () => props.selectedKb,
    (newKb) => {
      // 如果正在加载历史会话，不执行清空操作
      if (isLoadingHistory.value) {
        // console.log('正在加载历史会话，不清空聊天记录');
        return;
      }
      
      // 如果当前已有会话且知识库变化了，才清空聊天记录
      if (talkInfo.value.length > 0 && newKb !== currentSessionKb.value) {
        // console.log('知识库变更，清空聊天记录', currentSessionKb.value, '->', newKb);
        
        // 清空聊天记录
        talkInfo.value = [];
        // 标记为新对话
        startNewChat();
        // 更新当前会话的知识库
        currentSessionKb.value = newKb || '';
      } else if (talkInfo.value.length === 0) {
        // 如果没有会话，只需更新当前知识库记录
        currentSessionKb.value = newKb || '';
      }
    }
  );

  // 添加新对话标记变量
  const isNewChat = ref(true);

  // 修改 startNewChat 方法
  const startNewChat = () => {
    isNewChat.value = true;
    currentSessionId.value = ''; // 清空当前会话ID
  };

  // 提供设置聊天类型的方法
  const setChatType = (type) => {
    // console.log('设置聊天类型为:', type);
    chatType.value = type;
  };

  // 提交处理
  const handleSubmit = async () => {
    if (!inputValue.value.trim()) {
      ElMessage.warning('请输入问题内容！');
      return;
    }

    if (loading.value) {
      ElMessage.warning('请等待当前回答完成');
      return;
    }

    const userMessage = {
      id: talkInfo.value.length + 1,
      info: inputValue.value.trim(),
      type: 'user',
    };
    talkInfo.value.push(userMessage);

    const userMessageText = inputValue.value;
    inputValue.value = '';
    loading.value = true;

    try {
      // 根据知识库选择和聊天类型决定使用哪个API
      const requestBody = {
        prompt: userMessageText,
        new_chat: isNewChat.value,
        session_id: currentSessionId.value
      };
      
      // 关键修复：如果有会话ID，强制设置new_chat为false
      if (currentSessionId.value) {
        requestBody.new_chat = false;
        // console.log('使用历史会话ID，强制设置new_chat=false');
        // 同时也更新本地状态
        isNewChat.value = false;
      }
      
      // 如果有选择知识库，则添加知识库名称
      if (props.selectedKb) {
        requestBody.kb_name = props.selectedKb;
      }
      
      // 添加聊天类型（如果不是默认值）
      if (chatType.value && chatType.value !== 'chat') {
        requestBody.chat_type = chatType.value;
      }
      
      // console.log('发送请求:', requestBody);
      
      // 根据是否选择了知识库来决定使用哪个API
      const response = await (props.selectedKb ? kbChatStream(requestBody) : chatStream(requestBody));

      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
      }

      const aiMessage = {
        id: talkInfo.value.length + 1,
        info: '',
        type: 'ai',
        sources: [],
        isTyping: true,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: false
      };
      talkInfo.value.push(aiMessage);

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;

          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (!data || data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              if (parsed.code === 1) {
                aiMessage.info += parsed.data.content || '';
                if (parsed.data.session_id) {
                  currentSessionId.value = parsed.data.session_id;
                }
                if (parsed.data.sources?.length > 0) {
                  aiMessage.sources = [...parsed.data.sources];
                }
                // 处理图谱数据
                if (parsed.data.graph_data) {
                  aiMessage.graph_data = JSON.stringify(parsed.data.graph_data);
                }
                // 处理问答对数据
                if (parsed.data.qa_pair_data) {
                  aiMessage.qa_pair_data = JSON.stringify(parsed.data.qa_pair_data);
                }
                talkInfo.value = [...talkInfo.value];
              }
            } catch (e) {
              // console.error('解析响应数据失败:', e);
              aiMessage.info = '服务器返回数据格式错误，请联系管理员';
              break;
            }
          }
        }
      }
    } catch (error) {
      ElMessage.error(error.message || '发送消息失败');
      const lastMessage = talkInfo.value[talkInfo.value.length - 1];
      if (lastMessage?.type === 'ai') {
        lastMessage.info = '发送消息失败，请稍后重试';
      }
    } finally {
      loading.value = false;
      isNewChat.value = false;
      
      // 如果这是第一次发送消息，记录当前使用的知识库
      if (currentSessionKb.value === '') {
        currentSessionKb.value = props.selectedKb || '';
        // console.log('新对话设置知识库:', currentSessionKb.value);
      }
      
      // 当接收到会话ID时，通知父组件更新历史记录列表
      if (currentSessionId.value) {
        // 发送自定义事件，通知父组件刷新历史记录
        emit('session-created', currentSessionId.value);
      }
    }
  };

  // 编辑相关方法
  const startEdit = (item) => {
    // console.log('开始编辑消息:', item);
    if (item.type === 'ai') {
      // 使用Vue的响应式更新方式
      const itemIndex = talkInfo.value.findIndex(msg => msg.id === item.id);
      if (itemIndex >= 0) {
        // console.log('找到要编辑的消息索引:', itemIndex);
        
        // 创建一个新对象以确保Vue能检测到变化
        const updatedItems = [...talkInfo.value];
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          isEditing: true,
          editingInfo: updatedItems[itemIndex].info
        };
        
        // 替换整个数组以确保响应式更新
        talkInfo.value = updatedItems;
        // console.log('已更新talkInfo，设置isEditing为true');
        
        // 强制重新渲染
        forceRender();
        
        // 等待DOM更新后聚焦文本框
        nextTick(() => {
          // console.log('DOM更新后尝试聚焦编辑框');
          const textareas = document.querySelectorAll('.edit-textarea');
          // console.log('找到的编辑框数量:', textareas.length);
          
          // 直接选择最后一个编辑框
          if (textareas.length > 0) {
            const lastTextarea = textareas[textareas.length - 1];
            // console.log('聚焦到最后一个编辑框');
            lastTextarea.focus();
            autoAdjustEditHeight(lastTextarea);
          } else {
            // console.error('未找到编辑框元素');
          }
        });
      } else {
        // console.error('未找到要编辑的消息');
      }
    }
  };

  const saveEdit = (item) => {
    // console.log('保存编辑:', item);
    
    // 找到要更新的消息索引
    const itemIndex = talkInfo.value.findIndex(msg => msg.id === item.id);
    if (itemIndex >= 0) {
      // console.log('找到要保存的消息索引:', itemIndex);
      
      // 创建一个新对象以确保Vue能检测到变化
      const updatedItems = [...talkInfo.value];
      
      // 更新消息内容
      if (item.editingInfo?.trim()) {
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          info: item.editingInfo,
          isEditing: false
        };
        
        // 删除临时编辑信息
        delete updatedItems[itemIndex].editingInfo;
      } else {
        // 如果编辑内容为空，只关闭编辑模式
        updatedItems[itemIndex] = {
          ...updatedItems[itemIndex],
          isEditing: false
        };
        
        // 删除临时编辑信息
        delete updatedItems[itemIndex].editingInfo;
      }
      
      // 替换整个数组以确保响应式更新
      talkInfo.value = updatedItems;
      // console.log('已保存编辑，设置isEditing为false');
      
      // 强制重新渲染
      forceRender();
    } else {
      // console.error('未找到要保存的消息');
    }
  };

  // 添加函数检查
  const autoAdjustEditHeight = (element) => {
    // console.log('自动调整编辑区域高度', element);
    if (!element) {
      // console.warn('无法调整编辑框高度: 元素为空');
      return;
    }
    
    element.style.height = 'auto';
    element.style.height = `${element.scrollHeight}px`;
  };

  // 使用 FilePreview 组件的方法
  const formatSourceTitle = (title) => {
    if (!title) return '';
    // 只移除"来源文件："前缀，保留文件名中的所有字符（包括前导下划线）
    return title.replace('来源文件：', '');
  };

  const hasValidSources = (sources) => {
    if (!sources || !Array.isArray(sources) || sources.length === 0) {
      return false;
    }
    return sources.some(source => 
      source && source.title && source.title.trim() !== ''
    );
  };

  const getUniqueSources = (sources) => {
    if (!hasValidSources(sources)) return [];
    
    const uniqueMap = new Map();
    sources.forEach(source => {
      if (source && source.title && source.file_source) {
        if (!uniqueMap.has(source.file_source)) {
          uniqueMap.set(source.file_source, { ...source });
        }
      }
    });
    
    return Array.from(uniqueMap.values());
  };

  // 暴露方法给父组件
  defineExpose({
    talkInfo,
    inputValue,
    loading,
    handleSubmit,
    forceRender, // 暴露强制渲染方法
    setChatType, // 暴露设置聊天类型方法
    setHistorySession: (sessionData, sessionId) => {
      // 设置正在加载历史会话标志
      isLoadingHistory.value = true;
      
      // 如果有传入会话数据且包含消息
      if (sessionData && Array.isArray(sessionData)) {
        // 格式化会话消息，确保包含需要的所有字段
        const formattedMessages = sessionData.map(msg => {
          const formattedMsg = {
            id: msg.id || Date.now(),
            type: msg.role === 'user' ? 'user' : 'ai',
            info: msg.content,
            sources: msg.sources || [],
            graph_data: msg.graph_data,  // 保留graph_data
            qa_pair_data: msg.qa_pair_data,  // 保留qa_pair_data
            isTyping: false,
            isEditing: false,
            editingInfo: '',
            isUsingLLM: false,
            timestamp: msg.timestamp
          };

          // 如果qa_pair_data是字符串，尝试解析为对象
          if (formattedMsg.qa_pair_data && typeof formattedMsg.qa_pair_data === 'string') {
            try {
              formattedMsg.qa_pair_data = JSON.parse(formattedMsg.qa_pair_data);
            } catch (error) {
              console.error('解析历史记录中的qa_pair_data失败:', error);
              formattedMsg.qa_pair_data = null;
            }
          }

          // 如果graph_data是字符串，尝试解析为对象
          if (formattedMsg.graph_data && typeof formattedMsg.graph_data === 'string') {
            try {
              formattedMsg.graph_data = JSON.parse(formattedMsg.graph_data);
            } catch (error) {
              console.error('解析历史记录中的graph_data失败:', error);
              formattedMsg.graph_data = null;
            }
          }

          return formattedMsg;
        });
        
        // 设置格式化后的消息
        talkInfo.value = formattedMessages;
        
        // 关键修复: 标记为非新对话，这样发送消息时会继续会话而不是创建新的
        isNewChat.value = false;
        
        // 如果有会话ID，设置当前会话ID
        if (sessionId) {
          currentSessionId.value = sessionId;
        }
        
        // 记录当前选中的知识库，防止知识库切换时清空会话
        currentSessionKb.value = props.selectedKb || '';
        
        // 强制重新渲染并滚动到底部
        forceRender();

        // 确保Vue能够检测到qa_pair_data的变化
        nextTick(() => {
          // 再次强制渲染以确保问答对数据被正确处理
          forceRender();
          scrollToBottom();
        });
      }
      
      // 延迟重置加载标志，确保不会触发selectedKb的监听器
      setTimeout(() => {
        isLoadingHistory.value = false;
      }, 500);
    },
    scrollToBottom: () => {
      // 先强制更新渲染key
      forceRender();
      
      // 滚动到消息列表底部，并增加重试机制确保UI更新后执行
      const doScroll = (retryCount = 0) => {
        nextTick(() => {
          if (talkWindow.value) {
            talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
            
            // 确认是否真的滚动到底部，如果没有且未超过重试次数，则重试
            if (talkWindow.value.scrollHeight - talkWindow.value.scrollTop > talkWindow.value.clientHeight + 10 && retryCount < 5) {
              // console.log('滚动尚未到底部，重试...', retryCount + 1);
              setTimeout(() => doScroll(retryCount + 1), 100 * (retryCount + 1)); // 指数退避
            }
          }
        });
      };
      
      // 先立即滚动一次，然后再次尝试（通常在DOM更新后）
      if (talkWindow.value) {
        talkWindow.value.scrollTop = talkWindow.value.scrollHeight;
      }
      
      // 延迟执行，确保DOM已更新
      setTimeout(() => doScroll(), 50);
    }
  });

  // 修改 handleSourceClick 方法
  const handleSourceClick = (source, messageIndex) => {
    // console.log('点击引用源:', source);
    // console.log('原始文件路径:', source.file_source);

    // 关闭其他预览(如果已打开)
    if (showQAPairView.value) {
      closeQAPairView();
    }
    if (showGraphView.value) {
      closeGraphView();
    }

    showFilePreview.value = true;
    // 通知父组件预览状态变化
    emit('preview-change', true);
    
    // 重置高亮计数
    currentHighlightIndex.value = 0;
    totalHighlights.value = 0;
    hasHighlights.value = false;
    
    nextTick(() => {
      if (filePreviewRef.value) {
        // 如果提供了消息索引，直接使用该索引查找对应的消息
        if (messageIndex !== undefined && talkInfo.value[messageIndex]) {
          const parentMessage = talkInfo.value[messageIndex];
          // console.log('直接使用提供的消息索引找到父消息:', messageIndex);
          
          // 为点击的source添加sourcesCollection属性，包含所有来自同一父消息中相同file_source的sources
          const enrichedSource = {
            ...source,
            parentMessageIndex: messageIndex,  // 记录来源消息索引
            sourcesCollection: parentMessage.sources.filter(s => 
              s.file_source === source.file_source
            )
          };
          
          // console.log('传递扩展后的引用源，完整文件路径:', enrichedSource.file_source);
          filePreviewRef.value.handleSourceClick(enrichedSource);
        }
        // 如果没有提供消息索引，则尝试查找匹配的父消息
        else {
          const parentMessageIndex = findParentMessageIndex(source);
          
          if (parentMessageIndex !== -1) {
            const parentMessage = talkInfo.value[parentMessageIndex];
            // console.log('通过搜索找到包含此引用的父消息:', parentMessageIndex);
            
            const enrichedSource = {
              ...source,
              parentMessageIndex: parentMessageIndex,  // 记录来源消息索引
              sourcesCollection: parentMessage.sources.filter(s => 
                s.file_source === source.file_source
              )
            };
            
            // console.log('传递扩展后的引用源，完整文件路径:', enrichedSource.file_source);
            filePreviewRef.value.handleSourceClick(enrichedSource);
          } else {
            // 如果找不到，退回到原来的逻辑
            // console.log('找不到父消息，使用单个引用源，完整文件路径:', source.file_source);
            filePreviewRef.value.handleSourceClick(source);
          }
        }
        
        // 等待文档加载和高亮处理完成后，更新高亮计数
        setTimeout(() => {
          updateHighlightStats();
        }, 3000); // 增加等待时间，确保高亮处理完成
      } else {
        // console.error('filePreviewRef 未初始化');
        ElMessage.warning('无法打开文件预览');
      }
    });
  };

  // 通过比较引用来查找父消息的索引
  const findParentMessageIndex = (clickedSource) => {
    // 遍历所有消息，找到包含完全匹配的source的那个消息
    for (let i = talkInfo.value.length - 1; i >= 0; i--) {
      const msg = talkInfo.value[i];
      if (msg.sources && Array.isArray(msg.sources)) {
        // 寻找精确匹配的源（通过内容和文件路径匹配）
        const hasExactSource = msg.sources.some(s => 
          s.file_source === clickedSource.file_source && 
          s.content === clickedSource.content
        );
        
        if (hasExactSource) {
          return i;
        }
      }
    }
    return -1;
  };

  // 激活的文件index
  const activeFileUrl = ref('');

  // 配置 marked
  marked.setOptions({
    highlight: function (code, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value;
        } catch (e) {
          console.error(e);
        }
      }
      return hljs.highlightAuto(code).value;
    },
    breaks: true, // 支持 GitHub 风格的换行
    gfm: true, // 启用 GitHub 风格的 Markdown
  });

  // 添加 Markdown 渲染函数
  const renderMarkdown = (text) => {
    if (!text) return '';
    try {
      return marked(text);
    } catch (e) {
      console.error('Markdown 渲染错误:', e);
      return text;
    }
  };

  // 添加处理深度思考内容的方法
  const hasThinkingContent = (text) => {
    return text && text.includes('</think>');
  };

  const renderThinkingContent = (text) => {
    if (!text) return '';
    const thinkMatch = text.split('</think>')[0];
    return marked(thinkMatch);
  };

  const renderFinalContent = (text) => {
    if (!text) return '';
    const parts = text.split('</think>');
    return marked(parts.length > 1 ? parts[1] : text);
  };

  // 添加输入法状态追踪
  const isComposing = ref(false);

  // 处理输入法开始输入
  const handleCompositionStart = () => {
    isComposing.value = true;
  };

  // 处理输入法结束输入
  const handleCompositionEnd = () => {
    isComposing.value = false;
  };

  // 处理回车键按下
  const handleEnterPress = (e) => {
    if (!isComposing.value) {
      handleSubmit();
    }
  };

  const handleUseLLM = async (item) => {
    if (item.isUsingLLM || loading.value) return;
    
    // 将 aiMessage 变量声明移到 try 块外部
    let aiMessage;
    
    try {
      item.isUsingLLM = true;
      loading.value = true;
      
      // 获取原始问题
      const originalQuestion = findUserQuestion(item);
      if (!originalQuestion) {
        throw new Error('未找到原始问题');
      }

      // 调用普通聊天接口
      const response = await chatStream({
        prompt: originalQuestion,
        new_chat: false,
        session_id: currentSessionId.value
      });

      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
      }

      // 创建新的 AI 消息
      aiMessage = {
        id: talkInfo.value.length + 1,
        info: '',
        type: 'ai',
        sources: [],
        isTyping: true,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: false,
        fromLLM: true  // 添加标记表示这是来自大模型的回答
      };
      talkInfo.value.push(aiMessage);

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;

          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (!data || data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              if (parsed.code === 1) {
                aiMessage.info += parsed.data.content || '';
                if (parsed.data.session_id) {
                  currentSessionId.value = parsed.data.session_id;
                }
                talkInfo.value = [...talkInfo.value];
              }
            } catch (e) {
              console.error('解析响应数据失败:', e);
            }
          }
        }
      }
    } catch (error) {
      ElMessage.error(error.message || '使用大模型回答失败');
    } finally {
      item.isUsingLLM = false;
      loading.value = false;
      if (aiMessage) {
        aiMessage.isTyping = false;
        talkInfo.value = [...talkInfo.value];
      }
    }
  };

  // 添加查找原始问题的方法
  const findUserQuestion = (aiMessage) => {
    const aiIndex = talkInfo.value.findIndex(item => item.id === aiMessage.id);
    if (aiIndex > 0) {
      const userMessage = talkInfo.value[aiIndex - 1];
      if (userMessage.type === 'user') {
        return userMessage.info;
      }
    }
    return null;
  };

  // 修改关闭预览方法
  const closePreview = () => {
    // console.log('关闭预览按钮被点击');
    showFilePreview.value = false;
    // 通知父组件预览状态变化
    emit('preview-change', false);
    
    // 添加延迟确保状态更新
    setTimeout(() => {
      if (showFilePreview.value === true) {
        // console.log('强制关闭预览');
        showFilePreview.value = false;
        emit('preview-change', false);
      }
    }, 100);
  };

  // 添加跳转到高亮位置的方法
  const scrollToHighlight = (direction) => {
    if (filePreviewRef.value && showFilePreview.value) {
      const result = filePreviewRef.value.scrollToHighlight(direction);
      // 更新高亮计数
      updateHighlightStats();
      
      if (!result && hasHighlights.value) {
        // 如果导航失败但应该有高亮，尝试等待并重试
        setTimeout(() => {
          updateHighlightStats();
          if (hasHighlights.value) {
            filePreviewRef.value.scrollToHighlight(direction);
          }
        }, 500);
      }
    } else {
      ElMessage.warning('请先打开文件预览');
    }
  };
  
  // 添加更新高亮统计信息的方法
  const updateHighlightStats = () => {
    if (filePreviewRef.value) {
      const stats = filePreviewRef.value.getHighlightStats();
      // console.log('更新高亮统计:', stats);
      currentHighlightIndex.value = stats.currentIndex;
      totalHighlights.value = stats.total;
      hasHighlights.value = stats.total > 0;
    } else {
      hasHighlights.value = false;
    }
  };

  // 监听showFilePreview变化通知父组件
  watch(showFilePreview, (newVal) => {
    emit('preview-change', newVal);
    
    // 如果关闭预览，重置高亮计数
    if (!newVal) {
      currentHighlightIndex.value = 0;
      totalHighlights.value = 0;
      hasHighlights.value = false;
    }
  });

  // 在 script setup 部分添加新的方法
  const queryLLM = async (item) => {
    if (!item || item.isQuerying) return;
    
    try {
      item.isQuerying = true;
      const lastUserMessage = talkInfo.value
        .slice(0, talkInfo.value.indexOf(item))
        .reverse()
        .find(msg => msg.type === 'user');

      if (!lastUserMessage) {
        ElMessage.warning('未找到相关问题');
        return;
      }

      // 调用普通聊天接口
      const response = await chatStream({
        prompt: lastUserMessage.info,
        new_chat: false,
        session_id: currentSessionId.value
      });

      if (!response.ok) {
        throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
      }

      // 创建新的AI消息
      const newAiMessage = {
        id: talkInfo.value.length + 1,
        info: '',
        type: 'ai',
        sources: [],
        isTyping: true,
        isEditing: false,
        editingInfo: '',
        isUsingLLM: true
      };
      talkInfo.value.push(newAiMessage);

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (!data || data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              if (parsed.code === 1) {
                newAiMessage.info += parsed.data.content || '';
                if (parsed.data.session_id) {
                  currentSessionId.value = parsed.data.session_id;
                  isNewChat.value = false;
                }
                talkInfo.value = [...talkInfo.value];
              }
            } catch (e) {
              // console.error('解析响应数据失败:', e);
            }
          }
        }
      }

      newAiMessage.isTyping = false;
      talkInfo.value = [...talkInfo.value];
    } catch (error) {
      ElMessage.error(error.message || '请求失败');
      // console.error('调用大模型失败:', error);
    } finally {
      item.isQuerying = false;
    }
  };

  // 检查消息是否包含图谱数据
  const hasGraphData = (item) => {
    if (!item) return false;
    
    // 检查graph_data是否存在且有效
    if (item.graph_data) {
      // 如果是字符串类型，检查是否为非空字符串
      if (typeof item.graph_data === 'string') {
        return item.graph_data.trim().length > 0;
      }
      // 如果是对象类型，直接返回true
      else if (typeof item.graph_data === 'object') {
        return true;
      }
    }
    
    return false;
  };

  // 处理点击图谱按钮的事件
  const handleGraphClick = (item) => {
    try {
      // 检查图谱数据
      if (hasGraphData(item)) {
        let graphData;
        
        // 如果是字符串，尝试解析
        if (typeof item.graph_data === 'string') {
          try {
            graphData = JSON.parse(item.graph_data);
          } catch (parseError) {
            console.error('JSON解析错误:', parseError);
            ElMessage.error('解析图谱数据失败');
            return;
          }
        } 
        // 如果已经是对象，直接使用
        else if (typeof item.graph_data === 'object') {
          graphData = item.graph_data;
        }
        
        // 检查图谱数据是否有节点和边
        if (graphData && graphData.nodes && graphData.nodes.length > 0) {
          showGraphView.value = true;
          currentGraphData.value = graphData;
          activeGraphMessageId.value = item.id;
          
          // 关闭其他预览(如果已打开)
          if (showFilePreview.value) {
            closePreview();
          }
          if (showQAPairView.value) {
            closeQAPairView();
          }
          
          // 通知父组件预览状态变化
          emit('preview-change', true);
        } else {
          ElMessage.warning('图谱数据不包含节点信息');
        }
      } else {
        ElMessage.warning('没有可用的图谱数据');
      }
    } catch (error) {
      console.error('处理图谱点击时发生错误:', error);
      ElMessage.error('解析图谱数据失败');
    }
  };

  // 关闭图谱视图
  const closeGraphView = () => {
    showGraphView.value = false;
    currentGraphData.value = null;
    activeGraphMessageId.value = null;

    // 通知父组件预览状态变化
    emit('preview-change', false);
  };

  // 检查消息是否包含问答对数据
  const hasQAPairData = (item) => {
    if (!item) return false;

    // 检查qa_pair_data是否存在且有效
    if (item.qa_pair_data) {
      // 如果是字符串类型，检查是否为非空字符串
      if (typeof item.qa_pair_data === 'string') {
        return item.qa_pair_data.trim().length > 0;
      }
      // 如果是对象类型，直接返回true（与hasGraphData保持一致）
      else if (typeof item.qa_pair_data === 'object') {
        return true;
      }
    }

    return false;
  };

  // 获取问答对数量
  const getQAPairCount = (item) => {
    if (!hasQAPairData(item)) return 0;

    try {
      let qaPairData;

      if (typeof item.qa_pair_data === 'string') {
        qaPairData = JSON.parse(item.qa_pair_data);
      } else {
        qaPairData = item.qa_pair_data;
      }

      return qaPairData.nodes ? qaPairData.nodes.length : 0;
    } catch (error) {
      console.error('解析问答对数据失败:', error);
      return 0;
    }
  };

  // 处理点击问答对按钮的事件
  const handleQAPairClick = (item) => {
    try {
      // 检查问答对数据
      if (hasQAPairData(item)) {
        let qaPairData;

        // 如果是字符串，尝试解析
        if (typeof item.qa_pair_data === 'string') {
          try {
            qaPairData = JSON.parse(item.qa_pair_data);
          } catch (parseError) {
            console.error('解析问答对数据失败:', parseError);
            ElMessage.error('问答对数据格式错误');
            return;
          }
        } else {
          // 如果已经是对象，直接使用
          qaPairData = item.qa_pair_data;
        }

        // 验证解析后的数据结构
        if (qaPairData && qaPairData.nodes && qaPairData.nodes.length > 0) {
          showQAPairView.value = true;
          currentQAPairData.value = qaPairData;
          activeQAPairMessageId.value = item.id;

          // 关闭其他预览(如果已打开)
          if (showFilePreview.value) {
            closePreview();
          }
          if (showGraphView.value) {
            closeGraphView();
          }

          // 通知父组件预览状态变化
          emit('preview-change', true);
        } else {
          ElMessage.warning('问答对数据不包含节点信息');
        }
      } else {
        ElMessage.warning('没有可用的问答对数据');
      }
    } catch (error) {
      console.error('处理问答对点击时发生错误:', error);
      ElMessage.error('解析问答对数据失败');
    }
  };

  // 关闭问答对视图
  const closeQAPairView = () => {
    showQAPairView.value = false;
    currentQAPairData.value = null;
    activeQAPairMessageId.value = null;

    // 通知父组件预览状态变化
    emit('preview-change', false);
  };



  // 监听showGraphView变化通知父组件
  watch(showGraphView, (newVal) => {
    emit('preview-change', newVal);
  });

  // 监听showQAPairView变化通知父组件
  watch(showQAPairView, (newVal) => {
    emit('preview-change', newVal);
  });

  onMounted(() => {});
</script>

<style scoped>
.chat-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: transparent;
  overflow: hidden;
}

.chat-container.preview-active {
  /* 当预览激活时的距离调整 */
  /* 避免使用任何会导致主区域移位的样式 */
  transition: all 0.3s ease;
}

.chat-main-area {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* 只移动消息容器，不移动整个chat-main-area */
.preview-active .chat-main-area {
  /* 移除整个聊天区域的左移 */
  /* transform: translateX(-10%); */
  transition: transform 0.3s ease;
}

/* 只移动消息部分 */
.preview-active .messages-container {
  /* 预览激活时保持消息容器与普通状态一致，确保不变形 */
  width: 100%;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center; /* 保持居中对齐 */
  transform: translateX(-5%) translateY(0); /* 减小左移幅度，因为预览框变小了 */
  transition: transform 0.3s ease;
}

/* 文件预览覆盖层 - 修改位置到右上角 */
.file-preview-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 由35%增加到40%，利用左移的空间 */
  max-width: none; /* 移除最大宽度限制，让预览区域充分利用空间 */
  z-index: 5; /* 确保不会覆盖关闭对话按钮 */
  border-left: 2px solid #3f63a8;
  border-top: 2px solid #3f63a8;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease; /* 添加过渡效果 */
}

/* 问答对视图覆盖层 */
.qa-pair-view-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 与文件预览保持一致 */
  max-width: none;
  z-index: 5; /* 确保不会覆盖关闭对话按钮 */
  border-left: 2px solid #ffa500;
  border-top: 2px solid #ffa500;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(255, 165, 0, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 图谱视图覆盖层 */
.graph-view-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 与文件预览保持一致 */
  max-width: none;
  z-index: 5; /* 确保不会覆盖关闭对话按钮 */
  border-left: 2px solid #13fff3;
  border-top: 2px solid #13fff3;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(19, 255, 243, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 删除Vue过渡钩子，它们可能会干扰关闭操作 */

.file-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  height: 36px; /* 减小高度 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px; /* 减小内边距 */
  background-color: rgba(9, 39, 98, 0.9);
  border-bottom: 1px solid #3f63a8;
}

.preview-title {
  font-weight: bold;
  color: #13fff3;
  font-size: 14px; /* 减小字体 */
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-title::before {
  content: '📄';
  font-size: 14px; /* 减小图标 */
}

.close-preview-btn {
  cursor: pointer;
  padding: 4px 8px; /* 减小内边距 */
  color: white;
  background-color: rgba(15, 45, 90, 0.7);
  border: 1px solid #3f63a8;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 3px; /* 减小间距 */
  font-size: 13px; /* 减小字体 */
  transition: all 0.2s;
  z-index: 1010; /* 确保按钮在最上层 */
}

.close-preview-btn:hover {
  background-color: rgba(19, 255, 243, 0.15);
  color: #13fff3;
  border-color: #13fff3;
}

.preview-active .talk-window {
  /* 预览激活时确保聊天区域不被遮挡，保持原样 */
  width: 100%;
}

.talk-window {
  width: 100%;
  height: calc(100% - 20px);
  margin-bottom: 100px; /* 从70px增加到100px，增加与输入框的间距 */
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  transition: transform 0.3s ease; /* 只过渡transform属性，避免其他属性变化 */
}

.empty-chat {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding-top: 80px;
  transform: translateX(0) translateY(0); /* 默认位置，明确指定Y轴不变 */
  transition: transform 0.3s ease; /* 只过渡transform属性 */
}

.preview-active .empty-chat {
  transform: translateX(-5%) translateY(0); /* 同步调整空状态左移量 */
  transition: transform 0.3s ease;
}

.welcome-message {
  text-align: center;
  color: #fff;
  max-width: 600px;
  padding: 30px;
  background-color: rgba(9, 39, 98, 0.7);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.welcome-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.welcome-message h2 {
  font-size: 28px;
  margin-bottom: 15px;
}

.welcome-message p {
  font-size: 16px;
  opacity: 0.8;
}

/* 消息容器 */
.messages-container {
  width: 100%;
  padding: 20px 0 40px 0; /* 增加底部内边距到40px */
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center; /* 正常情况下居中对齐 */
  transform: translateX(0) translateY(0); /* 默认位置，明确指定Y轴不变 */
  transition: transform 0.3s ease; /* 只过渡transform属性 */
}

/* 消息行样式 - 正常情况 */
.message-row {
  width: 100%;
  max-width: 800px; /* 限制最大宽度，确保居中效果 */
  padding: 0 20px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

/* 最后一条消息额外的底部边距 */
.message-row:last-child {
  margin-bottom: 120px; /* 增加底部边距，避免与输入框重叠 */
}

/* 消息行样式 - 预览激活时修改 */
.preview-active .message-row {
  /* 保持与非预览状态相同 */
  width: 100%;
  max-width: 800px; /* 使用与正常状态相同的最大宽度 */
  margin-left: auto;
  margin-right: auto;
  /* 重置内边距，与普通状态保持一致 */
  padding: 0 20px;
  /* 添加过渡效果 */
  transition: transform 0.3s ease;
}

.message-row.user-message {
  justify-content: flex-end;
}

.message-row.ai-message {
  justify-content: flex-start;
}

/* 确保用户消息在预览模式下仍然保持右对齐 */
.preview-active .message-row.user-message {
  justify-content: flex-end;
}

/* 头像样式 */
.message-avatar {
  width: 36px;
  height: 36px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  object-fit: cover;
}

.ai-message .message-avatar {
  margin-right: 12px;
}

.user-message .message-avatar {
  margin-left: 12px;
  order: 2;
}

/* 气泡样式 */
.message-bubble {
  max-width: 75%;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* 确保气泡内容不会因为容器变化而收缩 */
  flex-shrink: 0;
  min-width: 0;
}

.ai-message .message-bubble {
  background-color: rgba(9, 39, 98, 0.8);
  color: #fff;
  border-radius: 16px;
  border-top-left-radius: 4px;
}

.user-message .message-bubble {
  background-color: rgba(15, 75, 165, 0.8);
  color: #fff;
  border-radius: 16px;
  border-top-right-radius: 4px;
}

/* 确保预览模式下的气泡宽度保持一致 */
.preview-active .message-bubble {
  max-width: 75%; /* 与普通状态保持一致 */
  width: auto; /* 由内容决定宽度 */
  min-width: min-content; /* 确保气泡至少有足够的宽度 */
}

/* 消息内容样式 */
.message-content {
  width: 100%;
  min-width: 0; /* 确保内容不会影响布局 */
}

/* 确保预览模式下内容宽度不变 */
.preview-active .message-content {
  width: 100%;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sender-name {
  font-weight: bold;
  opacity: 0.8;
}

.edit-icon {
  cursor: pointer;
  font-size: 14px;
  padding: 3px;
  border-radius: 4px;
  opacity: 0.7;
  transition: all 0.2s;
}

.edit-icon:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.message-body {
  line-height: 1.6;
}

/* 参考来源样式 */
.sources {
  margin-top: 16px;
  padding: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  background-color: rgba(9, 39, 98, 0.5);
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.source-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #13fff3;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.source-list {
  margin: 0;
  padding-left: 10px;
  list-style-type: none;
}

.source-item {
  margin-bottom: 8px;
  padding: 6px 10px;
  cursor: pointer;
  color: #a8c7ff;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  border-radius: 4px;
  background-color: rgba(9, 39, 98, 0.3);
  position: relative;
}

.source-item:hover {
  background-color: rgba(19, 255, 243, 0.15);
}

.source-item.active-source {
  background-color: rgba(19, 255, 243, 0.3);
  border-left: 3px solid #13fff3;
}

/* 知识图谱来源项特殊样式 */
.graph-source-item {
  background-color: rgba(9, 39, 98, 0.3);
  border-left: 3px solid transparent;
}

.graph-source-item:hover {
  background-color: rgba(19, 255, 243, 0.15);
  border-left: 3px solid #13fff3;
}

.graph-source-item.active-source {
  background-color: rgba(19, 255, 243, 0.3);
  border-left: 3px solid #13fff3;
}

.graph-source-item .source-icon {
  color: #13fff3;
}

/* 问答对来源项特殊样式 */
.qa-pair-source-item {
  background-color: rgba(9, 39, 98, 0.3);
  border-left: 3px solid transparent;
}

.qa-pair-source-item:hover {
  background-color: rgba(255, 165, 0, 0.15);
  border-left: 3px solid #ffa500;
}

.qa-pair-source-item.active-source {
  background-color: rgba(255, 165, 0, 0.3);
  border-left: 3px solid #ffa500;
}

.qa-pair-source-item .source-icon {
  color: #ffa500;
}

.source-icon {
  margin-right: 8px;
  font-size: 16px;
}

.source-text {
  text-decoration: underline;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.view-indicator {
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-left: 8px;
  color: #13fff3;
}

.source-item:hover .view-indicator {
  opacity: 1;
}

.source-item:hover, .active-source {
  background-color: rgba(19, 255, 243, 0.1);
  color: #13fff3;
}

.active-source .view-indicator {
  opacity: 1;
}

/* 跳转到高亮位置按钮样式 */
.highlight-jump-btn {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.highlight-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(9, 39, 98, 0.6);
  padding: 6px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.highlight-counter {
  color: #13fff3;
  font-size: 14px;
  min-width: 60px;
  text-align: center;
  font-weight: bold;
}

.highlight-jump-btn .el-button {
  background: linear-gradient(135deg, #3f63a8, #5c8df6);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.highlight-jump-btn .el-button:hover:not([disabled]) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(63, 99, 168, 0.3);
  background: linear-gradient(135deg, #4e78cf, #6f9dff);
}

.highlight-jump-btn .el-button[disabled] {
  opacity: 0.6;
  background: linear-gradient(135deg, #2a446f, #3a5ba0);
  cursor: not-allowed;
}

.highlight-jump-btn .el-button i {
  font-size: 14px;
}

/* 自定义滚动条 */
.talk-window::-webkit-scrollbar {
  width: 8px;
}

.talk-window::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.talk-window::-webkit-scrollbar-thumb {
  background: rgba(63, 99, 168, 0.6);
  border-radius: 4px;
}

.talk-window::-webkit-scrollbar-thumb:hover {
  background: rgba(93, 130, 201, 0.8);
}

/* 大模型回答相关样式 */
.use-llm-container {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.use-llm-divider {
  display: flex;
  align-items: center;
  text-align: center;
  color: #8e9dbb;
  font-size: 13px;
  margin: 8px 0;
}

.use-llm-divider::before,
.use-llm-divider::after {
  content: '';
  flex: 1;
  border-top: 1px solid #3f63a8;
  margin: 0 8px;
}

.divider-text {
  padding: 0 10px;
  white-space: nowrap;
}

.use-llm-button {
  display: flex;
  justify-content: center;
}

.button-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 4px;
}

/* 编辑区域样式 */
.editing-area {
  width: 75%;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.edit-textarea {
  width: 100%;
  min-height: 180px;
  padding: 12px 16px;
  background-color: rgba(9, 39, 98, 0.8);
  border: none;
  border-radius: 16px;
  border-top-left-radius: 4px;
  color: #fff;
  font-size: inherit;
  line-height: 1.6;
  resize: none;
  margin-bottom: 5px;
  font-family: inherit;
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}

.editing-controls {
  display: flex;
  justify-content: flex-end;
  padding: 0 5px;
}

.save-button {
  background-color: rgba(63, 81, 181, 0.8) !important;
  border-color: rgb(63, 81, 181) !important;
  color: white !important;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
}

.save-button:hover {
  background-color: rgb(63, 81, 181) !important;
  border-color: rgb(79, 125, 243) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 添加滚动条样式 */
.edit-textarea::-webkit-scrollbar {
  width: 8px;
}

.edit-textarea::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.edit-textarea::-webkit-scrollbar-thumb {
  background: rgba(63, 81, 181, 0.6);
  border-radius: 4px;
}

.edit-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(63, 81, 181, 0.8);
}

/* 深度思考部分样式 */
.thinking-section {
  margin-bottom: 16px;
  border-radius: 6px;
}

.thinking-title {
  color: #13fff3;
  font-size: 14px;
}

.thinking-content {
  padding: 10px;
  background-color: rgba(19, 255, 243, 0.1);
  border-radius: 6px;
}

/* 确保markdown内容不会在预览模式下变窄 */
.preview-active .markdown-body {
  width: 100%;
  min-width: 0;
}

/* 增强源文件引用容器样式，保持宽度 */
.preview-active .sources {
  width: 100%;
  min-width: 0;
}

/* 添加调用大模型按钮的样式 */
.llm-query-btn {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

.llm-query-btn .el-button {
  background: linear-gradient(135deg, #13fff3, #19aeff);
  border: none;
  color: #001e57;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.llm-query-btn .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(19, 255, 243, 0.3);
  background: linear-gradient(135deg, #00e6da, #0095ff);
}

.llm-query-btn .el-button i {
  font-size: 16px;
}

.llm-query-btn .el-button.is-loading {
  opacity: 0.8;
  transform: none;
}

/* 图谱预览相关样式 */
.graph-view-overlay {
  position: absolute;
  top: 60px; /* 调整到关闭对话按钮下方 */
  right: 0; /* 紧贴页面右侧 */
  bottom: 0;
  width: 37%; /* 与文件预览相同大小 */
  max-width: none;
  z-index: 5;
  border-left: 2px solid #3f63a8;
  border-top: 2px solid #3f63a8;
  border-top-left-radius: 8px;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 图谱标题和操作按钮样式 */
.graph-data {
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(19, 58, 138, 0.2);
  border-radius: 6px;
}

.graph-title {
  font-weight: bold;
  color: #13fff3;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.graph-action {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.view-graph-btn {
  background-color: #13a6ff !important;
  border: 1px solid #1396ee !important;
  color: white !important;
}
</style>

<style>
  /* 添加 Markdown 样式 */
  .markdown-body {
    color: #fff;
    line-height: 1.6;
  }

  .markdown-body h1,
  .markdown-body h2,
  .markdown-body h3,
  .markdown-body h4,
  .markdown-body h5,
  .markdown-body h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  .markdown-body code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.5);
    border-radius: 6px;
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  }

  .markdown-body pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: rgba(27, 31, 35, 0.5);
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .markdown-body pre code {
    padding: 0;
    margin: 0;
    background-color: transparent;
  }

  .markdown-body a {
    color: #58a6ff;
    text-decoration: none;
  }

  .markdown-body a:hover {
    text-decoration: underline;
  }

  .markdown-body blockquote {
    padding: 0 1em;
    color: #8b949e;
    border-left: 0.25em solid #30363d;
    margin: 0 0 16px 0;
  }

  .markdown-body ul,
  .markdown-body ol {
    padding-left: 2em;
    margin-bottom: 16px;
  }

  .markdown-body table {
    display: block;
    width: 100%;
    overflow: auto;
    margin-bottom: 16px;
    border-spacing: 0;
    border-collapse: collapse;
  }

  .markdown-body table th,
  .markdown-body table td {
    padding: 6px 13px;
    border: 1px solid #30363d;
  }

  .markdown-body table tr {
    background-color: #0d1117;
    border-top: 1px solid #30363d;
  }

  .markdown-body table tr:nth-child(2n) {
    background-color: #161b22;
  }

  .markdown-body img {
    max-width: 100%;
    box-sizing: border-box;
  }

  .markdown-body p {
    margin-bottom: 16px;
  }
</style>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.aichat.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.ai.aichat.model.entity.User">
            <id property="id" column="id" />
            <result property="userAccount" column="user_account" />
            <result property="userPassword" column="user_password" />
            <result property="userName" column="user_name" />
            <result property="userAvatar" column="user_avatar" />
            <result property="userRole" column="user_role" />
            <result property="editTime" column="edit_time" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="isdelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_account,user_password,user_name,user_avatar,user_role,
        edit_time,create_time,update_time,isDelete
    </sql>
</mapper>

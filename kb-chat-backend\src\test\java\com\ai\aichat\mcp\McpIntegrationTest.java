//package com.ai.aichat.mcp;
//
//import cn.hutool.core.lang.UUID;
//import com.ai.aichat.love.LoveApp;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Assertions;
//import org.springframework.ai.tool.ToolCallbackProvider;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.util.concurrent.TimeUnit;
//
///**
// * MCP集成测试类
// * 测试Model Context Protocol功能的各个方面
// */
//@Slf4j
//@SpringBootTest
//@ActiveProfiles("local")
//@DisplayName("MCP集成测试")
//class McpIntegrationTest {
//
//    @Resource
//    private LoveApp loveApp;
//
//    @Resource
//    private ToolCallbackProvider toolCallbackProvider;
//
//    private String testChatId;
//
//    @BeforeEach
//    void setUp() {
//        testChatId = UUID.randomUUID().toString();
//        log.info("测试会话ID: {}", testChatId);
//    }
//
//    @Test
//    @DisplayName("测试MCP客户端连接状态")
//    void testMcpClientConnection() {
//        // 通过测试LoveApp是否能正常工作来验证MCP连接
//        Assertions.assertNotNull(loveApp, "LoveApp应该已初始化");
//        log.info("MCP功能通过LoveApp验证正常");
//    }
//
//    @Test
//    @DisplayName("测试工具回调提供者")
//    void testToolCallbackProvider() {
//        Assertions.assertNotNull(toolCallbackProvider, "工具回调提供者应该已初始化");
//
//        var tools = toolCallbackProvider.getToolCallbacks();
//        Assertions.assertNotNull(tools, "工具列表不应为空");
//        Assertions.assertTrue(tools.length > 0, "应该至少有一个工具可用");
//
//        log.info("可用工具数量: {}", tools.length);
//        log.info("工具回调提供者测试通过");
//    }
//
//    @Test
//    @DisplayName("测试约会地点推荐 - 广州塔")
//    void testDateLocationRecommendation_GuangzhouTower() {
//        String message = "广州塔附近适合约会的地方？";
//        String response = loveApp.doChatWithMcp(message, testChatId);
//
//        Assertions.assertNotNull(response, "响应不应为空");
//        Assertions.assertFalse(response.trim().isEmpty(), "响应内容不应为空");
//
//        // 验证响应内容包含相关关键词
//        String lowerResponse = response.toLowerCase();
//        boolean containsRelevantInfo = lowerResponse.contains("广州塔") ||
//                                     lowerResponse.contains("约会") ||
//                                     lowerResponse.contains("地方") ||
//                                     lowerResponse.contains("推荐");
//
//        Assertions.assertTrue(containsRelevantInfo, "响应应包含相关的约会地点信息");
//        log.info("广州塔约会地点推荐测试通过，响应长度: {}", response.length());
//    }
//
//    @Test
//    @DisplayName("测试约会地点推荐 - 北京")
//    void testDateLocationRecommendation_Beijing() {
//        String message = "北京有哪些适合情侣约会的浪漫地方？";
//        String response = loveApp.doChatWithMcp(message, testChatId);
//
//        Assertions.assertNotNull(response, "响应不应为空");
//        Assertions.assertFalse(response.trim().isEmpty(), "响应内容不应为空");
//
//        log.info("北京约会地点推荐测试通过，响应: {}", response.substring(0, Math.min(100, response.length())));
//    }
//
//    @Test
//    @DisplayName("测试约会地点推荐 - 上海")
//    void testDateLocationRecommendation_Shanghai() {
//        String message = "上海外滩附近有什么好的约会餐厅推荐？";
//        String response = loveApp.doChatWithMcp(message, testChatId);
//
//        Assertions.assertNotNull(response, "响应不应为空");
//        Assertions.assertFalse(response.trim().isEmpty(), "响应内容不应为空");
//
//        log.info("上海约会地点推荐测试通过");
//    }
//
//    @Test
//    @DisplayName("测试MCP响应时间")
//    void testMcpResponseTime() {
//        String message = "深圳有哪些适合约会的地方？";
//
//        long startTime = System.currentTimeMillis();
//        String response = loveApp.doChatWithMcp(message, testChatId);
//        long endTime = System.currentTimeMillis();
//
//        long responseTime = endTime - startTime;
//
//        Assertions.assertNotNull(response, "响应不应为空");
//        Assertions.assertTrue(responseTime < 30000, "响应时间应在30秒内"); // 30秒超时
//
//        log.info("MCP响应时间: {}ms", responseTime);
//    }
//
//    @Test
//    @DisplayName("测试多轮对话上下文")
//    void testMultiTurnConversation() {
//        // 第一轮对话
//        String firstMessage = "杭州西湖附近有什么约会的地方？";
//        String firstResponse = loveApp.doChatWithMcp(firstMessage, testChatId);
//
//        Assertions.assertNotNull(firstResponse, "第一轮响应不应为空");
//        log.info("第一轮对话完成");
//
//        // 第二轮对话 - 基于上下文的追问
//        String secondMessage = "这些地方中哪个最适合晚上去？";
//        String secondResponse = loveApp.doChatWithMcp(secondMessage, testChatId);
//
//        Assertions.assertNotNull(secondResponse, "第二轮响应不应为空");
//        log.info("多轮对话测试完成");
//    }
//
//    @Test
//    @DisplayName("测试错误处理 - 无效地点")
//    void testErrorHandling_InvalidLocation() {
//        String message = "火星上有什么约会的地方？";
//        String response = loveApp.doChatWithMcp(message, testChatId);
//
//        Assertions.assertNotNull(response, "即使是无效地点，也应该有响应");
//        // 应该有礼貌的回复，而不是抛出异常
//        log.info("错误处理测试通过");
//    }
//
//    @Test
//    @DisplayName("测试特殊字符处理")
//    void testSpecialCharacterHandling() {
//        String message = "成都春熙路附近适合约会的地方？💕";
//        String response = loveApp.doChatWithMcp(message, testChatId);
//
//        Assertions.assertNotNull(response, "包含特殊字符的请求应该正常处理");
//        log.info("特殊字符处理测试通过");
//    }
//
//    @Test
//    @DisplayName("测试长文本处理")
//    void testLongTextHandling() {
//        String message = "我想在西安找一个特别浪漫的地方，最好是有历史文化底蕴的，" +
//                        "适合和女朋友一起去的，环境要优雅，氛围要好，价格适中，" +
//                        "交通便利，最好还能看到美丽的风景，请给我详细推荐几个地方。";
//        String response = loveApp.doChatWithMcp(message, testChatId);
//
//        Assertions.assertNotNull(response, "长文本请求应该正常处理");
//        log.info("长文本处理测试通过，响应长度: {}", response.length());
//    }
//}

package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.model.dto.qa.*;
import com.ai.aichat.model.vo.qa.QaHistoryVo;
import com.ai.aichat.model.vo.qa.QaListVo;
import com.ai.aichat.service.QaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 问答对管理控制器
 */
@Tag(name = "问答对管理接口")
@RequiredArgsConstructor
@RestController
public class QaController {

    private final QaService qaService;

    @Operation(summary = "获取问答库列表")
    @GetMapping("/list_qa")
    public BaseResponse<QaListVo> listQa() {
        QaListVo result = qaService.listQa();
        return ResultUtils.success(result);
    }

    @Operation(summary = "创建问答库")
    @PostMapping("/create_qa")
    public BaseResponse<String> createQa(@Valid @RequestBody QaBaseCreateDto dto) {
        ThrowUtils.throwIf(dto == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        String result = qaService.createQa(dto);
        return ResultUtils.success(result);
    }

    @Operation(summary = "更新问答库")
    @PutMapping("/update_qa")
    public BaseResponse<String> updateQa(@Valid @RequestBody QaBaseUpdateDto dto) {
        ThrowUtils.throwIf(dto == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        String result = qaService.updateQa(dto);
        return ResultUtils.success(result);
    }

    @Operation(summary = "删除问答库")
    @DeleteMapping("/delete_qa_base")
    public BaseResponse<String> deleteQaBase(@Valid @RequestBody QaBaseDeleteDto dto) {
        ThrowUtils.throwIf(dto == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        String result = qaService.deleteQaBase(dto);
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取问答对历史")
    @GetMapping("/get_qa_history")
    public BaseResponse<QaHistoryVo> getQaHistory(
            @Parameter(description = "问答库名称", required = true) @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名称", required = true) @RequestParam("file_name") String fileName) {
        ThrowUtils.throwIf(kbName == null || kbName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "问答库名称不能为空");
        ThrowUtils.throwIf(fileName == null || fileName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "文件名称不能为空");
        QaHistoryVo result = qaService.getQaHistory(kbName, fileName);
        return ResultUtils.success(result);
    }

    @Operation(summary = "删除问答对文件")
    @DeleteMapping("/delete_qa")
    public BaseResponse<String> deleteQa(
            @Parameter(description = "问答库名称", required = true) @RequestParam("kb_name") String kbName,
            @Parameter(description = "文件名称", required = true) @RequestParam("file_name") String fileName) {
        ThrowUtils.throwIf(kbName == null || kbName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "问答库名称不能为空");
        ThrowUtils.throwIf(fileName == null || fileName.trim().isEmpty(), ErrorCode.PARAMS_ERROR, "文件名称不能为空");
        String result = qaService.deleteQa(kbName, fileName);
        return ResultUtils.success(result);
    }

    @Operation(summary = "保存问答对")
    @PostMapping("/save_chat")
    public BaseResponse<String> saveChat(@Valid @RequestBody SaveChatDto dto) {
        ThrowUtils.throwIf(dto == null, ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        String result = qaService.saveChat(dto);
        return ResultUtils.success(result);
    }
}
package com.ai.aichat.util;

import com.ai.aichat.model.entity.Conversation;
import com.ai.aichat.service.ConversationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据库持久化的聊天历史Repository实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChatHistoryRepository {

    private final ConversationService conversationService;

    /**
     * 保存会话记录
     * @param type 业务类型，如： chat、service、pdf
     * @param chatId 会话ID
     */
    public void save(String type, String chatId) {
        // 检查会话是否已存在
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", chatId)
                   .eq("chat_type", type);

        Conversation existingConversation = conversationService.getOne(queryWrapper);

        if (existingConversation == null) {
            // 如果不存在，则创建新会话
            conversationService.saveConversation(type, chatId);
            log.debug("Created new conversation: type={}, chatId={}", type, chatId);
        }
    }

    /**
     * 获取会话ID列表
     * @param type 业务类型，如： chat、service、pdf
     * @return 会话ID列表
     */
    public List<String> getChatIds(String type) {
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("chat_type", type)
                   .eq("is_delete", 0)
                   .orderByDesc("update_time");

        List<Conversation> conversations = conversationService.list(queryWrapper);

        return conversations.stream()
                .map(Conversation::getSessionId)
                .collect(Collectors.toList());
    }

    public void save(String type, String chatId, String kbName) {
        // 检查会话是否已存在
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", chatId)
                .eq("chat_type", type);

        Conversation existingConversation = conversationService.getOne(queryWrapper);

        if (existingConversation == null) {
            // 如果不存在，则创建新会话
            conversationService.saveConversation(type, chatId, kbName);
            log.debug("Created new conversation: type={}, chatId={}, kbName={}", type, chatId, kbName);
        }
    }
}

package com.ai.aichat.model.dto.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 删除问答库请求DTO
 */
@Data
@Schema(description = "删除问答库请求")
public class QaBaseDeleteDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问答库名称
     */
    @NotBlank(message = "问答库名称不能为空")
    @Schema(description = "问答库名称", example = "技术问答库", required = true)
    private String kbName;
}

package com.ai.aichat.model.vo.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问答库列表响应VO
 */
@Data
@Schema(description = "问答库列表")
public class QaListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问答库列表
     */
    @Schema(description = "问答库列表")
    private List<QaBaseVo> kbList;
}

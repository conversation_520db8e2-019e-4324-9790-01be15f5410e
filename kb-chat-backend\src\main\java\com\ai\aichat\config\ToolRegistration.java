package com.ai.aichat.config;

import com.ai.aichat.tools.*;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class ToolRegistration {

    @Value("${search-api.api-key}")
    private String searchApiKey;

    @Bean
    public ToolCallback[] allTools() {
        FileOperationTool fileOperationTool = new FileOperationTool();
        WebSearchTool webSearchTool = new WebSearchTool(searchApiKey);
        WebScrapingTool webScrapingTool = new WebScrapingTool();
        ResourceDownloadTool resourceDownloadTool = new ResourceDownloadTool();
        WindowsTerminalOperationTool terminalOperationTool = new WindowsTerminalOperationTool();
        PDFGenerationTool pdfGenerationTool = new PDFGenerationTool();
        TerminateTool  terminateTool = new TerminateTool();
        KnowledgeBaseSearch knowledgeBaseSearch = new KnowledgeBaseSearch();
        TimeTools timeTools = new TimeTools();

        return ToolCallbacks.from(
            fileOperationTool,
            webSearchTool,
            webScrapingTool,
            resourceDownloadTool,
            terminalOperationTool,
            pdfGenerationTool,
            knowledgeBaseSearch,
            terminateTool,
            timeTools
        );
    }

    @Bean("localToolCallbackProvider")
    public ToolCallbackProvider localToolCallbackProvider(ToolCallback[] allTools) {
        System.out.println("创建本地工具回调提供者，工具数量: " + allTools.length);
        return () -> allTools;
    }
}

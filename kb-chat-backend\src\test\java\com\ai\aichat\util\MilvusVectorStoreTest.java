package com.ai.aichat.util;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class MilvusVectorStoreTest {

    @Autowired
    private MilvusVectorStore milvusVectorStore;
    @Test
    void delete() {
        milvusVectorStore.delete("kb_6", "《世界军事战争史-266》第二次世界大战中军事战略的发展（上）.docx");
    }
}
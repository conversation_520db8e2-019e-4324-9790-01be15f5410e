package com.ai.aichat.mapper;

import com.ai.aichat.model.entity.TrainingTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 训练任务Mapper接口
 */
@Mapper
public interface TrainingTaskMapper extends BaseMapper<TrainingTask> {

    /**
     * 获取最新的训练任务
     */
    @Select("SELECT * FROM training_task WHERE is_deleted = 0 ORDER BY create_time DESC LIMIT 1")
    TrainingTask getLatestTrainingTask();

}

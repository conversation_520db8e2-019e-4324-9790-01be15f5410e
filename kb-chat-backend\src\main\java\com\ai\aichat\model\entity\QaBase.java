package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 问答库实体类
 * @TableName qa_base
 */
@TableName(value = "qa_base")
@Data
public class QaBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 问答库名称，唯一
     */
    private String name;

    /**
     * 问答库描述
     */
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 软删除标记 0-正常 1-删除
     */
    @TableLogic
    private Integer isDelete;
}

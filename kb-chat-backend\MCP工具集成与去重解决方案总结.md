# MCP工具集成与去重解决方案总结

## 🎯 问题背景

在集成高德地图MCP工具到Spring AI项目时，遇到了**工具重复注册**的问题：

```
java.lang.IllegalStateException: Multiple tools with the same name (searchKnowledgeBaseSearch, downloadResource, generatePDF, scrapeWebPage, searchWeb, readFile, executeTerminalCommand, writeFile) found in ToolCallingChatOptions
```

## 🔍 根本原因分析

### 1. Spring AI MCP自动配置Bug

在`spring-ai-mcp-client-webflux-spring-boot-starter-1.0.0.M6`中存在一个类路径错误：

```java
@ConditionalOnMissingClass("io.modelcontextprotocol.client.transport.public class WebFluxSseClientTransport")
```

这个错误的类路径导致：
- `SseHttpClientTransportAutoConfiguration` 被注入
- `SseWebFluxTransportAutoConfiguration` 也被注入  
- 两个配置都创建了相同的MCP客户端
- 所有工具都被**重复注册**了两次

### 2. 配置时机问题

初期尝试使用`@Lazy`注解来延迟初始化，但这只是延缓了问题的出现，并没有解决根本的重复注册问题。

## 🛠️ 解决方案演进

### 方案1：延迟初始化（失败）

```java
@Bean
@Primary
@Lazy
public ToolCallbackProvider combinedToolCallbackProvider(
    @Qualifier("localToolCallbackProvider") ToolCallbackProvider localToolCallbackProvider,
    ApplicationContext applicationContext) {
    // 延迟初始化逻辑
}
```

**问题**：只是延迟了工具创建，但重复注册问题依然存在。

### 方案2：缓存机制（失败）

```java
private final AtomicReference<FunctionCallback[]> cachedTools = new AtomicReference<>();

public ToolCallbackProvider combinedToolCallbackProvider() {
    return () -> {
        FunctionCallback[] cached = cachedTools.get();
        if (cached != null) {
            return cached;
        }
        // 创建并缓存工具
    };
}
```

**问题**：缓存无法解决Spring容器级别的重复Bean创建问题。

### 方案3：禁用自动配置（复杂）

```yaml
spring:
  autoconfigure:
    exclude:
      - org.springframework.ai.mcp.client.autoconfigure.McpToolCallbackAutoConfiguration
```

**问题**：需要手动实现所有MCP配置，过于复杂且容易出错。

### 方案4：去重配置（成功✅）

最终采用的解决方案：创建一个去重的工具配置类。

## 🎯 最终解决方案

### 1. 创建去重配置类

```java
@Slf4j
@Configuration
public class DeduplicatedToolConfiguration {

    @Bean
    @Primary
    @Qualifier("combinedToolCallbackProvider")
    public ToolCallbackProvider combinedToolCallbackProvider(
            @Qualifier("localToolCallbackProvider") ToolCallbackProvider localToolCallbackProvider,
            ApplicationContext applicationContext) {
        
        return () -> {
            List<FunctionCallback> allTools = new ArrayList<>();
            Set<String> toolNames = new HashSet<>(); // 关键：使用HashSet去重
            
            // 添加本地工具（去重）
            FunctionCallback[] localTools = localToolCallbackProvider.getToolCallbacks();
            for (FunctionCallback tool : localTools) {
                String toolName = tool.getName();
                if (!toolNames.contains(toolName)) {
                    allTools.add(tool);
                    toolNames.add(toolName);
                } else {
                    log.warn("跳过重复的本地工具: {}", toolName);
                }
            }
            
            // 添加MCP工具（去重）
            try {
                SyncMcpToolCallbackProvider mcpProvider = 
                    applicationContext.getBean("mcpToolCallbacks", SyncMcpToolCallbackProvider.class);
                
                if (mcpProvider != null) {
                    ToolCallback[] mcpTools = mcpProvider.getToolCallbacks();
                    for (ToolCallback mcpTool : mcpTools) {
                        if (mcpTool instanceof FunctionCallback) {
                            FunctionCallback funcTool = (FunctionCallback) mcpTool;
                            String toolName = funcTool.getName();
                            
                            if (!toolNames.contains(toolName)) {
                                allTools.add(funcTool);
                                toolNames.add(toolName);
                            } else {
                                log.warn("跳过重复的MCP工具: {}", toolName);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("无法获取MCP工具: {}", e.getMessage());
            }
            
            return allTools.toArray(new FunctionCallback[0]);
        };
    }
}
```

### 2. 修改服务层配置

```java
@Service
public class ChatStreamServiceImpl implements ChatStreamService {

    @Resource
    @Qualifier("combinedToolCallbackProvider")  // 使用去重后的工具提供者
    private ToolCallbackProvider toolCallbackProvider;

    // 使用工具
    return chatClient.prompt()
            .user(prompt)
            .advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, chatId))
            .tools(toolCallbackProvider)  // 注入去重后的工具
            .stream()
            .content();
}
```

### 3. 修复Agent类中的ChatOptions配置

```java
// 原来使用DashScopeChatOptions（错误）
this.chatOptions = DashScopeChatOptions.builder()
        .withProxyToolCalls(true)
        .build();

// 修改为OpenAiChatOptions（正确）
this.chatOptions = OpenAiChatOptions.builder().build();
```

### 3. 保证高德地图MCP注册

#### MCP服务器配置

```json
// mcp-servers.json
{
  "mcpServers": {
    "amap-maps": {
      "command": "npx.cmd",
      "args": ["-y", "@amap/mcp-server-amap-maps"]
    }
  }
}
```

#### 依赖配置

```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-mcp-client</artifactId>
</dependency>
```

#### 自动配置验证

通过日志确认MCP工具成功注册：

```
2025-06-14T11:50:01.235+08:00  INFO 39172 --- [ai-chat] [pool-5-thread-1] i.m.c.transport.StdioClientTransport     : STDERR Message received: Amap Maps MCP Server running on stdio
2025-06-14T11:50:01.259+08:00  INFO 39172 --- [ai-chat] [pool-2-thread-1] i.m.client.McpAsyncClient                : Server response with Protocol: 2024-11-05
```

## 🔧 关键技术点

### 1. 去重机制

```java
Set<String> toolNames = new HashSet<>();  // 工具名称去重集合

// 检查工具是否已存在
if (!toolNames.contains(toolName)) {
    allTools.add(tool);
    toolNames.add(toolName);
} else {
    log.warn("跳过重复的工具: {}", toolName);
}
```

### 2. Bean优先级控制

```java
@Bean
@Primary  // 设置为主要Bean
@Qualifier("combinedToolCallbackProvider")  // 明确的限定符
public ToolCallbackProvider combinedToolCallbackProvider() {
    // 实现
}
```

### 3. 依赖注入控制

```java
// 在需要使用的地方明确指定Bean
@Resource
@Qualifier("combinedToolCallbackProvider")
private ToolCallbackProvider toolCallbackProvider;
```

### 4. 异常处理

```java
try {
    // 尝试获取MCP工具
    SyncMcpToolCallbackProvider mcpProvider = 
        applicationContext.getBean("mcpToolCallbacks", SyncMcpToolCallbackProvider.class);
} catch (Exception e) {
    log.warn("无法获取MCP工具: {}", e.getMessage());
    // 优雅降级，只使用本地工具
}
```

## 📊 最终效果

### 成功日志示例

```
2025-06-14T11:50:01.271+08:00  INFO 39172 --- [ai-chat] [main] c.a.a.c.DeduplicatedToolConfiguration    : === 创建去重的组合工具回调提供者 ===
2025-06-14T11:50:01.273+08:00  INFO 39172 --- [ai-chat] [main] c.a.a.c.DeduplicatedToolConfiguration    : 本地工具数量: 8
2025-06-14T11:50:01.295+08:00  INFO 39172 --- [ai-chat] [main] c.a.a.c.DeduplicatedToolConfiguration    : MCP工具总数: 12, 实际添加: 12
2025-06-14T11:50:01.295+08:00  INFO 39172 --- [ai-chat] [main] c.a.a.c.DeduplicatedToolConfiguration    : ✅ 成功集成去重的MCP工具
2025-06-14T11:50:01.295+08:00  INFO 39172 --- [ai-chat] [main] c.a.a.c.DeduplicatedToolConfiguration    : 最终去重后工具总数: 20
```

### 工具列表

**本地工具 (8个)**：
1. readFile
2. writeFile
3. searchWeb
4. scrapeWebPage
5. downloadResource
6. executeTerminalCommand
7. generatePDF
8. searchKnowledgeBaseSearch

**MCP工具 (12个)**：
9. spring_ai_mcp_client_amap_maps_maps_regeocode
10. spring_ai_mcp_client_amap_maps_maps_geo
11. spring_ai_mcp_client_amap_maps_maps_ip_location
12. spring_ai_mcp_client_amap_maps_maps_weather
13. spring_ai_mcp_client_amap_maps_maps_search_detail
14. spring_ai_mcp_client_amap_maps_maps_bicycling
15. spring_ai_mcp_client_amap_maps_maps_direction_walking
16. spring_ai_mcp_client_amap_maps_maps_direction_driving
17. spring_ai_mcp_client_amap_maps_maps_direction_transit_integrated
18. spring_ai_mcp_client_amap_maps_maps_distance
19. spring_ai_mcp_client_amap_maps_maps_text_search
20. spring_ai_mcp_client_amap_maps_maps_around_search

## 🎯 核心优势

1. **✅ 完全解决重复注册问题**：通过HashSet去重机制
2. **✅ 保持自动配置**：不需要禁用Spring AI的自动配置
3. **✅ 优雅降级**：MCP工具获取失败时自动回退到本地工具
4. **✅ 详细日志**：便于调试和监控
5. **✅ 可扩展性**：易于添加新的工具类型

## 🤖 智能体功能增强

### 1. 循环检测机制

为`ToolCallAgent`添加了循环检测功能，防止智能体陷入无限循环：

```java
// 循环检测属性
private int duplicateThreshold = 2;  // 重复阈值

// 检测方法
protected boolean isStuck() {
    // 检查最近的助手消息是否重复
    // 如果重复次数超过阈值，返回true
}

// 处理方法
protected void handleStuckState() {
    String stuckPrompt = "观察到重复响应。考虑新策略，避免重复已尝试过的无效路径。";
    // 添加提示到下一步执行中
}
```

### 2. 用户交互机制

支持智能体主动向用户询问信息：

```java
// 在think()方法中检查ASK_USER标记
if (result != null && result.contains("[ASK_USER]")) {
    return handleUserInteraction(result);
}

// 处理用户交互
protected boolean handleUserInteraction(String content) {
    // 提取问题并向用户展示
    // 获取用户输入并添加到消息历史
    // 返回true继续执行
}
```

### 3. 系统提示增强

默认系统提示包含用户交互指令：

```java
private String getDefaultSystemPrompt() {
    return "你是一个智能助手，可以使用各种工具来帮助用户完成任务。\n" +
           "当你需要用户提供更多信息或确认时，请在回复中使用 [ASK_USER]问题内容[/ASK_USER] 格式。\n" +
           "例如：[ASK_USER]请确认是否要删除这个文件？[/ASK_USER]\n" +
           "请根据用户的需求，合理选择和使用可用的工具，并在必要时寻求用户的帮助。";
}
```

## 🔮 后续优化建议

1. **监控工具调用**：添加工具调用统计和性能监控
2. **工具分组**：按功能对工具进行分组管理
3. **动态配置**：支持运行时动态添加/移除工具
4. **错误重试**：为MCP工具调用添加重试机制
5. **智能循环检测**：基于语义相似度的循环检测
6. **多种交互方式**：支持GUI、Web接口等用户交互方式

## 📝 关键文件修改清单

### 新增文件

1. **`DeduplicatedToolConfiguration.java`** - 去重配置类
   - 实现工具去重逻辑
   - 提供统一的工具回调提供者

2. **`EnhancedToolCallAgentTest.java`** - 增强智能体测试类
   - 测试循环检测功能
   - 测试用户交互功能
   - 提供使用示例

3. **`智能体循环检测与用户交互功能说明.md`** - 功能说明文档
   - 详细的功能介绍和使用方法
   - 代码示例和配置说明

### 修改文件

1. **`ChatStreamServiceImpl.java`**
   - 修改依赖注入：使用`@Qualifier("combinedToolCallbackProvider")`
   - 确保使用去重后的工具提供者

2. **`ToolCallAgent.java`**
   - 修改ChatOptions：从`DashScopeChatOptions`改为`OpenAiChatOptions`
   - 添加正确的导入：`org.springframework.ai.openai.OpenAiChatOptions`
   - 移除不支持的`withProxyToolCalls`方法调用
   - **新增循环检测功能**：`isStuck()`、`handleStuckState()`方法
   - **新增用户交互功能**：`handleUserInteraction()`方法
   - **增强系统提示**：包含用户交互指令的默认提示
   - **添加配置属性**：`duplicateThreshold`、`Scanner`等

3. **`pom.xml`**
   - 保持使用`spring-ai-starter-mcp-client`依赖
   - 避免使用有bug的WebFlux版本

4. **`mcp-servers.json`**
   - 配置高德地图MCP服务器
   - 使用`npx.cmd`命令（Windows环境）

## 🚀 测试验证

### 启动验证

服务启动时应看到以下日志：

```
=== 创建去重的组合工具回调提供者 ===
本地工具数量: 8
MCP工具总数: 12, 实际添加: 12
✅ 成功集成去重的MCP工具
最终去重后工具总数: 20
```

### 功能测试

可以通过以下问题测试MCP工具：

1. **天气查询**：`北京今天天气怎么样？`
2. **地点搜索**：`帮我搜索北京天安门附近的餐厅`
3. **路线规划**：`从北京西站到天安门广场怎么走？`

## 📚 技术要点总结

### @Lazy注解的作用

- **延迟初始化**：推迟Bean的创建时机
- **解决循环依赖**：在某些情况下避免循环依赖问题
- **本项目中的局限性**：只能延迟问题出现，无法解决根本的重复注册

### 高德地图MCP注册保证

1. **自动配置**：Spring AI自动扫描`mcp-servers.json`
2. **进程启动**：通过`StdioClientTransport`启动MCP服务器进程
3. **协议握手**：MCP客户端与服务器建立连接并交换能力信息
4. **工具注册**：服务器返回可用工具列表，客户端注册为Spring Bean

### 去重机制核心

1. **HashSet去重**：基于工具名称的唯一性检查
2. **优先级控制**：本地工具优先，MCP工具补充
3. **异常处理**：MCP工具获取失败时优雅降级
4. **日志监控**：详细记录工具添加和跳过情况

---

**总结**：通过创建去重配置类，我们成功解决了Spring AI MCP工具重复注册的问题，实现了本地工具和MCP工具的完美集成，为AI应用提供了丰富的功能扩展能力。

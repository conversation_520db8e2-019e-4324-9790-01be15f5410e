package com.ai.aichat.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 去重的工具配置，避免工具重复注册
 */
@Slf4j
@Configuration
public class DeduplicatedToolConfiguration {

    /**
     * 创建去重的工具回调提供者
     */
    @Bean
    @Primary
    @Qualifier("combinedToolCallbackProvider")
    public ToolCallbackProvider combinedToolCallbackProvider(
            @Qualifier("localToolCallbackProvider") ToolCallbackProvider localToolCallbackProvider,
            ApplicationContext applicationContext) {
        
        return () -> {
            log.info("=== 创建去重的组合工具回调提供者 ===");
            
            List<FunctionCallback> allTools = new ArrayList<>();
            Set<String> toolNames = new HashSet<>();
            
            // 添加本地工具
            FunctionCallback[] localTools = localToolCallbackProvider.getToolCallbacks();
            for (FunctionCallback tool : localTools) {
                String toolName = tool.getName();
                if (!toolNames.contains(toolName)) {
                    allTools.add(tool);
                    toolNames.add(toolName);
                    log.debug("添加本地工具: {}", toolName);
                } else {
                    log.warn("跳过重复的本地工具: {}", toolName);
                }
            }
            log.info("本地工具数量: {}", localTools.length);
            
            // 尝试获取MCP工具
            try {
                SyncMcpToolCallbackProvider mcpProvider = applicationContext.getBean("mcpToolCallbacks", SyncMcpToolCallbackProvider.class);
                
                if (mcpProvider != null) {
                    ToolCallback[] mcpTools = mcpProvider.getToolCallbacks();
                    
                    int mcpToolsAdded = 0;
                    for (ToolCallback mcpTool : mcpTools) {
                        if (mcpTool instanceof FunctionCallback) {
                            FunctionCallback funcTool = (FunctionCallback) mcpTool;
                            String toolName = funcTool.getName();
                            
                            if (!toolNames.contains(toolName)) {
                                allTools.add(funcTool);
                                toolNames.add(toolName);
                                mcpToolsAdded++;
                                log.debug("添加MCP工具: {}", toolName);
                            } else {
                                log.warn("跳过重复的MCP工具: {}", toolName);
                            }
                        }
                    }
                    
                    log.info("MCP工具总数: {}, 实际添加: {}", mcpTools.length, mcpToolsAdded);
                    log.info("✅ 成功集成去重的MCP工具");
                } else {
                    log.warn("⚠️ MCP工具提供者为null，只使用本地工具");
                }
            } catch (Exception e) {
                log.warn("⚠️ 无法获取MCP工具: {}，只使用本地工具", e.getMessage());
            }
            
            log.info("最终去重后工具总数: {}", allTools.size());
            
            // 列出所有工具名称
            log.info("所有工具列表:");
            for (int i = 0; i < allTools.size(); i++) {
                log.info("  {}. {}", i + 1, allTools.get(i).getName());
            }
            
            return allTools.toArray(new FunctionCallback[0]);
        };
    }
}

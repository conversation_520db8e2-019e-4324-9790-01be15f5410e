package com.ai.aichat.service.impl;


import cn.hutool.core.util.IdUtil;
import com.ai.aichat.model.dto.chat.ChatRequestDto;
import com.ai.aichat.model.vo.chat.SimpleChatResponseVo;
import com.ai.aichat.model.vo.chat.StreamResponseVo;
import com.ai.aichat.model.entity.Conversation;
import com.ai.aichat.util.ChatHistoryRepository;
import com.ai.aichat.service.ChatStreamService;
import com.ai.aichat.service.ConversationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY;

/**
 * 聊天流服务实现类，处理流式响应并保存消息到数据库
 */
@Slf4j
@Service
public class ChatStreamServiceImpl implements ChatStreamService {

    @Resource
    private ToolCallback[] allTools;

    @Resource
    @Qualifier("combinedToolCallbackProvider")
    private ToolCallbackProvider toolCallbackProvider;

    private final ChatClient chatClient;
    private final ConversationService conversationService;
    private final ObjectMapper objectMapper;
    private final ChatHistoryRepository chatHistoryRepository;

    public ChatStreamServiceImpl(ChatClient chatClient,
                               ConversationService conversationService,
                               ObjectMapper objectMapper,
                               ChatHistoryRepository chatHistoryRepository) {
        this.chatClient = chatClient;
        this.conversationService = conversationService;
        this.objectMapper = objectMapper;
        this.chatHistoryRepository = chatHistoryRepository;
    }

    @Override
    public Flux<String> chat(String prompt, String chatId, String chatType) {
        return processChat(prompt, chatId, chatType, chatClient);
    }

    @Override
    public Flux<String> chatWithSSE(String prompt, String chatId, String chatType) {
        // 获取或创建会话（仅用于更新会话元信息，不保存消息）
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", chatId).eq("chat_type", chatType);
        Conversation conversation = conversationService.getOne(queryWrapper);

        // 如果会话存在，更新会话标题（仅在第一次时）
        if (conversation != null) {
            updateConversationTitleIfNeeded(conversation, prompt);
        }

        // 请求模型并处理响应，转换为SSE格式
        // 消息保存由DatabaseChatMemory通过MessageChatMemoryAdvisor自动处理
        return chatClient.prompt()
                .user(prompt)
                .advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, chatId))
                .tools(toolCallbackProvider)
                .stream()
                .content()
                .map(chunk -> {
                    // 创建简化的响应VO（只包含content和session_id）
                    SimpleChatResponseVo responseData = new SimpleChatResponseVo();
                    responseData.setContent(chunk);
                    responseData.setSession_id(chatId);

                    StreamResponseVo streamResponse = StreamResponseVo.success(responseData);

                    // 转换为JSON字符串（Spring Boot会自动添加SSE格式）
                    try {
                        return objectMapper.writeValueAsString(streamResponse);
                    } catch (JsonProcessingException e) {
                        log.error("Failed to serialize response to JSON: {}", e.getMessage());
                        return objectMapper.valueToTree(StreamResponseVo.error("JSON序列化失败")).toString();
                    }
                })
                .concatWith(Flux.just("[DONE]")) // 添加结束标记
                .doOnError(error -> {
                    log.error("Error in SSE chat stream for conversation {}: {}", chatId, error.getMessage());
                });
    }

    @Override
    public Flux<String> handleDirectChat(String prompt, String chatId) {
        // 保存会话id到数据库
        chatHistoryRepository.save("chat", chatId);

        // 使用ChatStreamService处理聊天请求和消息持久化
        return chat(prompt, chatId, "chat");
    }

    @Override
    public ResponseEntity<Flux<String>> handleSSEChat(ChatRequestDto chatRequestDto) {
        // 获取参数
        String prompt = chatRequestDto.getPrompt();
        String chatId = chatRequestDto.getSession_id();
        if (chatId == null || chatId.trim().isEmpty()) {
            chatId = IdUtil.fastSimpleUUID();
        }

        // 保存会话id到数据库
        chatHistoryRepository.save("chat", chatId);

        Flux<String> stream = chatWithSSE(prompt, chatId, "chat");

        return ResponseEntity.ok()
                .header("Cache-Control", "no-cache")
                .header("Connection", "keep-alive")
                .header("Access-Control-Allow-Origin", "*")
                .header("X-Accel-Buffering", "no")
                .contentType(MediaType.TEXT_EVENT_STREAM)
                .body(stream);
    }

    /**
     * 通用聊天处理方法
     * 注意：消息保存现在完全由DatabaseChatMemory处理，避免重复保存
     */
    private Flux<String> processChat(String prompt, String chatId, String chatType, ChatClient client) {
        // 获取或创建会话（仅用于更新会话元信息，不保存消息）
        QueryWrapper<Conversation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", chatId).eq("chat_type", chatType);
        Conversation conversation = conversationService.getOne(queryWrapper);

        // 如果会话存在，更新会话标题（仅在第一次时）
        if (conversation != null) {
            updateConversationTitleIfNeeded(conversation, prompt);
        }

        // 请求模型并处理响应
        // 消息保存由DatabaseChatMemory通过MessageChatMemoryAdvisor自动处理
        return client.prompt()
                .user(prompt)
                .advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, chatId))
                .tools(allTools)
                .stream()
                .content()
                .doOnError(error -> {
                    log.error("Error in chat stream for conversation {}: {}", chatId, error.getMessage());
                });
    }

    /**
     * 更新会话标题（仅在需要时）
     * 消息保存现在由DatabaseChatMemory处理
     */
    private void updateConversationTitleIfNeeded(Conversation conversation, String prompt) {
        try {
            // 如果是第一条消息，设置标题
            if (conversation.getTitle() == null || conversation.getTitle().trim().isEmpty()) {
                String title = prompt.length() > 20 ? prompt.substring(0, 20) : prompt;
                conversation.setTitle(title);

                // 更新会话
                conversationService.updateById(conversation);

                log.debug("Updated conversation title for {}: {}", conversation.getSessionId(), title);
            }
        } catch (Exception e) {
            log.error("Failed to update conversation title for {}: {}",
                    conversation.getSessionId(), e.getMessage());
        }
    }
}

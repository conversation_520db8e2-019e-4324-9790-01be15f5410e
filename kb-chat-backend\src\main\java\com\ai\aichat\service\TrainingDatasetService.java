package com.ai.aichat.service;

import com.ai.aichat.model.entity.TrainingDataset;
import com.ai.aichat.model.vo.response.TrainingDatasetVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 训练数据集服务接口
 */
public interface TrainingDatasetService extends IService<TrainingDataset> {

    /**
     * 获取所有训练数据集列表
     * @return 训练数据集列表
     */
    List<TrainingDatasetVo> getAllTrainingDatasets();

    /**
     * 根据名称获取训练数据集
     * @param name 数据集名称
     * @return 训练数据集信息
     */
    TrainingDatasetVo getTrainingDatasetByName(String name);

    /**
     * 创建训练数据集
     * @param name 数据集名称
     * @param description 数据集描述
     * @param filePath 文件路径
     * @return 是否创建成功
     */
    Boolean createTrainingDataset(String name, String description, String filePath);

    /**
     * 删除训练数据集
     * @param id 数据集ID
     * @return 是否删除成功
     */
    Boolean deleteTrainingDataset(Long id);

}

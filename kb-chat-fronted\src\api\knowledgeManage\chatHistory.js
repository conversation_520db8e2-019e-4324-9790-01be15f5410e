import request from '@/utils/request'

/**
 * 获取历史会话列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 * @param {string} [params.chat_type] - 聊天类型: 'chat'(普通对话), 'kb_chat'(知识库对话)
 * @param {string} [params.date_range] - 日期范围
 * @returns {Promise} - 返回Promise对象
 */
export function getConversations(params) {
  return request({
    url: '/get_conversations',
    method: 'get',
    params
  })
}

/**
 * 获取指定会话的聊天历史
 * @param {string} sessionId - 会话ID
 * @returns {Promise} - 返回Promise对象
 */
export function getChatHistory(sessionId) {
  return request({
    url: '/chat_history',
    method: 'get',
    params: {
      session_id: sessionId
    }
  })
}

/**
 * 删除指定会话
 * @param {string} sessionId - 会话ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteConversation(sessionId) {
  return request({
    url: `/delete_conversation/${sessionId}`,
    method: 'delete'
  })
}

/**
 * 更新会话标题
 * @param {Object} data - 请求数据
 * @param {string} data.session_id - 会话ID
 * @param {string} data.title - 新标题
 * @returns {Promise} - 返回Promise对象
 */
export function updateConversationTitle(data) {
  return request({
    url: '/update_conversation_title',
    method: 'post',
    data
  })
} 
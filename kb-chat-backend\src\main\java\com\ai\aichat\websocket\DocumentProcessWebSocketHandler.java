package com.ai.aichat.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文档处理进度WebSocket处理器
 */
@Slf4j
@Component
public class DocumentProcessWebSocketHandler implements WebSocketHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储会话，key为sessionId，value为WebSocketSession
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 存储会话与知识库的关联，key为sessionId，value为kbId
    private final Map<String, Long> sessionKbMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        log.info("WebSocket连接建立: sessionId={}", sessionId);
        
        // 发送连接成功消息
        sendMessage(session, Map.of(
            "type", "connected",
            "message", "WebSocket连接成功",
            "sessionId", sessionId
        ));
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();
        log.info("收到WebSocket消息: sessionId={}, message={}", sessionId, payload);
        
        try {
            // 解析消息
            @SuppressWarnings("unchecked")
            Map<String, Object> messageMap = objectMapper.readValue(payload, Map.class);
            String type = (String) messageMap.get("type");
            
            if ("register".equals(type)) {
                // 注册会话与知识库的关联
                Object kbIdObj = messageMap.get("kbId");
                if (kbIdObj != null) {
                    Long kbId = Long.valueOf(kbIdObj.toString());
                    sessionKbMap.put(sessionId, kbId);
                    log.info("注册会话与知识库关联: sessionId={}, kbId={}", sessionId, kbId);
                    
                    sendMessage(session, Map.of(
                        "type", "registered",
                        "message", "注册成功",
                        "kbId", kbId
                    ));
                }
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: sessionId={}", sessionId, e);
            sendMessage(session, Map.of(
                "type", "error",
                "message", "消息处理失败: " + e.getMessage()
            ));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        log.error("WebSocket传输错误: sessionId={}", sessionId, exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        sessionKbMap.remove(sessionId);
        log.info("WebSocket连接关闭: sessionId={}, status={}", sessionId, closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 发送进度消息到指定知识库的所有连接
     */
    public void sendProgressToKb(Long kbId, Map<String, Object> progressData) {
        sessionKbMap.entrySet().stream()
            .filter(entry -> kbId.equals(entry.getValue()))
            .forEach(entry -> {
                String sessionId = entry.getKey();
                WebSocketSession session = sessions.get(sessionId);
                if (session != null && session.isOpen()) {
                    try {
                        sendMessage(session, progressData);
                    } catch (Exception e) {
                        log.error("发送进度消息失败: sessionId={}, kbId={}", sessionId, kbId, e);
                    }
                }
            });
    }

    /**
     * 发送消息到指定会话
     */
    private void sendMessage(WebSocketSession session, Map<String, Object> message) throws IOException {
        if (session.isOpen()) {
            String jsonMessage = objectMapper.writeValueAsString(message);
            session.sendMessage(new TextMessage(jsonMessage));
            log.debug("发送WebSocket消息: sessionId={}, message={}", session.getId(), jsonMessage);
        }
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return sessions.size();
    }

    /**
     * 获取指定知识库的连接数
     */
    public long getKbConnectionCount(Long kbId) {
        return sessionKbMap.values().stream()
            .filter(id -> kbId.equals(id))
            .count();
    }
}

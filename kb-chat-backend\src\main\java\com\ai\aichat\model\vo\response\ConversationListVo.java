package com.ai.aichat.model.vo.response;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 会话列表响应VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会话列表响应")
public class ConversationListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会话列表
     */
    @Schema(description = "会话列表")
    private List<ConversationVo> conversations;

    /**
     * 分页信息
     */
    @Schema(description = "分页信息")
    private PaginationVo pagination;

    /**
     * 分页信息VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分页信息")
    public static class PaginationVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 当前页码
         */
        @Schema(description = "当前页码", example = "1")
        private Integer page;

        /**
         * 每页数量
         */
        @Schema(description = "每页数量", example = "20")
        private Integer page_size;

        /**
         * 总记录数
         */
        @Schema(description = "总记录数", example = "100")
        private Long total_records;

        /**
         * 总会话数（前端兼容字段）
         */
        @Schema(description = "总会话数", example = "100")
        private Long total_conversations;

        /**
         * 总页数
         */
        @Schema(description = "总页数", example = "5")
        private Integer total_pages;

        /**
         * 是否有更多数据
         */
        @Schema(description = "是否有更多数据", example = "true")
        private Boolean has_more;
    }
}

package com.ai.aichat.model.dto.request;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新会话标题请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新会话标题请求")
public class UpdateConversationTitleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", example = "chat_123456", required = true)
    private String session_id;

    /**
     * 新标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 255, message = "标题长度不能超过255个字符")
    @Schema(description = "新标题", example = "关于AI的讨论", required = true)
    private String title;
}
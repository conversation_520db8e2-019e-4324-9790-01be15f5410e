package com.ai.aichat.service.impl;

import com.ai.aichat.mapper.ModelStructureMapper;
import com.ai.aichat.model.entity.ModelStructure;
import com.ai.aichat.model.vo.response.ModelStructureVo;
import com.ai.aichat.service.ModelStructureService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模型结构服务实现类
 */
@Service
public class ModelStructureServiceImpl extends ServiceImpl<ModelStructureMapper, ModelStructure>
        implements ModelStructureService {

    private static final Logger log = LoggerFactory.getLogger(ModelStructureServiceImpl.class);

    @Value("${app.model.upload-path:data/models}")
    private String modelUploadPath;

    @Value("${app.model.export-path:data/exports}")
    private String modelExportPath;

    @Override
    public List<ModelStructureVo> getAllModelStructures() {
        try {
            LambdaQueryWrapper<ModelStructure> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(ModelStructure::getCreateTime);
            
            List<ModelStructure> modelStructures = this.list(queryWrapper);
            
            return modelStructures.stream().map(this::convertToVo).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取模型结构列表失败", e);
            throw new RuntimeException("获取模型结构列表失败", e);
        }
    }

    @Override
    public ModelStructureVo getModelStructureById(Long id) {
        try {
            ModelStructure modelStructure = this.getById(id);
            if (modelStructure == null) {
                throw new RuntimeException("模型不存在");
            }
            return convertToVo(modelStructure);
        } catch (Exception e) {
            log.error("获取模型结构失败，ID: {}", id, e);
            throw new RuntimeException("获取模型结构失败", e);
        }
    }

    @Override
    public Boolean updateTrainingStatus(Long modelId, Integer status) {
        try {
            LambdaUpdateWrapper<ModelStructure> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ModelStructure::getId, modelId)
                        .set(ModelStructure::getTrainingStatus, status);

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("更新模型训练状态失败，模型ID: {}, 状态: {}", modelId, status, e);
            throw new RuntimeException("更新模型训练状态失败", e);
        }
    }

    @Override
    public Boolean createModelStructure(String version, String baseModel, String dataset, String description, String modelPath) {
        try {
            // 检查版本是否已存在
            LambdaQueryWrapper<ModelStructure> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ModelStructure::getVersion, version);
            if (this.count(queryWrapper) > 0) {
                throw new RuntimeException("模型版本已存在");
            }

            ModelStructure modelStructure = new ModelStructure();
            modelStructure.setVersion(version);
            modelStructure.setBaseModel(baseModel);
            modelStructure.setDataset(dataset);
            modelStructure.setDescription(description);
            modelStructure.setModelPath(modelPath);
            modelStructure.setTrainingStatus(0); // 默认未训练

            // 手动设置时间字段
            Date now = new Date();
            modelStructure.setCreateTime(now);
            modelStructure.setUpdateTime(now);
            modelStructure.setIsDeleted(0); // 设置未删除

            return this.save(modelStructure);
        } catch (Exception e) {
            log.error("创建模型结构失败，版本: {}", version, e);
            throw new RuntimeException("创建模型结构失败", e);
        }
    }

    @Override
    public Boolean updateModelStructure(Long id, String version, String baseModel, String dataset, String description, String modelPath) {
        try {
            ModelStructure modelStructure = this.getById(id);
            if (modelStructure == null) {
                throw new RuntimeException("模型不存在");
            }

            // 如果版本发生变化，检查新版本是否已存在
            if (version != null && !version.equals(modelStructure.getVersion())) {
                LambdaQueryWrapper<ModelStructure> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ModelStructure::getVersion, version)
                           .ne(ModelStructure::getId, id);
                if (this.count(queryWrapper) > 0) {
                    throw new RuntimeException("模型版本已存在");
                }
            }

            LambdaUpdateWrapper<ModelStructure> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ModelStructure::getId, id)
                        .set(version != null, ModelStructure::getVersion, version)
                        .set(baseModel != null, ModelStructure::getBaseModel, baseModel)
                        .set(dataset != null, ModelStructure::getDataset, dataset)
                        .set(description != null, ModelStructure::getDescription, description)
                        .set(modelPath != null, ModelStructure::getModelPath, modelPath);

            return this.update(updateWrapper);
        } catch (Exception e) {
            log.error("更新模型结构失败，ID: {}", id, e);
            throw new RuntimeException("更新模型结构失败", e);
        }
    }

    @Override
    public Boolean deleteModelStructure(Long id) {
        try {
            ModelStructure modelStructure = this.getById(id);
            if (modelStructure == null) {
                throw new RuntimeException("模型不存在");
            }

            // 检查是否有正在进行的训练任务
            if (modelStructure.getTrainingStatus() != null && modelStructure.getTrainingStatus() == 1) {
                throw new RuntimeException("模型正在训练中，无法删除");
            }

            return removeById(id);
        } catch (Exception e) {
            log.error("删除模型结构失败，ID: {}", id, e);
            throw new RuntimeException("删除模型结构失败", e);
        }
    }

    @Override
    public Map<String, Object> uploadModelFolder(String folderName, Map<String, MultipartFile> files) {
        try {
            log.info("上传模型文件夹，文件夹名称: {}, 文件数量: {}", folderName, files.size());

            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 生成带时间戳的文件夹名称
            long timestamp = System.currentTimeMillis();
            String folderNameWithTimestamp = folderName + "_" + timestamp;

            // 创建目标目录（使用配置的模型上传路径）
            String targetDir = projectRoot + File.separator + modelUploadPath + File.separator + folderNameWithTimestamp;
            Path targetPath = Paths.get(targetDir);
            Files.createDirectories(targetPath);

            log.info("创建目标目录: {}", targetPath.toAbsolutePath());

            // 保存文件
            for (Map.Entry<String, MultipartFile> entry : files.entrySet()) {
                String paramName = entry.getKey();
                MultipartFile file = entry.getValue();

                if (file != null && !file.isEmpty()) {
                    // 处理参数名，提取相对路径
                    String relativePath = paramName;
                    if (paramName.startsWith("files[") && paramName.endsWith("]")) {
                        relativePath = paramName.substring(6, paramName.length() - 1);
                    }

                    // 安全检查：防止路径遍历攻击
                    if (relativePath.contains("..") || relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                        log.warn("检测到不安全的文件路径: {}", relativePath);
                        continue;
                    }

                    Path filePath = targetPath.resolve(relativePath);

                    // 确保父目录存在
                    Path parentDir = filePath.getParent();
                    if (parentDir != null && !Files.exists(parentDir)) {
                        Files.createDirectories(parentDir);
                        log.debug("创建父目录: {}", parentDir);
                    }

                    // 保存文件
                    file.transferTo(filePath.toFile());
                    log.debug("保存文件: {} -> {}", file.getOriginalFilename(), filePath);
                }
            }

            // 返回相对路径给前端（使用配置的模型上传路径）
            String relativePath = modelUploadPath + "/" + folderNameWithTimestamp;

            return Map.of(
                "success", true,
                "save_dir", relativePath,
                "message", "上传成功"
            );

        } catch (Exception e) {
            log.error("上传模型文件夹失败", e);
            throw new RuntimeException("上传模型文件夹失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, String> getExportPath() {
        try {
            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 确保导出目录存在（使用配置的导出路径）
            Path exportPath = Paths.get(projectRoot, modelExportPath);
            if (!Files.exists(exportPath)) {
                Files.createDirectories(exportPath);
            }

            return Map.of("export_path", exportPath.toAbsolutePath().toString());

        } catch (Exception e) {
            log.error("获取导出路径失败", e);
            return Map.of(
                "export_path", modelExportPath,
                "error", e.getMessage()
            );
        }
    }

    @Override
    public Map<String, Object> exportModel(Long id) {
        try {
            log.info("导出模型，ID: {}", id);

            // 获取模型信息
            ModelStructureVo model = getModelStructureById(id);
            if (model == null) {
                throw new RuntimeException("模型不存在");
            }

            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 创建导出目录（使用配置的导出路径）
            String exportDir = projectRoot + File.separator + modelExportPath + File.separator + "model_" + model.getVersion() + "_" + System.currentTimeMillis();
            Path exportPath = Paths.get(exportDir);
            Files.createDirectories(exportPath);

            // 这里可以添加实际的模型文件复制逻辑
            // 目前只是创建一个信息文件
            String modelInfo = String.format(
                "模型版本: %s\n基础模型: %s\n描述: %s\n模型路径: %s\n数据集: %s\n",
                model.getVersion(),
                model.getBase_model(),
                model.getDescription(),
                model.getModel_path(),
                model.getDataset()
            );

            Files.write(exportPath.resolve("model_info.txt"), modelInfo.getBytes());

            return Map.of(
                "success", true,
                "export_path", exportDir,
                "message", "导出成功"
            );

        } catch (Exception e) {
            log.error("导出模型失败，ID: {}", id, e);
            throw new RuntimeException("导出模型失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO对象
     */
    private ModelStructureVo convertToVo(ModelStructure modelStructure) {
        ModelStructureVo vo = new ModelStructureVo();
        BeanUtils.copyProperties(modelStructure, vo);
        
        // 设置下划线命名的字段
        vo.setBase_model(modelStructure.getBaseModel());
        vo.setModel_path(modelStructure.getModelPath());
        vo.setTraining_status(modelStructure.getTrainingStatus());
        
        // 设置训练状态描述
        vo.setStatus_desc(getTrainingStatusDesc(modelStructure.getTrainingStatus()));
        
        return vo;
    }

    /**
     * 获取训练状态描述
     */
    private String getTrainingStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "未训练";
            case 1:
                return "训练中";
            case 2:
                return "训练完成";
            case 3:
                return "训练失败";
            default:
                return "未知";
        }
    }
}

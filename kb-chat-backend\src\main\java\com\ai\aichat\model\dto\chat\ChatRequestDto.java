package com.ai.aichat.model.dto.chat;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 聊天请求DTO
 */
@Data
@Schema(description = "聊天请求")
public class ChatRequestDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户输入的问题或消息
     */
    @Schema(description = "用户输入的问题或消息", required = true, example = "你好，请介绍一下自己")
    private String prompt;

    /**
     * 会话ID，用于标识对话会话
     */
    @Schema(description = "会话ID，用于标识对话会话", required = true, example = "chat_123456")
    private String session_id;

    /**
     * 是否为新对话，默认为false
     */
    @Schema(description = "是否为新对话", example = "false")
    private Boolean new_chat = false;

    /**
     * 聊天类型，如chat、game等
     */
    @Schema(description = "聊天类型", example = "chat")
    private String chat_type = "chat";

    /**
     * 最大历史对话数量
     */
    @Schema(description = "最大历史对话数量", example = "10")
    private Integer max_history = 10;

    /**
     * 是否保存对话历史
     */
    @Schema(description = "是否保存对话历史", example = "true")
    private Boolean save_history = true;
}

{"name": "ruoyi", "version": "3.8.5", "description": "物理资源库管理系统", "author": "若依", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@antv/g6": "^4.8.14", "@antv/x6": "^2.9.7", "@egjs/hammerjs": "^2.0.17", "@element-plus/icons-vue": "2.0.10", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "cesium": "^1.126.0", "docx-preview": "^0.3.4", "echarts": "5.4.0", "element-plus": "2.3.7", "file-saver": "2.0.5", "fuse.js": "6.6.2", "highlight.js": "^11.11.1", "js-audio-recorder": "1.0.7", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "keycharm": "^0.4.0", "mark.js": "^8.11.1", "marked": "^15.0.7", "nprogress": "0.2.0", "pinia": "2.0.22", "uuid": "^11.1.0", "vis-data": "^7.1.9", "vis-network": "^9.1.9", "vis-util": "^5.0.7", "vite-plugin-cesium": "^1.2.23", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}
<template>
  <div class="user-info-head">
    <img :src="avatarUrl" title="用户头像" class="img-circle img-lg" />

  </div>
</template>

<script setup>
import useUserStore from "@/store/modules/user";

const props = defineProps({
  user: {
    type: Object,
    default: () => ({})
  }
});

const userStore = useUserStore();

const avatarUrl = computed(() => {
  return props.user.userAvatar || userStore.avatar || '/src/assets/images/profile.jpg';
});


</script>

<style lang="scss" scoped>
.user-info-head {
  position: relative;
  display: inline-block;
  height: 120px;
}

.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
  object-fit: cover;
}
</style>

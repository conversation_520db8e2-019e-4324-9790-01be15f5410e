package com.ai.aichat.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 训练请求DTO
 */
@Data
@Schema(description = "训练请求参数")
public class TrainingRequestDto {

    /**
     * 模型ID
     */
    @Schema(description = "模型ID", required = true)
    private Long modelId;

    /**
     * 数据集名称
     */
    @Schema(description = "数据集名称", required = true)
    private String dataset;

    /**
     * 训练轮次
     */
    @Schema(description = "训练轮次", required = true, minimum = "1", maximum = "10000")
    private Integer epochs;

    /**
     * 学习率
     */
    @Schema(description = "学习率", required = true, minimum = "0.0001", maximum = "1.0")
    private BigDecimal learningRate;
}

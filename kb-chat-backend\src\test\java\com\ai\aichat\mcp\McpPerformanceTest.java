//package com.ai.aichat.mcp;
//
//import cn.hutool.core.lang.UUID;
//import com.ai.aichat.love.LoveApp;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Assertions;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.TimeUnit;
//
///**
// * MCP性能测试类
// * 测试MCP功能在各种负载条件下的性能表现
// */
//@Slf4j
//@SpringBootTest
//@ActiveProfiles("local")
//@DisplayName("MCP性能测试")
//class McpPerformanceTest {
//
//    @Resource
//    private LoveApp loveApp;
//
//    private final List<String> testMessages = List.of(
//            "北京有哪些适合约会的地方？",
//            "上海外滩附近的浪漫餐厅推荐？",
//            "广州塔周边约会景点？",
//            "深圳有什么好的约会咖啡厅？",
//            "杭州西湖附近适合情侣的地方？",
//            "成都春熙路约会推荐？",
//            "西安有哪些浪漫的约会地点？",
//            "南京夫子庙附近约会好去处？"
//    );
//
//    @BeforeEach
//    void setUp() {
//        log.info("开始MCP性能测试");
//    }
//
//    @Test
//    @DisplayName("测试单次调用响应时间")
//    void testSingleCallResponseTime() {
//        String message = "北京有哪些适合约会的地方？";
//        String chatId = UUID.randomUUID().toString();
//
//        long startTime = System.currentTimeMillis();
//        String response = loveApp.doChatWithMcp(message, chatId);
//        long endTime = System.currentTimeMillis();
//
//        long responseTime = endTime - startTime;
//
//        Assertions.assertNotNull(response, "响应不应为空");
//        Assertions.assertTrue(responseTime < 30000, "单次调用应在30秒内完成");
//
//        log.info("单次调用响应时间: {}ms", responseTime);
//        log.info("响应长度: {} 字符", response.length());
//    }
//
//    @Test
//    @DisplayName("测试连续调用性能")
//    void testConsecutiveCallsPerformance() {
//        List<Long> responseTimes = new ArrayList<>();
//        String chatId = UUID.randomUUID().toString();
//
//        for (int i = 0; i < 5; i++) {
//            String message = testMessages.get(i % testMessages.size());
//
//            long startTime = System.currentTimeMillis();
//            String response = loveApp.doChatWithMcp(message, chatId);
//            long endTime = System.currentTimeMillis();
//
//            long responseTime = endTime - startTime;
//            responseTimes.add(responseTime);
//
//            Assertions.assertNotNull(response, "第" + (i + 1) + "次调用响应不应为空");
//
//            log.info("第{}次调用响应时间: {}ms", i + 1, responseTime);
//
//            // 避免过于频繁的调用
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//            }
//        }
//
//        // 计算平均响应时间
//        double averageTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
//        long maxTime = responseTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
//        long minTime = responseTimes.stream().mapToLong(Long::longValue).min().orElse(0L);
//
//        log.info("连续调用性能统计:");
//        log.info("- 平均响应时间: {:.2f}ms", averageTime);
//        log.info("- 最大响应时间: {}ms", maxTime);
//        log.info("- 最小响应时间: {}ms", minTime);
//
//        Assertions.assertTrue(averageTime < 25000, "平均响应时间应在25秒内");
//    }
//
//    @Test
//    @DisplayName("测试并发调用性能")
//    void testConcurrentCallsPerformance() {
//        int concurrentUsers = 3; // 减少并发数以避免过载
//        ExecutorService executor = Executors.newFixedThreadPool(concurrentUsers);
//        List<CompletableFuture<Long>> futures = new ArrayList<>();
//
//        long testStartTime = System.currentTimeMillis();
//
//        for (int i = 0; i < concurrentUsers; i++) {
//            final int userIndex = i;
//            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
//                String chatId = UUID.randomUUID().toString();
//                String message = testMessages.get(userIndex % testMessages.size());
//
//                long startTime = System.currentTimeMillis();
//                String response = loveApp.doChatWithMcp(message, chatId);
//                long endTime = System.currentTimeMillis();
//
//                Assertions.assertNotNull(response, "用户" + userIndex + "的响应不应为空");
//
//                long responseTime = endTime - startTime;
//                log.info("用户{}响应时间: {}ms", userIndex, responseTime);
//
//                return responseTime;
//            }, executor);
//
//            futures.add(future);
//        }
//
//        try {
//            // 等待所有任务完成
//            List<Long> responseTimes = new ArrayList<>();
//            for (CompletableFuture<Long> future : futures) {
//                responseTimes.add(future.get(60, TimeUnit.SECONDS));
//            }
//
//            long testEndTime = System.currentTimeMillis();
//            long totalTestTime = testEndTime - testStartTime;
//
//            // 计算统计信息
//            double averageTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
//            long maxTime = responseTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
//
//            log.info("并发调用性能统计:");
//            log.info("- 并发用户数: {}", concurrentUsers);
//            log.info("- 总测试时间: {}ms", totalTestTime);
//            log.info("- 平均响应时间: {:.2f}ms", averageTime);
//            log.info("- 最大响应时间: {}ms", maxTime);
//
//            Assertions.assertTrue(averageTime < 40000, "并发调用平均响应时间应在40秒内");
//
//        } catch (Exception e) {
//            log.error("并发测试失败", e);
//            Assertions.fail("并发测试失败: " + e.getMessage());
//        } finally {
//            executor.shutdown();
//        }
//    }
//
//    @Test
//    @DisplayName("测试内存使用情况")
//    void testMemoryUsage() {
//        Runtime runtime = Runtime.getRuntime();
//
//        // 执行垃圾回收以获得更准确的内存测量
//        System.gc();
//        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
//
//        // 执行多次MCP调用
//        String chatId = UUID.randomUUID().toString();
//        for (int i = 0; i < 3; i++) {
//            String message = testMessages.get(i % testMessages.size());
//            String response = loveApp.doChatWithMcp(message, chatId);
//            Assertions.assertNotNull(response, "响应不应为空");
//        }
//
//        // 再次测量内存
//        System.gc();
//        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
//
//        long memoryUsed = memoryAfter - memoryBefore;
//
//        log.info("内存使用情况:");
//        log.info("- 测试前内存: {} MB", memoryBefore / 1024 / 1024);
//        log.info("- 测试后内存: {} MB", memoryAfter / 1024 / 1024);
//        log.info("- 内存增长: {} MB", memoryUsed / 1024 / 1024);
//
//        // 内存增长不应过大（这里设置为100MB的阈值）
//        Assertions.assertTrue(memoryUsed < 100 * 1024 * 1024, "内存增长应控制在合理范围内");
//    }
//
//    @Test
//    @DisplayName("测试长时间运行稳定性")
//    void testLongRunningStability() {
//        String chatId = UUID.randomUUID().toString();
//        int iterations = 5; // 减少迭代次数以适应测试环境
//        List<Long> responseTimes = new ArrayList<>();
//
//        for (int i = 0; i < iterations; i++) {
//            String message = testMessages.get(i % testMessages.size());
//
//            long startTime = System.currentTimeMillis();
//            String response = loveApp.doChatWithMcp(message, chatId);
//            long endTime = System.currentTimeMillis();
//
//            long responseTime = endTime - startTime;
//            responseTimes.add(responseTime);
//
//            Assertions.assertNotNull(response, "第" + (i + 1) + "次迭代响应不应为空");
//
//            log.info("迭代{}: 响应时间{}ms", i + 1, responseTime);
//
//            // 间隔一段时间再进行下次调用
//            try {
//                Thread.sleep(2000);
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                break;
//            }
//        }
//
//        // 检查响应时间的稳定性
//        double averageTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
//        double variance = responseTimes.stream()
//                .mapToDouble(time -> Math.pow(time - averageTime, 2))
//                .average().orElse(0.0);
//        double standardDeviation = Math.sqrt(variance);
//
//        log.info("长时间运行稳定性统计:");
//        log.info("- 迭代次数: {}", iterations);
//        log.info("- 平均响应时间: {:.2f}ms", averageTime);
//        log.info("- 标准差: {:.2f}ms", standardDeviation);
//
//        // 标准差不应过大，表示性能稳定
//        Assertions.assertTrue(standardDeviation < averageTime * 0.5, "响应时间应保持相对稳定");
//    }
//
//    @Test
//    @DisplayName("测试错误恢复性能")
//    void testErrorRecoveryPerformance() {
//        String chatId = UUID.randomUUID().toString();
//
//        // 先发送一个可能导致错误的请求
//        String errorMessage = "火星上有什么约会的地方？";
//        long startTime = System.currentTimeMillis();
//        String errorResponse = loveApp.doChatWithMcp(errorMessage, chatId);
//        long errorTime = System.currentTimeMillis() - startTime;
//
//        Assertions.assertNotNull(errorResponse, "错误情况下也应有响应");
//        log.info("错误处理响应时间: {}ms", errorTime);
//
//        // 然后发送正常请求，测试恢复能力
//        String normalMessage = "北京有哪些适合约会的地方？";
//        startTime = System.currentTimeMillis();
//        String normalResponse = loveApp.doChatWithMcp(normalMessage, chatId);
//        long normalTime = System.currentTimeMillis() - startTime;
//
//        Assertions.assertNotNull(normalResponse, "恢复后的响应不应为空");
//        log.info("恢复后响应时间: {}ms", normalTime);
//
//        // 恢复后的性能不应显著下降
//        Assertions.assertTrue(normalTime < 35000, "错误恢复后性能应正常");
//    }
//}

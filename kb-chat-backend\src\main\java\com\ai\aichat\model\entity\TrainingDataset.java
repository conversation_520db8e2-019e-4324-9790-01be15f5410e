package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

/**
 * 训练数据集实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("training_dataset")
@Schema(description = "训练数据集")
public class TrainingDataset implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "数据集ID")
    private Long id;

    /**
     * 数据集名称
     */
    @TableField("name")
    @Schema(description = "数据集名称")
    private String name;

    /**
     * 数据集描述
     */
    @TableField("description")
    @Schema(description = "数据集描述")
    private String description;

    /**
     * 数据集文件路径
     */
    @TableField("file_path")
    @Schema(description = "数据集文件路径")
    private String filePath;

    /**
     * 数据集大小（字节）
     */
    @TableField("file_size")
    @Schema(description = "数据集大小")
    private Long fileSize;

    /**
     * 数据条数
     */
    @TableField("data_count")
    @Schema(description = "数据条数")
    private Integer dataCount;

    /**
     * 数据集状态：0-未处理，1-处理中，2-处理完成，3-处理失败
     */
    @TableField("status")
    @Schema(description = "数据集状态")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    @Schema(description = "是否删除")
    private Integer isDeleted;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.aichat.mapper.ChatMessageMapper">

    <resultMap id="BaseResultMap" type="com.ai.aichat.model.entity.ChatMessage">
            <id property="id" column="id" />
            <result property="conversationId" column="conversation_id" />
            <result property="role" column="role" />
            <result property="content" column="content" />
            <result property="sources" column="sources" />
            <result property="graphData" column="graph_data" />
            <result property="qaPairData" column="qa_pair_data" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,conversation_id,role,content,sources,graph_data,
        create_time
    </sql>
</mapper>

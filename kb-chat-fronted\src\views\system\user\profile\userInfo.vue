<template>
   <el-form ref="userRef" :model="user" :rules="rules" label-width="80px">
      <el-form-item label="用户名称" prop="userName">
         <el-input v-model="user.userName" maxlength="30" />
      </el-form-item>
      <el-form-item label="登录账号" prop="userAccount">
         <el-input v-model="user.userAccount" maxlength="50" disabled />
      </el-form-item>
      <el-form-item label="用户简介" prop="userProfile">
         <el-input
           v-model="user.userProfile"
           type="textarea"
           :rows="4"
           maxlength="500"
           placeholder="请输入用户简介" />
      </el-form-item>
      <el-form-item>
      <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      <el-button type="danger" @click="close">关闭</el-button>
      </el-form-item>
   </el-form>
</template>

<script setup>
import { updateUserProfile } from "@/api/system/user";

const props = defineProps({
  user: {
    type: Object
  }
});

const { proxy } = getCurrentInstance();
const loading = ref(false);

const rules = ref({
  userName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
  userAccount: [{ required: true, message: "登录账号不能为空", trigger: "blur" }],
  userProfile: [{ max: 500, message: "用户简介不能超过500个字符", trigger: "blur" }],
});

/** 提交按钮 */
function submit() {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      loading.value = true;
      updateUserProfile(props.user).then(response => {
        if (response.code === 0) {
          proxy.$modal.msgSuccess("修改成功");
          // 刷新父组件的用户信息
          proxy.$parent.getUser();
        } else {
          proxy.$modal.msgError(response.message || "修改失败");
        }
      }).catch(() => {
        proxy.$modal.msgError("修改失败");
      }).finally(() => {
        loading.value = false;
      });
    }
  });
};
/** 关闭按钮 */
function close() {
  proxy.$tab.closePage();
};
</script>

<template>
   <div class="user-info-form">
      <el-form ref="userRef" :model="user" :rules="rules" label-width="100px" label-position="top">
         <el-row :gutter="20">
            <el-col :span="12">
               <el-form-item label="用户名称" prop="userName">
                  <el-input
                     v-model="user.userName"
                     maxlength="30"
                     placeholder="请输入用户名称"
                     prefix-icon="User"
                     show-word-limit />
               </el-form-item>
            </el-col>
            <el-col :span="12">
               <el-form-item label="登录账号" prop="userAccount">
                  <el-input
                     v-model="user.userAccount"
                     maxlength="50"
                     disabled
                     prefix-icon="Key">
                     <template #suffix>
                        <el-tooltip content="登录账号不可修改" placement="top">
                           <el-icon class="disabled-icon"><Lock /></el-icon>
                        </el-tooltip>
                     </template>
                  </el-input>
               </el-form-item>
            </el-col>
         </el-row>

         <el-form-item label="用户简介" prop="userProfile">
            <el-input
              v-model="user.userProfile"
              type="textarea"
              :rows="4"
              maxlength="500"
              placeholder="请输入用户简介，让大家更好地了解你..."
              show-word-limit
              resize="none" />
         </el-form-item>

         <el-form-item class="form-actions">
            <el-button type="primary" @click="submit" :loading="loading" size="large">
               <el-icon><Check /></el-icon>
               保存修改
            </el-button>
            <el-button @click="resetForm" size="large">
               <el-icon><Refresh /></el-icon>
               重置
            </el-button>
         </el-form-item>
      </el-form>
   </div>
</template>

<script setup>
import { updateUserProfile } from "@/api/system/user";
import { ElMessage } from "element-plus";
import { User, Key, Lock, Check, Refresh } from "@element-plus/icons-vue";

const props = defineProps({
  user: {
    type: Object
  }
});

const emit = defineEmits(['refresh']);

const { proxy } = getCurrentInstance();
const loading = ref(false);

const rules = ref({
  userName: [
    { required: true, message: "用户名称不能为空", trigger: "blur" },
    { min: 2, max: 30, message: "用户名称长度在 2 到 30 个字符", trigger: "blur" }
  ],
  userAccount: [{ required: true, message: "登录账号不能为空", trigger: "blur" }],
  userProfile: [{ max: 500, message: "用户简介不能超过500个字符", trigger: "blur" }],
});

/** 提交按钮 */
function submit() {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      loading.value = true;
      updateUserProfile(props.user).then(response => {
        if (response.code === 0) {
          ElMessage.success("修改成功");
          // 通知父组件刷新用户信息
          emit('refresh');
        } else {
          ElMessage.error(response.message || "修改失败");
        }
      }).catch(error => {
        console.error('更新用户信息失败:', error);
        ElMessage.error("修改失败，请稍后重试");
      }).finally(() => {
        loading.value = false;
      });
    }
  });
};

/** 重置表单 */
function resetForm() {
  proxy.$refs.userRef.resetFields();
  emit('refresh');
};
</script>

<style lang="scss" scoped>
.user-info-form {
  padding: 20px;

  :deep(.el-form-item__label) {
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }

  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }

    &.is-disabled .el-input__wrapper {
      background-color: #f5f7fa;
      box-shadow: 0 0 0 1px #e4e7ed inset;
    }
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      border-radius: 8px;
      border: 1px solid #dcdfe6;
      transition: all 0.3s;

      &:hover {
        border-color: #c0c4cc;
      }

      &:focus {
        border-color: #409eff;
      }
    }
  }

  .disabled-icon {
    color: #c0c4cc;
    cursor: not-allowed;
  }

  .form-actions {
    margin-top: 30px;
    text-align: center;

    .el-button {
      min-width: 120px;
      border-radius: 20px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #66b1ff 0%, #5dade2 100%);
        }
      }

      + .el-button {
        margin-left: 15px;
      }
    }
  }
}

@media (max-width: 768px) {
  .user-info-form {
    padding: 15px;

    :deep(.el-col) {
      margin-bottom: 10px;
    }
  }
}
</style>

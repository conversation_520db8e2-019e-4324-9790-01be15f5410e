//package com.ai.aichat.mcp;
//
//import com.ai.aichat.love.LoveApp;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Assertions;
//import org.springframework.ai.tool.ToolCallbackProvider;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.util.Arrays;
//
///**
// * MCP工具调查测试类
// * 调查MCP工具是如何被调用的
// */
//@Slf4j
////@SpringBootTest
//@ActiveProfiles("local")
//@DisplayName("MCP工具调查测试")
//class McpToolInvestigationTest {
//
//    @Resource
//    @Qualifier("localToolCallbackProvider")
//    private ToolCallbackProvider toolCallbackProvider;
//
//    @Resource
//    private LoveApp loveApp;
//
//    @BeforeEach
//    void setUp() {
//        log.info("开始MCP工具调查测试");
//    }
//
//    @Test
//    @DisplayName("详细检查工具列表")
//    void testDetailedToolList() {
//        var tools = toolCallbackProvider.getToolCallbacks();
//
//        log.info("=== 工具详细信息 ===");
//        log.info("工具总数: {}", tools.length);
//
//        for (int i = 0; i < tools.length; i++) {
//            var tool = tools[i];
//            log.info("工具 {}: 名称={}, 类型={}", i + 1, tool.getName(), tool.getClass().getSimpleName());
//            log.info("  描述: {}", tool.getDescription());
//            log.info("  类全名: {}", tool.getClass().getName());
//        }
//
//        // 检查是否有MCP相关的工具
//        boolean hasMcpTool = Arrays.stream(tools)
//                .anyMatch(tool -> tool.getClass().getName().contains("mcp") ||
//                                 tool.getClass().getName().contains("Mcp") ||
//                                 tool.getName().toLowerCase().contains("amap") ||
//                                 tool.getName().toLowerCase().contains("map"));
//
//        log.info("是否包含MCP工具: {}", hasMcpTool);
//
//        Assertions.assertTrue(tools.length > 0, "应该至少有一个工具");
//    }
//
//    @Test
//    @DisplayName("测试MCP调用并分析日志")
//    void testMcpCallWithLogAnalysis() {
//        String testMessage = "北京天安门附近有什么好的约会地点？";
//        String testChatId = "investigation-" + System.currentTimeMillis();
//
//        log.info("=== 开始MCP调用测试 ===");
//        log.info("测试消息: {}", testMessage);
//        log.info("会话ID: {}", testChatId);
//
//        try {
//            String response = loveApp.doChatWithMcp(testMessage, testChatId);
//
//            log.info("=== MCP调用结果 ===");
//            log.info("响应长度: {}", response.length());
//            log.info("响应内容: {}", response.substring(0, Math.min(200, response.length())));
//
//            Assertions.assertNotNull(response, "MCP响应不应为空");
//            Assertions.assertFalse(response.trim().isEmpty(), "MCP响应内容不应为空");
//
//            // 检查响应是否包含地点相关信息
//            boolean containsLocationInfo = response.toLowerCase().contains("北京") ||
//                                         response.toLowerCase().contains("天安门") ||
//                                         response.toLowerCase().contains("约会") ||
//                                         response.toLowerCase().contains("地点");
//
//            log.info("响应是否包含地点信息: {}", containsLocationInfo);
//
//        } catch (Exception e) {
//            log.error("MCP调用失败", e);
//            Assertions.fail("MCP调用失败: " + e.getMessage());
//        }
//    }
//
//    @Test
//    @DisplayName("测试不同类型的地点查询")
//    void testDifferentLocationQueries() {
//        String[] testQueries = {
//            "上海外滩附近的浪漫餐厅",
//            "广州塔周边约会地点",
//            "深圳有什么好玩的地方适合情侣",
//            "杭州西湖附近的咖啡厅"
//        };
//
//        for (String query : testQueries) {
//            log.info("=== 测试查询: {} ===", query);
//
//            try {
//                String response = loveApp.doChatWithMcp(query, "test-" + System.currentTimeMillis());
//
//                log.info("查询: {}", query);
//                log.info("响应长度: {}", response.length());
//                log.info("响应摘要: {}", response.substring(0, Math.min(100, response.length())));
//
//                Assertions.assertNotNull(response, "响应不应为空");
//
//            } catch (Exception e) {
//                log.error("查询失败: {}", query, e);
//            }
//        }
//    }
//
//    @Test
//    @DisplayName("检查Spring上下文中的MCP相关Bean")
//    void testMcpBeansInContext() {
//        // 这个测试用于检查Spring上下文中是否有MCP相关的Bean
//        log.info("=== 检查MCP相关Bean ===");
//
//        // 检查ToolCallbackProvider
//        log.info("ToolCallbackProvider类型: {}", toolCallbackProvider.getClass().getName());
//
//        // 检查LoveApp
//        log.info("LoveApp类型: {}", loveApp.getClass().getName());
//
//        // 这里我们主要是通过日志来观察，实际的MCP工具可能是通过其他方式集成的
//        Assertions.assertNotNull(toolCallbackProvider, "ToolCallbackProvider应该存在");
//        Assertions.assertNotNull(loveApp, "LoveApp应该存在");
//    }
//}

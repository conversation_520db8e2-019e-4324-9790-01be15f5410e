package com.ai.aichat.model.vo.chat;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 简化的聊天响应数据VO - 仅包含基本字段
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "简化的聊天响应数据")
public class SimpleChatResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应内容
     */
    @Schema(description = "响应内容", example = "你好！我是AI助手，很高兴为您服务。")
    private String content;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "chat_123456")
    private String session_id;
}

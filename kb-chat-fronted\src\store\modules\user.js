import { defineStore } from 'pinia'
import {
  login,
  logout,
  getInfo
} from '@/api/login'
import {
  getToken,
  setToken,
  removeToken
} from '@/utils/auth'
import defAva from '@/assets/images/profile.jpg'

const useUserStore = defineStore(
  'user', {
    state: () => ({
      token: getToken(),
      name: '',
      avatar: '',
      roles: [],
      permissions: []
    }),
    actions: {
      // 登录
      login(userInfo) {
        const userAccount = userInfo.username.trim()
        const userPassword = userInfo.password
        return new Promise((resolve, reject) => {
          login(userAccount, userPassword).then(res => {
            // 后端返回的是 BaseResponse<LoginUserVO> 格式
            if (res.code === 0 && res.data) {
              // 使用用户ID作为token，或者可以生成一个简单的token
              const token = 'user_' + res.data.id + '_' + Date.now()
              setToken(token)
              this.token = token
              // 保存用户信息
              this.name = res.data.userName
              this.roles = res.data.userRole === 'admin' ? ['admin'] : ['user']
              resolve()
            } else {
              reject(new Error(res.message || '登录失败'))
            }
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            // 后端返回的是 BaseResponse<LoginUserVO> 格式
            if (res.code === 0 && res.data) {
              const user = res.data
              const avatar = (user.userAvatar == "" || user.userAvatar == null) ? defAva : user.userAvatar;
              // 根据用户角色设置权限
              if (user.userRole === 'admin') {
                this.roles = ['admin']
                this.permissions = ['*:*:*']
              } else {
                this.roles = ['user']
                this.permissions = ['user:*:*']
              }
              this.name = user.userName
              this.avatar = avatar;
              resolve(res)
            } else {
              reject(new Error(res.message || '获取用户信息失败'))
            }
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            removeToken()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      }
    }
  })

export default useUserStore
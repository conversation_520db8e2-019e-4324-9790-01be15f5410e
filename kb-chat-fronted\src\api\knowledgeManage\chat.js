import request from "/src/utils/request";

// 知识库问答（流式响应）
export const kbChatStream = async (data) => {
  try {
    const { signal, ...requestData } = data;
    const response = await fetch('/dev-api/kb_chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify({
        ...requestData,
        new_chat: requestData.new_chat || false,
        session_id: requestData.session_id
      }),
      signal: signal // 添加abort signal支持
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error('Stream request error:', error);
    throw error;
  }
};

// 通用对话（流式响应）
export const chatStream = async (data) => {
  const { signal, ...requestData } = data;
  const response = await fetch('/dev-api/chat/sse', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    },
    body: JSON.stringify({
      ...requestData,
      new_chat: requestData.new_chat || false,
      session_id: requestData.session_id
    }),
    signal: signal // 添加abort signal支持
  });
  return response;
};

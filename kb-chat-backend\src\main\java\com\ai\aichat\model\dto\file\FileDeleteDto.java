package com.ai.aichat.model.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "删除文件请求")
public class FileDeleteDto implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    private Long kbId;

    /**
     * 知识库名称
     */
    private String kbName;

    /**
     * 文件名称
     */
    private String fileName;
}
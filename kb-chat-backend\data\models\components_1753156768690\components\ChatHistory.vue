<template>
  <div class="history-list-container">
    <div class="history-header">
      <span class="history-title">
        <el-icon><ChatDotRound /></el-icon>
        历史记录
      </span>
      <div class="history-filter">
        <el-select v-model="historyFilterType" placeholder="类型" size="small" @change="loadChatHistory(1)" class="history-type-select">
          <el-option label="全部会话" value="all" />
          <el-option label="普通聊天" value="normal" />
          <el-option label="知识库聊天" value="kb" />
        </el-select>
      </div>
    </div>
    
    <!-- 调试信息在生产环境中隐藏 -->
    
    <div class="history-list" ref="historyListContainer" @scroll="handleHistoryScroll">
      <div v-if="loadingHistory && !chatHistoryList.length" class="history-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <template v-else-if="chatHistoryList.length > 0">
        <template v-for="(group, groupIndex) in groupedHistoryList" :key="`group-${groupIndex}-${group.dateValue}`">
          <!-- 时间分组标题 -->
          <div class="history-date-group" v-if="group.conversations.length > 0">
            <div class="date-divider">
              <span class="date-label">{{ group.label }}</span>
            </div>
            
            <!-- 该分组下的会话列表 -->
            <div 
              v-for="(item, itemIndex) in group.conversations" 
              :key="`item-${groupIndex}-${itemIndex}`" 
              class="history-item"
              :class="{ 
                'active': item.session_id === currentSessionId,
                'loading': item.session_id === loadingSessionId,
                'editing': editingSessionId === item.session_id
              }"
              @click="editingSessionId !== item.session_id && loadSessionHistory(item.session_id)">
              <div class="history-item-content">
                <!-- 编辑模式 -->
                <div class="history-item-title edit-mode" v-if="editingSessionId === item.session_id">
                  <el-input 
                    v-model="editingTitle" 
                    size="small" 
                    placeholder="请输入会话标题"
                    @keyup.enter="saveInlineEdit(item)"
                    @blur="saveInlineEdit(item)"
                    ref="titleInput"
                    maxlength="30"
                  >
                  </el-input>
                </div>
                
                <!-- 显示模式 -->
                <div class="history-item-title" v-else :title="item.title">
                  <el-icon v-if="item.session_id === loadingSessionId" class="is-loading"><Loading /></el-icon>
                  <el-icon v-else-if="item.type === 'kb'"><Connection /></el-icon>
                  <el-icon v-else><ChatDotRound /></el-icon>
                  {{ formatTitle(item.title) }}
                </div>
                <div class="history-item-info" v-if="editingSessionId !== item.session_id">
                  <span class="message-count">{{ item.message_count }}条消息</span>
                  <span class="history-type" :class="item.type">{{ item.type === 'kb' ? '知识库' : '普通对话' }}</span>
                </div>
              </div>
              <div class="history-item-actions" v-if="editingSessionId !== item.session_id">
                <el-dropdown @command="handleCommand($event, item)" trigger="click" placement="bottom-end">
                  <el-icon class="menu-icon" @click.stop><MoreFilled /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="rename">
                        <el-icon><Edit /></el-icon> 重命名
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" divided>
                        <el-icon><Delete /></el-icon> 删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </template>
        
        <!-- 底部加载状态指示器 -->
        <div v-if="isLoadingMore" class="loading-more">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>正在加载更多...</span>
        </div>
        <div v-else-if="hasMoreData" class="load-more-hint">
          <span>滑动查看更多历史记录</span>
        </div>
        <div v-else-if="chatHistoryList.length > 10" class="no-more-data">
          -- 没有更多记录了 --
        </div>
      </template>
      <div v-else class="no-history">
        <el-icon><ChatDotRound /></el-icon>
        <span>暂无历史记录</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits, defineProps, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElInput } from 'element-plus';
import { ChatDotRound, Connection, ArrowLeft, ArrowRight, Close, Loading, Edit, Delete, MoreFilled, Check } from '@element-plus/icons-vue';
import { getConversations, getChatHistory, deleteConversation, updateConversationTitle } from '@/api/knowledgeManage/chatHistory';

const props = defineProps({
  currentSessionId: String
});

const emit = defineEmits(['session-loaded', 'session-created', 'session-deleted', 'loading-history-start']);

const historyFilterType = ref('all');
const chatHistoryList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalPages = ref(1);
const totalConversations = ref(0);
const loadingHistory = ref(false);
const historyListContainer = ref(null);
const hasMoreData = ref(false);
const isLoadingMore = ref(false);
// 调试模式默认关闭
const debugMode = ref(false);

// 添加重命名的内联编辑状态和相关变量
const editingSessionId = ref(null);  // 正在编辑的会话ID
const editingTitle = ref('');        // 编辑中的标题

// 1. 添加一个新的变量来跟踪正在加载的会话ID
const loadingSessionId = ref(null);

// 处理长标题
const formatTitle = (title) => {
  const maxLength = 10; // 最大显示长度
  if (title && title.length > maxLength) {
    return title.substring(0, maxLength) + '...';
  }
  return title;
};

// 精确比较两个时间戳的函数
const compareTimestamps = (a, b) => {
  // 尝试解析带毫秒的时间戳格式
  const parseTimestamp = (timestamp) => {
    if (!timestamp) return new Date(0);
    try {
      return new Date(timestamp.replace(' ', 'T'));
    } catch (e) {
      console.error('时间戳解析错误:', timestamp, e);
      return new Date(0);
    }
  };
  
  const dateA = parseTimestamp(a);
  const dateB = parseTimestamp(b);
  return dateB - dateA; // 降序排列
};

// 格式化日期（YYYY-MM-DD -> 更友好的格式）
const formatDate = (dateStr) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    const dateParts = dateStr.split('-');
    const dateObj = new Date(parseInt(dateParts[0]), parseInt(dateParts[1]) - 1, parseInt(dateParts[2]));
    
    if (dateObj.getTime() === today.getTime()) {
      return '今天';
    } else if (dateObj.getTime() === yesterday.getTime()) {
      return '昨天';
    } else if (today.getTime() - dateObj.getTime() < 7 * 24 * 60 * 60 * 1000) {
      return '最近一周';
    } else {
      return `${dateObj.getFullYear()}年${dateObj.getMonth() + 1}月${dateObj.getDate()}日`;
    }
  } catch (e) {
    console.error('日期格式化错误:', e);
    return dateStr;
  }
};

// 改用计算属性从chatHistoryList中分组，而不是处理uniqueKey
const groupedHistoryList = computed(() => {
  // 先按照时间排序
  const sortedHistory = [...chatHistoryList.value].sort((a, b) => {
    const timestampA = a.last_message?.created_at || a.created_at;
    const timestampB = b.last_message?.created_at || b.created_at;
    return compareTimestamps(timestampA, timestampB);
  });
  
  // 按日期分组
  const groups = {};
  const processedIds = new Set(); // 用于确保每个会话只出现一次
  
  for (const item of sortedHistory) {
    // 如果会话ID已经处理过，则跳过
    if (processedIds.has(item.session_id)) {
      continue;
    }
    
    processedIds.add(item.session_id);
    
    // 获取日期部分
    const timestamp = item.last_message?.created_at || item.created_at;
    if (!timestamp) continue;
    
    const datePart = timestamp.split(' ')[0];
    
    if (!groups[datePart]) {
      groups[datePart] = {
        label: formatDate(datePart),
        conversations: []
      };
    }
    
    // 添加到对应的日期组
    groups[datePart].conversations.push(item);
  }
  
  // 转换为数组并按日期排序
  return Object.entries(groups)
    .map(([date, group]) => ({
      ...group,
      // 保存日期作为排序依据
      dateValue: date
    }))
    .sort((a, b) => {
      // 获取每个组中第一个会话的时间
      if (a.conversations.length === 0 || b.conversations.length === 0) {
        return 0;
      }
      
      const dateA = a.conversations[0].last_message?.created_at || a.conversations[0].created_at;
      const dateB = b.conversations[0].last_message?.created_at || b.conversations[0].created_at;
      return compareTimestamps(dateA, dateB);
    });
});

// 监听滚动事件
const handleHistoryScroll = (event) => {
  if (isLoadingMore.value || !hasMoreData.value) return;
  
  const container = event.target;
  // 当滚动到底部时加载更多历史记录
  // 检测是否已滚动到底部（距离底部20px以内）
  const isBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 20;
  
  if (isBottom) {
    console.log('[ChatHistory] 滚动到底部，加载更多记录');
    loadMoreHistory();
  }
};

// 修改loadChatHistory函数，简化唯一性处理
const loadChatHistory = async (page) => {
  if (loadingHistory.value && page === currentPage.value) {
    console.log('已经正在加载历史记录，忽略重复请求');
    return;
  }
  
  // 如果是加载第一页，清空现有数据
  if (page === 1) {
    chatHistoryList.value = [];
  }
  
  try {
    loadingHistory.value = true;
    
    const params = {
      page: page,
      page_size: pageSize.value,
      // 添加时间戳防止缓存
      _t: Date.now()
    };
    
    // 根据过滤类型设置查询参数
    if (historyFilterType.value === 'normal') {
      params.chat_type = 'chat';
    } else if (historyFilterType.value === 'kb') {
      params.chat_type = 'kb_chat';
    }
    
    const response = await getConversations(params);
    
    if (response.code === 1) {
      const { conversations, pagination } = response.data;
      
      // 更新分页信息
      currentPage.value = pagination.page;
      totalPages.value = pagination.total_pages;
      totalConversations.value = pagination.total_conversations;
      hasMoreData.value = pagination.page < pagination.total_pages;
      
      // 过滤会话类型
      let filteredConversations = [...conversations];
      if (historyFilterType.value === 'kb') {
        filteredConversations = filteredConversations.filter(item => 
          getChatType(item.chat_type) === 'kb'
        );
      } else if (historyFilterType.value === 'normal') {
        filteredConversations = filteredConversations.filter(item => 
          getChatType(item.chat_type) === 'normal'
        );
      }
      
      // 转换会话数据
      const processedConversations = filteredConversations.map(item => ({
        ...item,
        type: getChatType(item.chat_type)
      }));
      
      // 更新列表，确保不存在重复的会话ID
      if (page === 1) {
        // 全新加载
        chatHistoryList.value = processedConversations;
      } else {
        // 追加不重复的会话
        const existingIds = new Set(chatHistoryList.value.map(item => item.session_id));
        const newItems = processedConversations.filter(
          item => !existingIds.has(item.session_id)
        );
        
        chatHistoryList.value = [...chatHistoryList.value, ...newItems];
      }
    } else {
      throw new Error(response.msg || '加载历史记录失败');
    }
  } catch (error) {
    console.error('[ChatHistory] 加载历史记录出错:', error);
    ElMessage.error('加载历史记录失败: ' + (error.message || '未知错误'));
    if (page === 1) {
      chatHistoryList.value = [];
    }
    hasMoreData.value = false;
  } finally {
    loadingHistory.value = false;
    isLoadingMore.value = false;
  }
};

// 修改loadMoreHistory函数，与loadChatHistory保持一致的数据处理方式
const loadMoreHistory = async () => {
  if (isLoadingMore.value || !hasMoreData.value) return;
  
  isLoadingMore.value = true;
  const nextPage = currentPage.value + 1;
  console.log(`[ChatHistory] 加载第${nextPage}页历史记录`);
  
  try {
    await loadChatHistory(nextPage);
  } catch (error) {
    console.error('[ChatHistory] 加载更多记录失败:', error);
  } finally {
    setTimeout(() => {
      isLoadingMore.value = false;
    }, 500);
  }
};

// 添加聊天类型判断辅助函数
const getChatType = (chatType) => {
  if (!chatType) return 'normal';
  
  // 任何kb_chat开头或者明确的kb_chat类型都视为知识库聊天
  const isKbChat = chatType && (
    chatType.startsWith('kb_chat') || 
    chatType === 'kb_chat_vector' ||
    chatType === 'kb_chat_keyword' ||
    chatType === 'kb_chat_hybrid'
  );
  
  return isKbChat ? 'kb' : 'normal';
};

// 2. 修改loadSessionHistory函数
const loadSessionHistory = async (sessionId) => {
  // 如果已经在加载，或者是当前正在加载的会话，则忽略此次点击
  if (loadingHistory.value || loadingSessionId.value === sessionId) {
    console.log('已经在加载中，忽略重复点击', sessionId);
    return;
  }
  
  try {
    // 设置当前正在加载的会话ID，而不是整个列表的加载状态
    loadingSessionId.value = sessionId;
    
    // 通知父组件正在加载历史记录
    emit('loading-history-start', sessionId);
    
    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('加载会话历史超时，请重试')), 10000);
    });
    
    // 竞争模式，任何一个Promise完成都会结束
    const response = await Promise.race([
      getChatHistory(sessionId),
      timeoutPromise
    ]);
    
    // 检查是否在加载过程中切换到其他会话
    if (loadingSessionId.value !== sessionId) {
      console.log('加载过程中已切换到其他会话，忽略此次结果');
      return;
    }
    
    if (response.code === 1 && response.data && response.data.records) {
      // 检查是否有记录
      if (response.data.records.length === 0) {
        ElMessage.warning('该会话没有历史记录');
        return;
      }
      
      // 对记录按创建时间升序排序（从早到晚）
      const sortedRecords = [...response.data.records].sort((a, b) => {
        // 检查是否存在timestamp字段或created_at字段
        const timeStrA = a.timestamp || a.created_at || '';
        const timeStrB = b.timestamp || b.created_at || '';
        
        // 如果没有时间字段，使用ID排序
        if (!timeStrA && !timeStrB) {
          return (a.id || 0) - (b.id || 0);
        }
        
        try {
          // 安全地处理时间字符串
          const timeA = timeStrA ? new Date(timeStrA.replace ? timeStrA.replace(' ', 'T') : timeStrA) : new Date(0);
          const timeB = timeStrB ? new Date(timeStrB.replace ? timeStrB.replace(' ', 'T') : timeStrB) : new Date(0);
          return timeA - timeB; // 升序排序（从早到晚）
        } catch (error) {
          console.error('时间排序错误:', error, { a, b });
          return (a.id || 0) - (b.id || 0); // 降级到ID排序
        }
      });
      
      // 提取聊天记录并确保每个记录都有正确的角色和内容
      const messages = [];
      
      // 检查新的API响应格式
      if (sortedRecords.length > 0 && 'role' in sortedRecords[0]) {
        // 新格式：每条记录已经包含角色信息
        messages.push(...sortedRecords);
      } else {
        // 旧格式：每条记录包含user_message和bot_message
        for (const record of sortedRecords) {
          if (record.user_message !== undefined) {
            // 添加用户消息
            messages.push({
              role: 'user',
              content: record.user_message,
              timestamp: record.created_at || record.timestamp
            });
            
            // 添加AI回复
            messages.push({
              role: 'assistant',
              content: record.bot_message,
              sources: record.sources,
              graph_data: record.graph_data,
              timestamp: record.created_at || record.timestamp
            });
          }
        }
      }

      // 确定聊天类型
      const chatType = response.data.records.length > 0 
        ? response.data.records[0].chat_type 
        : 'chat';
      
      // 获取知识库名称
      const kbName = response.data.records.length > 0 && response.data.records[0].kb_name
        ? response.data.records[0].kb_name 
        : "";
      
      // 任何kb_chat开头或者明确的kb_chat类型都视为知识库聊天
      const isKbChat = chatType && (
        chatType.startsWith('kb_chat') || 
        chatType === 'kb_chat_vector' ||
        chatType === 'kb_chat_keyword' ||
        chatType === 'kb_chat_hybrid'
      );
      
      // 将成功获取的会话历史数据发送给父组件
      emit('session-loaded', messages, sessionId, {
        type: isKbChat ? 'kb' : 'normal',
        kbName: kbName || '',
        chatType: chatType
      });
      
      ElMessage.success(`已加载${messages.length}条历史对话`);
    } else {
      console.error('响应格式不符合预期:', response);
      throw new Error(response.msg || '加载会话历史失败');
    }
  } catch (error) {
    // 只有当当前正在加载的会话ID仍然是这个会话时，才处理错误
    if (loadingSessionId.value === sessionId) {
      console.error('加载会话历史错误:', error);
      ElMessage.error('加载会话历史失败: ' + (error.message || '未知错误'));
      // 尝试重置状态，确保界面不会卡在加载状态
      emit('session-loaded', [], sessionId);
    }
  } finally {
    // 只有当当前正在加载的会话ID仍然是这个会话时，才重置状态
    if (loadingSessionId.value === sessionId) {
      // 重置加载中的会话ID
      loadingSessionId.value = null;
    }
  }
};

const confirmDeleteHistory = (sessionId) => {
  ElMessageBox.confirm(
    '确定要删除这个对话历史吗？此操作不可恢复。',
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      deleteHistory(sessionId);
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 修改deleteHistory函数，使用更简单的列表更新逻辑
const deleteHistory = async (sessionId) => {
  try {
    const response = await deleteConversation(sessionId);
    
    if (response.code === 1) {
      ElMessage.success('已删除历史对话');
      
      // 通知父组件会话已被删除
      emit('session-deleted', sessionId);
      
      // 直接从本地列表移除该会话
      chatHistoryList.value = chatHistoryList.value.filter(
        item => item.session_id !== sessionId
      );
      
      // 如果当前页显示的会话数量过少，尝试加载更多或者回到第一页
      if (chatHistoryList.value.length < pageSize.value / 2) {
        if (hasMoreData.value) {
          // 延迟加载下一页
          setTimeout(() => loadMoreHistory(), 300);
        } else if (currentPage.value > 1 && chatHistoryList.value.length === 0) {
          // 如果没有更多数据且当前不是第一页，回到第一页
          setTimeout(() => loadChatHistory(1), 300);
        }
      }
    } else {
      throw new Error(response.msg || '删除历史对话失败');
    }
  } catch (error) {
    console.error('[ChatHistory] 删除历史对话出错:', error);
    ElMessage.error('删除历史对话失败: ' + (error.message || '未知错误'));
  }
};

// 启动内联编辑模式
const startInlineEdit = (item) => {
  // 设置正在编辑的会话ID和标题
  editingSessionId.value = item.session_id;
  editingTitle.value = item.title || '';
  
  console.log('[ChatHistory] 开始内联编辑会话标题:', item.session_id);
  
  // 在下一个DOM更新周期后，聚焦到输入框
  nextTick(() => {
    if (document.querySelector('.history-item.editing .el-input__inner')) {
      document.querySelector('.history-item.editing .el-input__inner').focus();
    }
  });
};

// 保存内联编辑
const saveInlineEdit = async (item) => {
  if (!editingSessionId.value) {
    return;
  }
  
  // 如果标题为空，使用原标题
  if (!editingTitle.value.trim()) {
    const originalItem = chatHistoryList.value.find(i => i.session_id === editingSessionId.value);
    if (originalItem && originalItem.title) {
      editingTitle.value = originalItem.title;
      cancelInlineEdit();
      return;
    } else {
      editingTitle.value = '未命名会话';
    }
  }
  
  try {
    const response = await updateConversationTitle({
      session_id: editingSessionId.value,
      title: editingTitle.value.trim()
    });
    
    if (response.code === 1) {
      // 无需提示成功消息，保持UI流畅
      
      // 获取当前时间
      const now = new Date();
      const formattedNow = now.getFullYear() + '-' + 
                         String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                         String(now.getDate()).padStart(2, '0') + ' ' + 
                         String(now.getHours()).padStart(2, '0') + ':' + 
                         String(now.getMinutes()).padStart(2, '0') + ':' + 
                         String(now.getSeconds()).padStart(2, '0');
      
      // 更新本地列表中的标题和时间戳
      const updatedList = chatHistoryList.value.map(listItem => {
        if (listItem.session_id === editingSessionId.value) {
          return {
            ...listItem,
            title: editingTitle.value.trim(),
            // 更新时间戳，使会话显示在最近期的分组中
            updated_at: formattedNow,
            // 如果有last_message，也更新它的时间戳
            last_message: listItem.last_message ? {
              ...listItem.last_message,
              created_at: formattedNow
            } : null
          };
        }
        return listItem;
      });
      
      // 使用新列表替换原列表，确保视图更新
      chatHistoryList.value = updatedList;
      
      // 退出编辑模式
      cancelInlineEdit();
    } else {
      throw new Error(response.msg || '重命名失败');
    }
  } catch (error) {
    console.error('[ChatHistory] 重命名出错:', error);
    ElMessage.error('重命名失败: ' + (error.message || '未知错误'));
    cancelInlineEdit();
  }
};

// 取消内联编辑
const cancelInlineEdit = () => {
  editingSessionId.value = null;
  editingTitle.value = '';
};

// 处理下拉菜单命令
const handleCommand = (command, item) => {
  if (command === 'rename') {
    // 开始内联编辑
    startInlineEdit(item);
  } else if (command === 'delete') {
    confirmDeleteHistory(item.session_id);
  }
};

// 暴露给父组件的方法
defineExpose({
  loadChatHistory
});

onMounted(() => {
  // 加载历史记录
  loadChatHistory(1);
});
</script>

<style scoped>
/* 历史聊天记录样式 */
.history-list-container {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
  width: 100%;
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.history-title {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  font-weight: 500;
  color: #d0d9f0;
  white-space: nowrap;
  flex-shrink: 0;
}

.history-filter {
  flex-shrink: 1;
}

.history-type-select {
  width: 110px !important;
}

:deep(.el-select .el-input__wrapper) {
  width: 110px !important;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
  width: 100%;
  overflow-y: auto;   /* 启用垂直滚动 */
  padding-right: 5px; /* 为滚动条预留空间 */
  scrollbar-width: thin; /* Firefox滚动条样式 */
  scrollbar-color: rgba(19, 255, 243, 0.3) transparent; /* Firefox滚动条颜色 */
  padding-bottom: 20px; /* 底部添加间距 */
  box-sizing: border-box;
  margin-right: 0;
  background-color: transparent;
}

/* WebKit浏览器的滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 5px;
}

.history-list::-webkit-scrollbar-track {
  background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: rgba(19, 255, 243, 0.3);
  border-radius: 10px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(19, 255, 243, 0.5);
}

/* 响应式布局 */
@media screen and (max-height: 800px) {
  .history-list-container {
    height: calc(100vh - 180px);
  }
}

@media screen and (max-height: 600px) {
  .history-list-container {
    height: calc(100vh - 160px);
  }
}

@media screen and (max-height: 500px) {
  .history-list-container {
    height: calc(100vh - 140px);
  }
}

/* 在超宽屏幕上增加底部间距 */
@media screen and (min-width: 1920px) {
  .history-list {
    padding-bottom: 30px;
  }
}

/* 在大型显示器上增加底部间距 */
@media screen and (min-height: 1080px) {
  .history-list-container {
    height: calc(100vh - 250px);
  }
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: rgba(9, 39, 98, 0.7);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}

.history-item:hover {
  background-color: rgba(15, 45, 90, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.history-item.active {
  background-color: rgba(19, 255, 243, 0.15);
  border: 1px solid rgba(19, 255, 243, 0.4);
}

.history-item-content {
  flex: 1;
}

.history-item-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #13fff3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
}

.history-type {
  background-color: rgba(63, 99, 168, 0.5);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.history-type.kb {
  background-color: rgba(19, 255, 243, 0.15);
  color: #13fff3;
}

.history-item-actions {
  display: flex;
  align-items: center;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.history-item:hover .history-item-actions {
  opacity: 1;
}

.menu-icon {
  font-size: 18px;
  color: #8e9dbb;
  cursor: pointer;
  transition: color 0.3s, transform 0.2s;
  padding: 5px;
}

.menu-icon:hover {
  color: #13fff3;
  transform: scale(1.1);
}

:deep(.el-dropdown-menu__item) {
  color: #d0d9f0;
  display: flex;
  align-items: center;
  gap: 5px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 5px;
}

:deep(.el-dropdown-menu) {
  background-color: rgba(9, 39, 98, 0.95);
  border: 1px solid rgba(63, 99, 168, 0.7);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: rgba(19, 255, 243, 0.15);
  color: #13fff3;
}

:deep(.el-dropdown-menu__item:hover .el-icon) {
  color: #13fff3;
}

:deep(.el-dropdown-menu__item:not(:last-child)) {
  border-bottom: 1px solid rgba(63, 99, 168, 0.3);
}

/* 无限滚动加载相关样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 15px 0;
  color: #8e9dbb;
  font-size: 12px;
}

.loading-more .el-icon {
  font-size: 14px;
  color: #13fff3;
}

.load-more-hint {
  text-align: center;
  color: #5d82c9;
  padding: 12px 0;
  font-size: 12px;
  opacity: 0.8;
  background-color: rgba(9, 39, 98, 0.3);
  border-radius: 4px;
  margin-top: 10px;
  transition: all 0.3s ease;
}

.load-more-hint:hover {
  background-color: rgba(19, 255, 243, 0.1);
  color: #13fff3;
  opacity: 1;
}

.no-more-data {
  text-align: center;
  color: #8e9dbb;
  padding: 15px 0;
  font-size: 12px;
  opacity: 0.7;
}

.no-history {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 25px;
  color: #8e9dbb;
  border-radius: 8px;
  background: rgba(9, 39, 98, 0.3);
  border: 1px dashed rgba(63, 99, 168, 0.5);
}

.no-history .el-icon {
  font-size: 24px;
  opacity: 0.7;
}

.history-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  color: #8e9dbb;
}

.history-loading .el-icon {
  font-size: 18px;
  color: #13fff3;
}

/* 调试信息样式 */
.debug-info {
  color: #999;
  font-size: 12px;
  padding: 10px;
  background: #1c1c1c;
  border-radius: 4px;
  margin: 8px 0;
  white-space: pre-wrap;
  border: 1px dashed #444;
}

.debug-info pre {
  max-height: 200px;
  overflow: auto;
  background: #111;
  padding: 8px;
  border-radius: 4px;
  margin-top: 5px;
}

.edit-icon {
  margin-left: 5px;
  font-size: 14px;
  color: #8e9dbb;
  opacity: 0.6;
  transition: opacity 0.3s, color 0.3s;
  cursor: pointer;
}

.edit-icon:hover {
  opacity: 1;
  color: #13fff3;
}

/* 日期分组样式 */
.history-date-group {
  margin-bottom: 15px;
}

.date-divider {
  position: relative;
  text-align: center;
  margin: 15px 0 10px;
}

.date-label {
  position: relative;
  display: inline-block;
  background-color: rgba(9, 39, 98, 0.9);
  padding: 3px 10px;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 600;
  color: #13fff3;
  border: 1px solid rgba(19, 255, 243, 0.2);
}

.date-label::before,
.date-label::after {
  content: '';
  position: absolute;
  top: 50%;
  height: 1px;
  background-color: rgba(63, 99, 168, 0.5);
  width: 40px;
}

.date-label::before {
  right: 100%;
  margin-right: 10px;
}

.date-label::after {
  left: 100%;
  margin-left: 10px;
}

/* 添加加载中的会话项样式 */
.history-item.loading {
  background-color: rgba(19, 255, 243, 0.05);
  box-shadow: 0 0 5px rgba(19, 255, 243, 0.3);
}

.history-item.loading .history-item-title {
  color: #13fff3;
}

.history-item.loading .is-loading {
  color: #13fff3;
  margin-right: 6px;
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 添加内联编辑相关样式 */
.history-item.editing {
  background-color: rgba(19, 255, 243, 0.08);
  border: 1px solid rgba(19, 255, 243, 0.4);
  padding: 10px;
}

.history-item.editing .history-item-title {
  margin-bottom: 5px;
}

.edit-mode {
  width: 100%;
}

.edit-mode .el-input {
  width: 100%;
}

.edit-mode :deep(.el-input__wrapper) {
  background-color: rgba(9, 39, 98, 0.4);
  border-color: rgba(19, 255, 243, 0.4);
  box-shadow: 0 0 0 1px rgba(19, 255, 243, 0.3) inset;
  padding: 0 8px;
}

.edit-mode :deep(.el-input__inner) {
  color: #ffffff;
  height: 32px;
  font-size: 14px;
}

.edit-mode :deep(.el-input__count) {
  display: none; /* 隐藏字数统计，保持简洁 */
}
</style> 
import * as Cesium from 'cesium'
import cache from '@/plugins/cache'
/**
 * 将屏幕坐标转换为经纬度
 * @param {*} viewer
 * @param {*} px
 * @returns
 */
export function getLngLatFromPX(viewer, px) {
    let cartesian = viewer.scene.camera.pickEllipsoid(
        px,
        viewer.scene.globe.ellipsoid
    );
    // 3D世界坐标转弧度
    let centerCartographic =
        viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
    // 弧度转经纬度
    let longitude = Cesium.Math.toDegrees(centerCartographic.longitude);
    let latitude = Cesium.Math.toDegrees(centerCartographic.latitude);
    return [longitude, latitude];
}

/**
 * 屏幕坐标转为Cartesian3
 * @param {*}} viewer
 * @param {*} px
 * @returns
 */
export function getCartesian3FromPX(viewer, px) {
    let cartesian = viewer.scene.camera.pickEllipsoid(
        px,
        viewer.scene.globe.ellipsoid
    );
    if (!cartesian) {
        return null;
    }
    return cartesian;
    //return Cesium.Cartesian3.fromDegrees(lnglat[0], lnglat[1], height || 0);
}

/**
 *
 * @param {*} cartesian
 * @returns
 */
export function cartesian3ToLngLat(viewer, cartesian) {
    // 3D世界坐标转弧度
    let centerCartographic =
        viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
    // 弧度转经纬度
    let longitude = Cesium.Math.toDegrees(centerCartographic.longitude);
    let latitude = Cesium.Math.toDegrees(centerCartographic.latitude);
    return [longitude, latitude];
}

/**绘制文字图片 */
export async function drawTextImage(text, color, callbackFn) {
    // let startTime = new Date().getTime()
    //  let imageSrc = await cache.db.get(text + "_" + color, 'images');
    //  // 先从indexedDB中取
    //  if(imageSrc !== undefined && imageSrc !== null && imageSrc !== '') {
    //      console.info("从indexDB中获取:" , imageSrc)
    //      let image = new Image();
    //      image.src = imageSrc;
    //      let endTime = new Date().getTime()
    //      console.info('查询indexedDB耗时：{}', endTime - startTime)
    //     return image;
    // }
    // let startTime2 = new Date().getTime();
    let canvas = document.createElement("canvas");
    canvas.height = 40;
    let ctx = canvas.getContext("2d");
    ctx.font =
        "28px Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif";
    let drawText = ctx.measureText(text);
    canvas.width = drawText.width + 10;
    ctx.fillStyle = color || '#fff';
    ctx.font =
        "28px Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif";
    ctx.fillText(text, 0, 30);
    let image = new Image();
    image.src = canvas.toDataURL("image/png");
    // let endTime2 = new Date().getTime();
    // console.info('canvas绘制耗时：{}', endTime2 - startTime2)
    // 存入indexedDB
    // cache.db.set(text + "_" + color, image.src, 'images')
    // console.info("存入indexedDB:", await cache.db.get(text + color, 'images'))
    return image;
}

/**
 * 绘制情报图片
 * text: 显示名称
 * color: 颜色
 * realityState： 目标真假
 * markSrc：军标图片
 * selected： 是否选中（可选）
*/
export async function drawIntellImage(text, color, realityState, markSrc, selected) {
    let canvas = document.createElement("canvas");
    canvas.height = 52;
    //canvas.width = 130
    let ctx = canvas.getContext("2d");
    ctx.fillStyle = color || '#fff';
    ctx.font =
        "14px Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif";
    let drawText = ctx.measureText(text);
    canvas.width = drawText.width + 80;
    // ctx.fillText(text, 0, 20);
    // 画点
    ctx.beginPath()
    ctx.fillStyle = color || '#fff';
    ctx.arc(2, 50, 2, 0, 2 * Math.PI);
    ctx.fill()

    // 画折线
    ctx.beginPath()
    ctx.moveTo(2, 50)
    ctx.lineWidth = "1"
    ctx.strokeStyle = color || '#fff'
    ctx.setLineDash([4, 2])
    ctx.lineDashOffset = 10;
    ctx.lineTo(25, 25)
    ctx.lineTo(50, 25)
    ctx.stroke();

    // 画圆
    if (realityState === '0') {
        // 未知真假，两个圆，内虚外实
        // 内虚
        ctx.beginPath();
        ctx.setLineDash([4, 2])
        ctx.lineDashOffset = 10;
        ctx.arc(65, 25, 11, 0, 2 * Math.PI);
        ctx.stroke()

        // 外实
        ctx.beginPath();
        ctx.setLineDash([1, 0])
        ctx.lineDashOffset = 0;
        ctx.arc(65, 25, 15, 0, 2 * Math.PI);
        ctx.stroke()
    } else if (realityState === '1') {
        // 真目标
        // 实圆
        ctx.beginPath();
        ctx.setLineDash([1, 0])
        ctx.lineDashOffset = 0;
        ctx.arc(65, 25, 15, 0, 2 * Math.PI);
        ctx.stroke()
    } else if (realityState === '2') {
        // 假目标
        // 虚圆
        ctx.beginPath();
        ctx.setLineDash([4, 2])
        ctx.lineDashOffset = 10;
        ctx.arc(65, 25, 15, 0, 2 * Math.PI);
        ctx.stroke()
    }
    ctx.fillText(text, 80, 20);
    if (selected) {
        // 绘制选中样式圈
        ctx.beginPath();
        ctx.strokeStyle = '#00FF00'
        ctx.setLineDash([1, 0])
        ctx.lineDashOffset = 0;
        ctx.lineWidth = "2"
        ctx.arc(65, 25, 18, 0, 2 * Math.PI);
        ctx.stroke()
    }
    let markImage = new Image();
    markImage.src = markSrc;
    const loadImage = function () {
        return new Promise((resolve, reject) => {
            markImage.onload = function () {
                ctx.fillStyle = color || '#fff'
                ctx.drawImage(this, 57, 15, 20, 20)
                resolve();
            }
        })
    }
    await loadImage();
    let image = new Image();
    image.src = canvas.toDataURL("image/png");
    return image;
}

/**
 * 绘制情报选中样式圈
*/
export function drawIntellSelectedImage() {
    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFUAAAA0CAYAAAD7XXSlAAAAAXNSR0IArs4c6QAAA1pJREFUeF7tm4FVE0EQhv+tQDsQKxAqECoQKxArUCtQKxArECpAK1AqECpAKlArWN8/s5vNxVyO5G432c3MezzIg9vkvpv9Z3ZmcDCbnICbfEVbEAY1gxMYVIOagUCGJc1TDepIAh6HAI4BPAfwFMCzhRVvAfwC8EO+HG42ecf2PdXjMYAzAG8BPFkT0j2ATwAu4fDnode2DdXjA4A3gIClEZJ6IT3SyfdkXrz4IHgzf44P4S+AczhZb9DahKrb/Asg32nXAcrXQSJdyKfBwykXNMrD2ZAstAfVy1Y/B/AoeCYhdD1yLbIA1IMvgudSBt7Byeul1hZUBUoPpV2Kl62hhStZqzbzYb0Kf/e6D2w7ULtA6UkEML15CXgMXrSXcPhPUtqAqhr6PQSkfEDjI0pgKQUnixrbCtSfISgx9aEE5DcvmkopuIWbBUR53/qhatr0PgSlw8k0dOixqMbycMC06+N8ulU3VL2xu7DtuQ3HRfkhkIu/16yAssM89iA+0NqhxqBxDSc3WN68PEjmsTMtrx0qvZQnoKVRuAhhDx4QrsIJjfWEijVVIz4D1D2cgN2eeakL8LBxxEygXk9NaU25iN/32FImIBJQM1Qm3S8A9J5sirluOnh8g8NpzVBjblo+6vdnATdwOKoZqg+Z9m7cg8fs8+zGB9pkn87dxCaXT36NQZ0cKcuD5qmTY20EKs/dbNztUqCS4krNmmopVYbtFs/9lvxPBjcdU9kVlTP31szjd6iUVX5MJUEvgw+sZ+5CQWVWg6hXUxVqlABOk5xsxVO91FNZdmym9MciNb2VFaLyWUCTRWr11thOIVxq2oPHc0Z5tnYdWH9g2bGhdkqk4qVXxJy1XCbgZb6ATcYGG3/qrSxYs61BGSjZomZv6rjNFrWCjW0NvsoH1svAWxzUaHiYIsnA/NgP+/KEO43GqoZyMiXOFezB2E8CS48lUEoBgxdvflzrujugxi3PobfeCcK689T+nhE1lmDjpDShfl4FYulSKinc7rH9vaejlPN0NN3iAYFeS4uj55xXvYOTudVk3aFf9r/isLAN/S6AGjuezqB0sY42t7n9V8sCt3IcQ7d/pBh1Aip48X55aiGwBjUDaINqUDMQyLCkeapBzUAgw5LmqRmg/gOJVPI1XRg74wAAAABJRU5ErkJggg=="
    // let canvas = document.createElement("canvas");
    // canvas.height = 52;
    // canvas.width = 85;
    // let ctx = canvas.getContext("2d");
    // // 绘制选中样式圈
    // ctx.beginPath();
    // ctx.strokeStyle = '#00FF00'
    // ctx.setLineDash([1, 0])
    // ctx.lineDashOffset = 0;
    // ctx.lineWidth = "2"
    // ctx.arc(65, 25, 18, 0, 2 * Math.PI);
    // ctx.stroke()

    // let image = new Image();
    // image.src = canvas.toDataURL("image/png");
    // console.info(image.src)
    // return image;
}

/**关系线三角形 */
export function drawRelationTriangle() {
    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAG9JREFUKFNjZEAC/5fYOoO4jDGH98KEGWGM/6tsRRl+MsiC+ewMjxnDDr8GK4YrWGxrBJIA838yyDLGHj4HVwDTDRP8D1UMMgVsArIAmA+1DqSBEV03upWM6LrRHc34f4mNDwMzyxVk78LZf//oAADUUjlA0irjMAAAAABJRU5ErkJggg=="
    // let canvas = document.createElement("canvas");
    // canvas.height = 8;
    // canvas.width = 8;
    // let ctx = canvas.getContext("2d");
    // // 绘制选中样式圈
    // ctx.beginPath();
    // ctx.strokeStyle = 'rgba(255, 164, 60, 0.5)'
    // ctx.lineWidth = "0.5"
    // ctx.moveTo(0, 8)
    // ctx.lineTo(4, 0)
    // ctx.lineTo(8, 8)
    // ctx.closePath()
    // ctx.stroke();

    // let image = new Image();
    // image.src = canvas.toDataURL("image/png");
    // console.info(image.src)
    // return image;
}

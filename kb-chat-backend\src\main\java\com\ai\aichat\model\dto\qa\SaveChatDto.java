package com.ai.aichat.model.dto.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 保存问答对请求DTO
 */
@Data
@Schema(description = "保存问答对请求")
public class SaveChatDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问答对内容（JSON格式）
     */
    @NotBlank(message = "问答对内容不能为空")
    @Schema(description = "问答对内容（JSON格式）", required = true)
    private String content;

    /**
     * 问答库名称
     */
    @Schema(description = "问答库名称", example = "技术问答库")
    private String kbName;

    /**
     * 数据集名称
     */
    @Size(max = 255, message = "数据集名称长度不能超过255个字符")
    @Schema(description = "数据集名称", example = "技术问答数据集")
    private String datasetName;

    /**
     * 数据集描述
     */
    @Size(max = 1000, message = "数据集描述长度不能超过1000个字符")
    @Schema(description = "数据集描述", example = "从文件导入的问答对")
    private String description;
}

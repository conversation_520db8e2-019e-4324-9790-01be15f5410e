package com.ai.aichat.service.impl;

import com.ai.aichat.mapper.TrainingDatasetMapper;
import com.ai.aichat.model.entity.TrainingDataset;
import com.ai.aichat.model.vo.response.TrainingDatasetVo;
import com.ai.aichat.service.TrainingDatasetService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 训练数据集服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingDatasetServiceImpl extends ServiceImpl<TrainingDatasetMapper, TrainingDataset> 
        implements TrainingDatasetService {

    @Override
    public List<TrainingDatasetVo> getAllTrainingDatasets() {
        try {
            LambdaQueryWrapper<TrainingDataset> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TrainingDataset::getCreateTime);
            
            List<TrainingDataset> datasets = this.list(queryWrapper);
            
            return datasets.stream().map(this::convertToVo).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取训练数据集列表失败", e);
            throw new RuntimeException("获取训练数据集列表失败", e);
        }
    }

    @Override
    public TrainingDatasetVo getTrainingDatasetByName(String name) {
        try {
            LambdaQueryWrapper<TrainingDataset> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TrainingDataset::getName, name);
            
            TrainingDataset dataset = this.getOne(queryWrapper);
            if (dataset == null) {
                throw new RuntimeException("数据集不存在");
            }
            
            return convertToVo(dataset);
        } catch (Exception e) {
            log.error("获取训练数据集失败，名称: {}", name, e);
            throw new RuntimeException("获取训练数据集失败", e);
        }
    }

    @Override
    public Boolean createTrainingDataset(String name, String description, String filePath) {
        try {
            // 检查数据集是否已存在
            LambdaQueryWrapper<TrainingDataset> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TrainingDataset::getName, name);
            if (this.count(queryWrapper) > 0) {
                throw new RuntimeException("数据集已存在");
            }

            TrainingDataset dataset = new TrainingDataset();
            dataset.setName(name);
            dataset.setDescription(description);
            dataset.setFilePath(filePath);
            dataset.setStatus(2); // 默认为处理完成状态

            // 如果文件路径存在，计算文件大小
            if (filePath != null && !filePath.isEmpty()) {
                File file = new File(filePath);
                if (file.exists()) {
                    dataset.setFileSize(file.length());
                }
            }

            return this.save(dataset);
        } catch (Exception e) {
            log.error("创建训练数据集失败，名称: {}", name, e);
            throw new RuntimeException("创建训练数据集失败", e);
        }
    }

    @Override
    public Boolean deleteTrainingDataset(Long id) {
        try {
            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除训练数据集失败，ID: {}", id, e);
            throw new RuntimeException("删除训练数据集失败", e);
        }
    }

    /**
     * 转换为VO对象
     */
    private TrainingDatasetVo convertToVo(TrainingDataset dataset) {
        TrainingDatasetVo vo = new TrainingDatasetVo();
        BeanUtils.copyProperties(dataset, vo);
        
        // 设置状态描述
        vo.setStatusDesc(getStatusDesc(dataset.getStatus()));
        
        // 设置文件大小可读格式
        if (dataset.getFileSize() != null) {
            vo.setFileSizeReadable(formatFileSize(dataset.getFileSize()));
        }
        
        return vo;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "未处理";
            case 1:
                return "处理中";
            case 2:
                return "处理完成";
            case 3:
                return "处理失败";
            default:
                return "未知";
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long size) {
        if (size == null || size == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", fileSize, units[unitIndex]);
    }
}

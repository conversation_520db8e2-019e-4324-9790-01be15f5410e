package com.ai.aichat.model.dto.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新问答库请求DTO
 */
@Data
@Schema(description = "更新问答库请求")
public class QaBaseUpdateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 原问答库名称
     */
    @NotBlank(message = "原问答库名称不能为空")
    @Schema(description = "原问答库名称", example = "技术问答库", required = true)
    private String kbName;

    /**
     * 新问答库名称
     */
    @Size(max = 100, message = "新问答库名称长度不能超过100个字符")
    @Schema(description = "新问答库名称", example = "新技术问答库")
    private String newKbName;

    /**
     * 问答库描述
     */
    @Size(max = 1000, message = "问答库描述长度不能超过1000个字符")
    @Schema(description = "问答库描述", example = "存储技术相关的问答对")
    private String description;
}

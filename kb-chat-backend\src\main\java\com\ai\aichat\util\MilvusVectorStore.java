package com.ai.aichat.util;

import com.ai.aichat.service.MilvusService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.OllamaEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@RequiredArgsConstructor
public class MilvusVectorStore implements VectorStore {

    private final MilvusService milvusService;
    private final OllamaEmbeddingModel embeddingModel;

    @Override
    public void add(List<Document> documents) {
        if (!documents.isEmpty()) {
            for (Document document : documents) {
                // 默认用 "default" 作为 collectionName，可根据实际需求从 metadata 获取
                String collectionName = "default";
                if (document.getMetadata() != null && document.getMetadata().containsKey("collectionName")) {
                    collectionName = document.getMetadata().get("collectionName").toString();
                }
                milvusService.insert(collectionName, embeddingModel.embed(document), document.getText(), JSON.toJSONString(document.getMetadata()), null);
            }
        }
    }

    @Override
    public void delete(List<String> idList) {
        if (!idList.isEmpty()) {
            // idList转换为id数组
            String[] ids = idList.toArray(new String[0]);
            milvusService.delete("default", ids);
        }
    }

    @Override
    public void delete(Filter.Expression filterExpression) {
        milvusService.delete("default", filterExpression.toString());
    }

    /**
     * 根据id删除指定集合的文档
     * @param collectionName 集合名称
     * @param idList 文档ID列表
     */
    public void delete(String collectionName, List<String> idList){
        if (!idList.isEmpty()) {
            // idList转换为id数组
            String[] ids = idList.toArray(new String[0]);
            milvusService.delete(collectionName, ids);
        }
    }

    /**
     * 根据过滤条件删除指定集合的文档
     * @param collectionName 集合名称
     * @param fileName 过滤条件
     */
    public void delete(String collectionName, String fileName){
        milvusService.delete(collectionName, fileName);
    }

    @Override
    public List<Document> similaritySearch(@NotNull SearchRequest request) {
        String collectionName = this.getCollectionName(request);
        return milvusService.search(collectionName, request);
    }

    private String getCollectionName(SearchRequest request) {
        String collectionName = "default";
        // 尝试从 filterExpression 里提取 collectionName
        if (request.getFilterExpression() != null) {
            String filterText = request.getFilterExpression().toString();
            Matcher matcher = Pattern
                    .compile("right=Value\\[value=([^\\]]+)\\]")
                    .matcher(filterText);
            if (matcher.find()) {
                collectionName = matcher.group(1);
            }
        }
        return collectionName;
    }
}
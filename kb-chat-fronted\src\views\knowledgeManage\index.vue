<template>
  <div>
    <div class="title">知识问答助手</div>
    <main>
      <div>
        <el-menu
          class="answer-menu"
          @select="handleSelect"
          :default-active="activeIndex"
          style="
            width: 300px;
            border-right: none;
            margin-right: 10px;
            border: 1px solid #083271;
            padding: 10px;
            height: 80vh;
          ">
          <el-menu-item index="1">本地知识库加载</el-menu-item>
          <el-menu-item index="2">问答知识库构建</el-menu-item>
          <!-- <el-menu-item index="3">知识库谱系构建</el-menu-item> -->
        </el-menu>

        <div
          class="btns"
          v-if="AskPushToKnowledge"
          @click="backToAsk">
          返回至问答
        </div>
      </div>

      <div class="inner">
        <LocalLoad v-if="activeIndex === '1'"> </LocalLoad>
        <AskBuild v-if="activeIndex === '2'"></AskBuild>
        <KnowledgeBuild v-if="activeIndex === '3'"></KnowledgeBuild>
      </div>
    </main>

    <AddKnowledge></AddKnowledge>
  </div>
</template>
<script setup>
  import LocalLoad from "./components/LocalLoad.vue";
  import AddKnowledge from "./components/AddKnowledge.vue";
  import FileProcess from "./components/FileProcess.vue";
  import AskBuild from "./components/AskBuild.vue";
  import KnowledgeBuild from "./components/KnowledgeGraph.vue";
  import { ref, onMounted, watch } from "vue";
  import { useDialogStore } from "/src/store/dialog.js";
  import { storeToRefs } from "pinia";
  const { selectedkb, AskPushToKnowledge } = storeToRefs(useDialogStore());
  import { useRouter, useRoute } from "vue-router";
  const router = useRouter();
  const route = useRoute();

  // 根据URL参数设置默认激活的标签页
  const getInitialActiveIndex = () => {
    const tab = route.query.tab;
    if (tab === 'qa') return "2"; // 问答知识库构建
    if (tab === 'graph') return "3"; // 知识库谱系构建
    return "1"; // 默认本地知识库加载
  };

  const activeIndex = ref(getInitialActiveIndex());
  const handleSelect = (index) => {
    activeIndex.value = index;
  };

  // 监听路由查询参数变化，动态更新激活的标签页
  watch(() => route.query.tab, (newTab) => {
    if (newTab === 'qa') {
      activeIndex.value = "2";
    } else if (newTab === 'graph') {
      activeIndex.value = "3";
    } else if (!newTab) {
      activeIndex.value = "1";
    }
  });

  // 页面挂载时设置初始标签页
  onMounted(() => {
    activeIndex.value = getInitialActiveIndex();
  });

  //返回至问答
  const backToAsk = () => {
    router.push("/ask");
    AskPushToKnowledge.value = false;
  };
</script>
<style scoped>
  .title {
    width: 100%;
    height: 120px;
    margin: 0 auto;
    text-align: center;
    color: #fff;
    font-size: 50px;

    font-family: YouShe, serif;
    background-image: url("/assets/images/nav-bar-bg.png");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  main {
    display: flex;
    width: 100%;
    height: calc(100vh - 120px); /* 减去标题高度 */
  }

  :deep .el-menu-item:hover {
    background-color: rgb(20, 87, 175);
    color: #fff;
  }

  .inner {
    width: calc(100% - 300px);
    height: 100%;
    overflow-y: auto;
    padding: 10px;
  }
</style>

<style>
  .answer-menu .is-active {
    background-color: rgb(20, 87, 175);
    color: #fff;
  }
</style>

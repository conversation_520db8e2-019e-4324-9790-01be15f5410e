package com.ai.aichat.model.vo.chat;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 聊天响应数据VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "聊天响应数据")
public class ChatResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应内容
     */
    @Schema(description = "响应内容", example = "你好！我是AI助手，很高兴为您服务。")
    private String content;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "chat_123456")
    private String session_id;

    /**
     * 消息来源列表（知识库检索时使用）
     */
    @Schema(description = "消息来源列表")
    private List<SourceVo> sources;

    /**
     * 知识图谱数据（JSON格式字符串）
     */
    @Schema(description = "知识图谱数据")
    private String graph_data;

    /**
     * 问答对数据（JSON格式字符串）
     */
    @Schema(description = "问答对数据")
    private String qa_pair_data;

    /**
     * 是否为流式响应的最后一块数据
     */
    @Schema(description = "是否为流式响应的最后一块数据", example = "false")
    private Boolean is_final = false;

    /**
     * 消息来源VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "消息来源")
    public static class SourceVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 来源文档名称
         */
        @Schema(description = "来源文档名称", example = "document.pdf")
        private String filename;

        /**
         * 来源内容片段
         */
        @Schema(description = "来源内容片段", example = "这是相关的文档内容...")
        private String content;

        /**
         * 相似度分数
         */
        @Schema(description = "相似度分数", example = "0.85")
        private Double score;

        /**
         * 页码或段落号
         */
        @Schema(description = "页码或段落号", example = "1")
        private Integer page;
    }
}

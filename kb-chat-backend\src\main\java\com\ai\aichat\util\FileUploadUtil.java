package com.ai.aichat.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传工具类
 */
@Slf4j
public class FileUploadUtil {

    /**
     * 允许的模型文件扩展名
     */
    private static final List<String> ALLOWED_MODEL_EXTENSIONS = Arrays.asList(
        ".bin", ".safetensors", ".pt", ".pth", ".ckpt", ".json", ".txt", ".md", 
        ".py", ".yaml", ".yml", ".cfg", ".config", ".tokenizer", ".vocab"
    );

    /**
     * 检查文件是否为允许的模型文件类型
     * @param fileName 文件名
     * @return 是否允许
     */
    public static boolean isAllowedModelFile(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        return ALLOWED_MODEL_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);
    }

    /**
     * 创建目录
     * @param dirPath 目录路径
     * @return 是否创建成功
     */
    public static boolean createDirectories(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.info("创建目录成功: {}", dirPath);
            }
            return true;
        } catch (IOException e) {
            log.error("创建目录失败: {}", dirPath, e);
            return false;
        }
    }

    /**
     * 保存上传的文件
     * @param file 上传的文件
     * @param targetPath 目标路径
     * @return 是否保存成功
     */
    public static boolean saveFile(MultipartFile file, Path targetPath) {
        try {
            // 确保父目录存在
            Files.createDirectories(targetPath.getParent());
            
            // 保存文件
            file.transferTo(targetPath.toFile());
            
            log.info("文件保存成功: {}", targetPath);
            return true;
        } catch (IOException e) {
            log.error("文件保存失败: {}", targetPath, e);
            return false;
        }
    }

    /**
     * 获取文件大小的可读格式
     * @param size 文件大小（字节）
     * @return 可读格式的文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size;
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", fileSize, units[unitIndex]);
    }

    /**
     * 计算目录大小
     * @param dirPath 目录路径
     * @return 目录大小（字节）
     */
    public static long calculateDirectorySize(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path) || !Files.isDirectory(path)) {
                return 0;
            }
            
            return Files.walk(path)
                    .filter(Files::isRegularFile)
                    .mapToLong(p -> {
                        try {
                            return Files.size(p);
                        } catch (IOException e) {
                            log.warn("无法获取文件大小: {}", p, e);
                            return 0;
                        }
                    })
                    .sum();
        } catch (IOException e) {
            log.error("计算目录大小失败: {}", dirPath, e);
            return 0;
        }
    }

    /**
     * 删除目录及其所有内容
     * @param dirPath 目录路径
     * @return 是否删除成功
     */
    public static boolean deleteDirectory(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                return true;
            }
            
            Files.walk(path)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(p -> {
                        try {
                            Files.delete(p);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", p, e);
                        }
                    });
            
            log.info("删除目录成功: {}", dirPath);
            return true;
        } catch (IOException e) {
            log.error("删除目录失败: {}", dirPath, e);
            return false;
        }
    }

    /**
     * 复制目录
     * @param sourcePath 源目录路径
     * @param targetPath 目标目录路径
     * @return 是否复制成功
     */
    public static boolean copyDirectory(String sourcePath, String targetPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            if (!Files.exists(source) || !Files.isDirectory(source)) {
                log.error("源目录不存在或不是目录: {}", sourcePath);
                return false;
            }
            
            Files.walk(source)
                    .forEach(sourceFile -> {
                        try {
                            Path targetFile = target.resolve(source.relativize(sourceFile));
                            if (Files.isDirectory(sourceFile)) {
                                Files.createDirectories(targetFile);
                            } else {
                                Files.createDirectories(targetFile.getParent());
                                Files.copy(sourceFile, targetFile);
                            }
                        } catch (IOException e) {
                            log.warn("复制文件失败: {}", sourceFile, e);
                        }
                    });
            
            log.info("复制目录成功: {} -> {}", sourcePath, targetPath);
            return true;
        } catch (IOException e) {
            log.error("复制目录失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }

    /**
     * 检查路径是否安全（防止路径遍历攻击）
     * @param path 路径
     * @param basePath 基础路径
     * @return 是否安全
     */
    public static boolean isPathSafe(String path, String basePath) {
        try {
            Path normalizedPath = Paths.get(basePath).resolve(path).normalize();
            Path normalizedBasePath = Paths.get(basePath).normalize();
            
            return normalizedPath.startsWith(normalizedBasePath);
        } catch (Exception e) {
            log.warn("路径安全检查失败: {}", path, e);
            return false;
        }
    }

    /**
     * 生成唯一的文件名
     * @param originalFileName 原始文件名
     * @return 唯一的文件名
     */
    public static String generateUniqueFileName(String originalFileName) {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return "file_" + System.currentTimeMillis();
        }
        
        int lastDotIndex = originalFileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            String nameWithoutExt = originalFileName.substring(0, lastDotIndex);
            String extension = originalFileName.substring(lastDotIndex);
            return nameWithoutExt + "_" + System.currentTimeMillis() + extension;
        } else {
            return originalFileName + "_" + System.currentTimeMillis();
        }
    }

    /**
     * 验证文件名是否合法
     * @param fileName 文件名
     * @return 是否合法
     */
    public static boolean isValidFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含非法字符
        String invalidChars = "<>:\"/\\|?*";
        for (char c : invalidChars.toCharArray()) {
            if (fileName.indexOf(c) >= 0) {
                return false;
            }
        }
        
        // 检查是否为保留名称（Windows）
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                 "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                 "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
        
        String upperFileName = fileName.toUpperCase();
        for (String reserved : reservedNames) {
            if (upperFileName.equals(reserved) || upperFileName.startsWith(reserved + ".")) {
                return false;
            }
        }
        
        return true;
    }
}

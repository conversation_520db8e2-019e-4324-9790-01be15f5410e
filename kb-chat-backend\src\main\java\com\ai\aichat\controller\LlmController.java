package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Tag(name = "大模型相关", description = "大模型相关接口")
@RequiredArgsConstructor
@RestController
public class LlmController {

    @Operation(summary = "获取大模型列表")
    @GetMapping("/model/list")
    public BaseResponse<List<String>> listModels() {
        // 1. 创建一个包含假数据的列表
        List<String> fakeModelList = Arrays.asList(
                "gpt-4o",
                "gpt-4-turbo",
                "gpt-3.5-turbo",
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "gemini-1.5-pro-latest",
                "llama3-70b-8192"
        );
        return ResultUtils.success(fakeModelList);
    }
}

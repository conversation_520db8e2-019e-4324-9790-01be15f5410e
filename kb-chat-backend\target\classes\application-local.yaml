# 本地开发环境配置
# 包含敏感信息，不应提交到版本控制系统

server:
  port: 5000

spring:
  ai:
    openai:
      api-key: sk-1ecbd04fab7b46ae86cbc52b801ed058
    vectorstore:
      pgvector:
        host: rm-cn-lf64ayxxx0001myo.rwlb.rds.aliyuncs.com
        port: 5432
        database: yu_ai_agent
        username: my_user
        password: Aa123!11
        schema-name: public
        table-name: vector_store
        index-type: HNSW
        distance-type: COSINE_DISTANCE
        dimensions: 1536
        initialize-schema: false
  datasource:
    url: ***********************************
    username: root
    password: 123456

# searchApi
search-api:
  api-key: GJbmjXV7embAbBtGbkGfZ1hM

# 本地开发特定配置
logging:
  level:
    com.ai.aichat: debug
    org.springframework.ai: debug

# 应用配置
app:
  qa:
    base-dir: ./data/qa_base  # 问答对数据存储目录

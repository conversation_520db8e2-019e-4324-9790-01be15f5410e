-- AI聊天系统初始化数据脚本
-- 使用数据库
USE ai_chat;

-- 插入测试用户数据
-- 注意：密码需要使用BCrypt加密，这里使用的是 "123456" 的BCrypt加密结果
-- 实际使用时，应该通过后端API注册用户，这样密码会被正确加密

-- 管理员用户 (账号: admin, 密码: 123456)
INSERT INTO `user` (`user_account`, `user_password`, `user_name`, `user_profile`, `user_role`) 
VALUES ('admin', '$2a$10$7JB720yubVSOfvVWind4Xed7dAzKTRlreQW.HIldo/nUI2XNjWIXa', '系统管理员', '系统管理员账号，拥有所有权限', 'admin')
ON DUPLICATE KEY UPDATE 
    `user_name` = VALUES(`user_name`),
    `user_profile` = VALUES(`user_profile`),
    `user_role` = VALUES(`user_role`);

-- 普通用户1 (账号: user1, 密码: 123456)
INSERT INTO `user` (`user_account`, `user_password`, `user_name`, `user_profile`, `user_role`) 
VALUES ('user1', '$2a$10$7JB720yubVSOfvVWind4Xed7dAzKTRlreQW.HIldo/nUI2XNjWIXa', '测试用户1', '这是一个测试用户账号', 'user')
ON DUPLICATE KEY UPDATE 
    `user_name` = VALUES(`user_name`),
    `user_profile` = VALUES(`user_profile`),
    `user_role` = VALUES(`user_role`);

-- 普通用户2 (账号: user2, 密码: 123456)
INSERT INTO `user` (`user_account`, `user_password`, `user_name`, `user_profile`, `user_role`) 
VALUES ('user2', '$2a$10$7JB720yubVSOfvVWind4Xed7dAzKTRlreQW.HIldo/nUI2XNjWIXa', '测试用户2', '另一个测试用户账号', 'user')
ON DUPLICATE KEY UPDATE 
    `user_name` = VALUES(`user_name`),
    `user_profile` = VALUES(`user_profile`),
    `user_role` = VALUES(`user_role`);

-- 查看插入的数据
SELECT id, user_account, user_name, user_profile, user_role, create_time 
FROM `user` 
WHERE is_delete = 0 
ORDER BY id;

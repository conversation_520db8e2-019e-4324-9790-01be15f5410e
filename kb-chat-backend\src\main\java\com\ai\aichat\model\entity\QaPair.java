package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 问答对实体类
 * @TableName qa_pair
 */
@TableName(value = "qa_pair")
@Data
public class QaPair implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 问答对文件ID（外键）
     */
    private Long qaFileId;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 指令
     */
    private String instruction;

    /**
     * 输入
     */
    private String input;

    /**
     * 输出
     */
    private String output;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 软删除标记 0-正常 1-删除
     */
    @TableLogic
    private Integer isDelete;
}

<template>
  <div class="app-container">
    <div class="title">知识问答助手</div>

    <div class="buttons-container">
      <div
        class="button smart-qa"
        @click="router.push('/ask')">
        <div class="button-content">
          <div class="icon-wrapper">
            <img
              src="/assets/images/问答.png"
              class="button-icon" />
          </div>
          <span class="button-text">智能问答</span>

        </div>
      </div>

      <div
        class="button knowledge-manage"
        @click="router.push('/knowledgeManage')">
        <div class="button-content">
          <div class="icon-wrapper">
            <img
              src="/assets/images/知识.png"
              class="button-icon" />
          </div>
          <span class="button-text">知识管理</span>

        </div>
      </div>

      <div
        class="button model-manage"
        @click="router.push('/modelManage')">
        <div class="button-content">
          <div class="icon-wrapper">
            <img
              src="/assets/images/模型.png"
              class="button-icon" />
          </div>
          <span class="button-text">模型管理</span>

        </div>
      </div>

      <div
        class="button version-manage"
        @click="router.push('/version')">
        <div class="button-content">
          <div class="icon-wrapper">
            <img
              src="/assets/images/版本.png"
              class="button-icon" />
          </div>
          <span class="button-text">版本管理</span>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
  import { useRouter } from "vue-router";
  const router = useRouter();
</script>

<style scoped>
  .app-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .title {
    position: absolute;
    top: 80px;
    width: 100%;
    text-align: center;
    color: #ffffff;
    font-size: 3.5rem;
    font-family: YouShe, serif;
    font-weight: 600;
    letter-spacing: 2px;
    text-shadow: 0 4px 20px rgba(59, 130, 246, 0.5);
    z-index: 10;
  }

  .buttons-container {
    position: relative;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 1fr;
    gap: 40px;
    width: 1200px;
    height: 300px;
    margin-top: 100px;
  }

  .button {
    position: relative;
    width: 260px;
    height: 260px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    overflow: hidden;
  }

  .button::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg,
      rgba(59, 130, 246, 0.1),
      rgba(147, 197, 253, 0.1),
      rgba(59, 130, 246, 0.1));
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
  }

  .button-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(145deg, rgba(30, 58, 138, 0.9), rgba(15, 23, 42, 0.9));
    border: 2px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 2;
  }

  .icon-wrapper {
    position: relative;
    margin-bottom: 20px;
    transition: all 0.4s ease;
  }

  .button-icon {
    width: 80px;
    height: 80px;
    filter: brightness(1.1);
    transition: all 0.4s ease;
  }

  .button-text {
    font-family: YouShe, serif;
    color: #ffffff;
    font-size: 1.4rem;
    font-weight: 500;
    text-align: center;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
  }



  /* 悬停效果 */
  .button:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
      0 15px 35px rgba(59, 130, 246, 0.15),
      0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .button:hover::before {
    opacity: 1;
  }

  .button:hover .button-content {
    background: linear-gradient(145deg, rgba(59, 130, 246, 0.15), rgba(30, 58, 138, 0.95));
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 20px rgba(59, 130, 246, 0.1);
    animation: subtlePulse 2s ease-in-out infinite;
  }

  .button:hover .icon-wrapper {
    transform: translateY(-3px) scale(1.08);
  }

  .button:hover .button-icon {
    filter: brightness(1.2) drop-shadow(0 2px 8px rgba(59, 130, 246, 0.2));
  }

  .button:hover .button-text {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
    transform: translateY(-2px);
  }

  /* 点击效果 */
  .button:active {
    transform: translateY(-5px) scale(1.02);
    transition: all 0.1s ease;
  }

  /* 微妙脉冲动画 */
  @keyframes subtlePulse {
    0%, 100% {
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 20px rgba(59, 130, 246, 0.1);
    }
    50% {
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        0 0 25px rgba(59, 130, 246, 0.15);
    }
  }



  /* 入场动画 */
  .button {
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;
    transform: translateY(50px);
  }

  .button:nth-child(1) { animation-delay: 0.1s; }
  .button:nth-child(2) { animation-delay: 0.2s; }
  .button:nth-child(3) { animation-delay: 0.3s; }
  .button:nth-child(4) { animation-delay: 0.4s; }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .buttons-container {
      width: 1000px;
      height: 250px;
      gap: 30px;
    }

    .button {
      width: 220px;
      height: 220px;
    }

    .button-icon {
      width: 70px;
      height: 70px;
    }

    .button-text {
      font-size: 1.3rem;
    }
  }

  @media (max-width: 1024px) {
    .title {
      font-size: 2.8rem;
      top: 50px;
    }

    .buttons-container {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      width: 500px;
      height: 500px;
      gap: 40px;
      margin-top: 80px;
    }

    .button {
      width: 200px;
      height: 200px;
    }

    .button-icon {
      width: 60px;
      height: 60px;
    }

    .button-text {
      font-size: 1.2rem;
    }

    .icon-wrapper {
      margin-bottom: 18px;
    }
  }

  @media (max-width: 768px) {
    .title {
      font-size: 2.5rem;
      top: 40px;
    }

    .buttons-container {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      width: 350px;
      height: 350px;
      gap: 25px;
      margin-top: 80px;
    }

    .button {
      width: 150px;
      height: 150px;
    }

    .button-icon {
      width: 50px;
      height: 50px;
    }

    .button-text {
      font-size: 1rem;
    }

    .icon-wrapper {
      margin-bottom: 15px;
    }
  }

  @media (max-width: 480px) {
    .title {
      font-size: 2rem;
      top: 30px;
    }

    .buttons-container {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      width: 280px;
      height: 280px;
      gap: 20px;
      margin-top: 60px;
    }

    .button {
      width: 120px;
      height: 120px;
    }

    .button-icon {
      width: 40px;
      height: 40px;
    }

    .button-text {
      font-size: 0.9rem;
    }

    .icon-wrapper {
      margin-bottom: 10px;
    }
  }
</style>
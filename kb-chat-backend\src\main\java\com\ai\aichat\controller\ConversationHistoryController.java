package com.ai.aichat.controller;

import com.ai.aichat.common.BaseResponse;
import com.ai.aichat.common.ResultUtils;
import com.ai.aichat.exception.ErrorCode;
import com.ai.aichat.exception.ThrowUtils;
import com.ai.aichat.model.dto.request.UpdateConversationTitleDto;
import com.ai.aichat.model.vo.response.ChatHistoryListVo;
import com.ai.aichat.model.vo.response.ConversationListVo;
import com.ai.aichat.service.ConversationHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * 会话历史控制器
 */
@Slf4j
@Tag(name = "会话历史管理")
@RequiredArgsConstructor
@RestController
@Validated
public class ConversationHistoryController {

    private final ConversationHistoryService conversationHistoryService;

    @Operation(summary = "获取历史会话列表")
    @GetMapping("/get_conversations")
    public BaseResponse<ConversationListVo> getConversations(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,

            @Parameter(description = "每页数量", example = "20")
            @RequestParam(defaultValue = "20") Integer page_size,

            @Parameter(description = "聊天类型", example = "chat")
            @RequestParam(required = false) String chat_type,

            @Parameter(description = "日期范围")
            @RequestParam(required = false) String date_range) {

        ThrowUtils.throwIf(page < 1, ErrorCode.PARAMS_ERROR, "页码必须大于0");
        ThrowUtils.throwIf(page_size < 1 || page_size > 100, ErrorCode.PARAMS_ERROR, "每页数量必须在1-100之间");

        ConversationListVo result = conversationHistoryService.getConversations(page, page_size, chat_type, date_range);
        return ResultUtils.success(result);
    }

    @Operation(summary = "获取指定会话的聊天历史")
    @GetMapping("/chat_history")
    public BaseResponse<ChatHistoryListVo> getChatHistory(
            @Parameter(description = "会话ID", required = true)
            @RequestParam @NotBlank(message = "会话ID不能为空") String session_id) {

        ChatHistoryListVo result = conversationHistoryService.getChatHistory(session_id);
        return ResultUtils.success(result);
    }

    @Operation(summary = "删除指定会话")
    @DeleteMapping("/delete_conversation/{sessionId}")
    public BaseResponse<Boolean> deleteConversation(
            @Parameter(description = "会话ID", required = true)
            @PathVariable @NotBlank(message = "会话ID不能为空") String sessionId) {

        Boolean result = conversationHistoryService.deleteConversation(sessionId);
        return ResultUtils.success(result);
    }

    @Operation(summary = "更新会话标题")
    @PostMapping("/update_conversation_title")
    public BaseResponse<Boolean> updateConversationTitle(
            @Parameter(description = "更新会话标题请求", required = true)
            @RequestBody @Valid UpdateConversationTitleDto request) {

        Boolean result = conversationHistoryService.updateConversationTitle(
                request.getSession_id(), request.getTitle());
        return ResultUtils.success(result);
    }
}

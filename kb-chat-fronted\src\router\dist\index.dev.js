"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = exports.dynamicRoutes = exports.constantRoutes = void 0;

var _vueRouter = require("vue-router");

var _layout = _interopRequireDefault(require("@/layout"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { "default": obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj["default"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */
// 公共路由
var constantRoutes = [{
  path: '/redirect',
  component: _layout["default"],
  hidden: true,
  children: [{
    path: '/redirect/:path(.*)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/redirect/index.vue'));
      });
    }
  }]
}, {
  path: '/login',
  component: function component() {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require('@/views/login'));
    });
  },
  hidden: true
}, {
  path: '/register',
  component: function component() {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require('@/views/register'));
    });
  },
  hidden: true
}, {
  path: "/:pathMatch(.*)*",
  component: function component() {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require('@/views/error/404'));
    });
  },
  hidden: true
}, {
  path: '/401',
  component: function component() {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require('@/views/error/401'));
    });
  },
  hidden: true
}, {
  path: '',
  component: _layout["default"],
  redirect: '/index',
  children: [{
    path: '/index',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/index'));
      });
    },
    name: 'Index',
    meta: {
      title: '首页',
      icon: 'dashboard',
      affix: true
    }
  }]
}, {
  path: '/user',
  component: _layout["default"],
  hidden: true,
  redirect: 'noredirect',
  children: [{
    path: 'profile',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/system/user/profile/index'));
      });
    },
    name: 'Profile',
    meta: {
      title: '个人中心',
      icon: 'user'
    }
  }]
}]; // 动态路由，基于用户权限动态去加载

exports.constantRoutes = constantRoutes;
var dynamicRoutes = [{
  path: '/system/user-auth',
  component: _layout["default"],
  hidden: true,
  permissions: ['system:user:edit'],
  children: [{
    path: 'role/:userId(\\d+)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/system/user/authRole'));
      });
    },
    name: 'AuthRole',
    meta: {
      title: '分配角色',
      activeMenu: '/system/user'
    }
  }]
}, {
  path: '/system/role-auth',
  component: _layout["default"],
  hidden: true,
  permissions: ['system:role:edit'],
  children: [{
    path: 'user/:roleId(\\d+)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/system/role/authUser'));
      });
    },
    name: 'AuthUser',
    meta: {
      title: '分配用户',
      activeMenu: '/system/role'
    }
  }]
}, {
  path: '/system/dict-data',
  component: _layout["default"],
  hidden: true,
  permissions: ['system:dict:list'],
  children: [{
    path: 'index/:dictId(\\d+)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/system/dict/data'));
      });
    },
    name: 'Data',
    meta: {
      title: '字典数据',
      activeMenu: '/system/dict'
    }
  }]
}, {
  path: '/monitor/job-log',
  component: _layout["default"],
  hidden: true,
  permissions: ['monitor:job:list'],
  children: [{
    path: 'index/:jobId(\\d+)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/monitor/job/log'));
      });
    },
    name: 'JobLog',
    meta: {
      title: '调度日志',
      activeMenu: '/monitor/job'
    }
  }]
}, {
  path: '/tool/gen-edit',
  component: _layout["default"],
  hidden: true,
  permissions: ['tool:gen:edit'],
  children: [{
    path: 'index/:tableId(\\d+)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/tool/gen/editTable'));
      });
    },
    name: 'GenEdit',
    meta: {
      title: '修改生成配置',
      activeMenu: '/tool/gen'
    }
  }]
}, {
  path: '/res/resResourceInfoHis',
  component: _layout["default"],
  hidden: true,
  permissions: ['resource:resResourceInfo:list'],
  children: [{
    path: 'index/:id(\\d+)',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/resource/resResourceInfoHis/index'));
      });
    },
    name: 'resResourceInfoHis',
    meta: {
      title: '资源历史版本',
      activeMenu: '/resource/resResourceInfoHis'
    }
  }]
}, {
  path: '/scenario/:omtstd/:aid',
  component: _layout["default"],
  hidden: true,
  permissions: ['simulationRun:scenario:list'],
  children: [{
    path: 'edit',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/simulationRun/scenario/edit.vue'));
      });
    },
    name: 'ScenarioEdit',
    meta: {
      title: '想定编辑',
      activeMenu: '/simulationRun/scenario',
      noCache: true
    }
  }, {
    path: 'deduction',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/simulationRun/scenario/deduction/index.vue'));
      });
    },
    name: 'ScenarioDeduction',
    meta: {
      title: '想定推演',
      activeMenu: '/simulationRun/scenario'
    }
  }, {
    path: 'deduction/:assignmentType',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/simulationRun/scenario/deduction'));
      });
    },
    name: 'ScenarioDeductionType',
    meta: {
      title: '想定推演',
      activeMenu: '/simulationRun/scenario'
    }
  }, {
    path: 'record',
    component: function component() {
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require('@/views/simulationRun/scenario/deduction/record'));
      });
    },
    name: 'DeductionRecord',
    meta: {
      title: '记录回放',
      activeMenu: '/simulationRun/scenario'
    }
  }]
}];
exports.dynamicRoutes = dynamicRoutes;
var router = (0, _vueRouter.createRouter)({
  history: (0, _vueRouter.createWebHistory)(),
  routes: constantRoutes,
  scrollBehavior: function scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return {
        top: 0
      };
    }
  }
});
var _default = router;
exports["default"] = _default;
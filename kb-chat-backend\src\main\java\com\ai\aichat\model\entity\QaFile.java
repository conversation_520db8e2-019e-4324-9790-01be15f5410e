package com.ai.aichat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 问答对文件实体类
 * @TableName qa_file
 */
@TableName(value = "qa_file")
@Data
public class QaFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 问答库ID（外键）
     */
    private Long qaBaseId;

    /**
     * 文件名（唯一标识）
     */
    private String fileName;

    /**
     * 文件原始名称
     */
    private String originalName;

    /**
     * 文件存储路径
     */
    private String filePath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 问答对数量
     */
    private Integer qaPairCount;

    /**
     * 文件描述
     */
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 软删除标记 0-正常 1-删除
     */
    @TableLogic
    private Integer isDelete;
}

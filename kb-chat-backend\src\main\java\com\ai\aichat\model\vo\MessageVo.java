package com.ai.aichat.model.vo;

import com.ai.aichat.model.entity.ChatMessage;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.messages.Message;

import java.util.Date;

@NoArgsConstructor
@Data
public class MessageVo {
    private String role;
    private String content;
    private String sources;
    private String graphData;
    private Date createTime;

    public MessageVo(Message message){
        switch (message.getMessageType()){
            case USER:
                this.role = "user";
                break;
            case ASSISTANT:
                this.role = "assistant";
                break;
            default:
                this.role = "";
                break;
        }
        this.content = message.getText();
    }

    public MessageVo(ChatMessage chatMessage) {
        this.role = chatMessage.getRole();
        this.content = chatMessage.getContent();
        this.sources = chatMessage.getSources();
        this.graphData = chatMessage.getGraphData();
        this.createTime = chatMessage.getCreateTime();
    }
}

# AI 智能问答系统 

🌟 项目亮点

• 🚀 全栈集成：Spring Boot + Spring AI + LangChain4j 强力组合

• ⚡ 高性能推理：vLLM 加速大模型响应

• 🧠 智能增强：LightRAG 实现精准知识检索

• 🛠️ 灵活扩展：支持 LlamaFactory 模型微调

• 💾 数据双引擎：MySQL + Redis 确保稳定高效


🏗️ 技术架构

```
👤 用户
  │
  ▼
🖥️ Spring Boot API
  │
  ▼
🔗 SpringAI (RAG/Chain)
  ├──▶ 🚀 vLLM 推理引擎
  ├──▶ 🏭 LlamaFactory 微调
  │
  ▼
💡 LightRAG 知识增强
  │
  ▼
🗄️ MySQL + 🧠 Redis
```

🚀 快速开始

⚙️ 环境准备

• ☕ JDK 17+

• 🧰 Maven 3.9+

• 🐳 Docker (可选)

• 🐬 MySQL 8.0+

• 🔴 Redis 7.0+


📥 安装步骤

```bash
git clone https://github.com/your-repo/ai-qa-system.git
cd ai-qa-system
mvn clean install
```

⚡ 一键启动

```bash
java -jar target/ai-qa-system-*.jar
```

🧠 AI 模型部署

🚀 vLLM 快速启动

```bash
docker run --gpus all -p 8000:8000 \
  vllm/vllm:latest \
  --model meta-llama/Llama-2-7b-chat-hf \
  --trust-remote-code
```

🏭 LlamaFactory 微调

```bash
python src/main/python/train.py \
  --model_name_or_path meta-llama/Llama-2-7b-chat-hf \
  --data_path ./data/train.json \
  --output_dir ./models/finetuned
```

📡 API 接口

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/ask` | POST | 💬 提交问题 |
| `/api/knowledge` | PUT | 📚 更新知识库 |
| `/api/sessions` | GET | 🕒 获取会话历史 |
| `/api/models` | GET | 🤖 可用模型列表 |

🧪 测试示例

```bash
# 提问测试
curl -X POST http://localhost:8080/api/ask \
  -H "Content-Type: application/json" \
  -d '{"question":"如何配置Spring Security?"}'

# 压力测试
wrk -t4 -c100 -d60s http://localhost:8080/api/health
```

🛡️ 安全建议

1. 🔑 使用环境变量存储敏感信息
2. 🔒 启用HTTPS加密传输
3. 🛑 配置API访问限流
4. 👁️ 实现审计日志记录

🤝 加入贡献

欢迎通过以下方式参与：
1. 🐛 提交Issue报告问题
2. 💡 提出功能建议
3. 👩‍💻 提交Pull Request
4. 📖 完善项目文档

---

💡 提示：部署生产环境前请务必进行：
1. 🔐 安全扫描
2. ⚖️ 负载测试
3. 📊 性能基准测试
4. 🔄 CI/CD流水线配置
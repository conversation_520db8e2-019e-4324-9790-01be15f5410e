<template>
  <div class="container">
    <nav class="fixed-nav">
      <el-form :inline="true" @submit.prevent>
        <el-form-item label="问答库名称：">
          <el-input
            v-model="searchQuery"
            placeholder="搜索问答库"
            @keyup.enter="search">
          </el-input>
        </el-form-item>

        <el-form-item>
          <div class="btns" @click="search">搜索</div>
        </el-form-item>
      </el-form>
      <div>
        <div class="btns" @click="goToQAMaker">制作问答对</div>
        <div class="btns" @click="showCreateDialog">新建问答库</div>
        <div class="btns" @click="router.go(-1)">返回</div>
      </div>
    </nav>

    <main class="scrollable-content">
      <div
        class="items"
        v-for="item in filteredQABases"
        :key="item.kbName">
        <div class="kb-header">
          <p class="kb-name">{{ item.kbName }}</p>
          <p class="kb-date">{{ formatDate(item.latestFileDate || item.createdAt) }}</p>
        </div>

        <div class="kb-description">
          {{ item.description || "存储对话时保存的问答对" }}
        </div>

        <div class="kb-files">
          <img src="/assets/images/文档.png" />
          {{ (item.qaFiles || []).length }}个文件
        </div>

        <div class="kb-actions">
          <el-button
            @click="showEditDialog(item)"
            link
            type="primary">
            <el-icon><Edit /></el-icon>编辑
          </el-button>
          <el-button
            @click="showFileList(item)"
            link
            type="primary">
            <el-icon><Document /></el-icon>查看问答数据
          </el-button>
          <el-button
            @click="showUploadDialog(item)"
            link
            type="primary">
            <el-icon><Upload /></el-icon>上传
          </el-button>
          <el-button
            @click="deleteQABase(item)"
            link
            type="danger">
            <el-icon><Delete /></el-icon>删除
          </el-button>
        </div>
      </div>
    </main>

    <el-dialog
      v-model="createDialogVisible"
      title="新建问答库"
      width="30%">
      <el-form :model="createForm" label-width="100px" @submit.prevent>
        <el-form-item label="问答库名称">
          <el-input v-model="createForm.kbName" placeholder="请输入问答库名称" @keyup.enter="createQABase"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            placeholder="请输入问答库描述"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createQABase">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="fileListVisible"
      :title="`${currentKB} - 选择要查看的文件`"
      width="40%">
      <div class="file-list">
        <div v-for="file in paginatedFiles" :key="file" class="file-item">
          <span class="file-name">{{ file }}</span>
          <div class="file-actions">
            <el-button @click="viewQAHistory(currentKBItem, file)" type="primary" link>
              <el-icon><Document /></el-icon>查看问答对
            </el-button>
            <el-button @click="deleteQAFile(currentKB, file)" type="danger" link>
              <el-icon><Delete /></el-icon>删除
            </el-button>
          </div>
        </div>
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="fileCurrentPage"
            v-model:page-size="filePageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="currentFiles.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleFileSizeChange"
            @current-change="handleFileCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="dialogVisible"
      :title="`对话记录 - ${currentKB}/${currentFile}`"
      width="70%"
      @close="handleDialogClose">
      <div v-loading="loading">
        <div v-for="(qa, index) in qaHistory" :key="index" class="qa-item">
          <div class="question">
            <span class="label">问：</span>
            <span>{{ qa.question }}</span>
          </div>
          <div class="answer">
            <span class="label">答：</span>
            <span v-html="qa.answer"></span>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="editDialogVisible"
      title="编辑问答库"
      width="30%">
      <el-form :model="editForm" label-width="100px" @submit.prevent>
        <el-form-item label="原名称">
          <el-input v-model="editForm.kbName" disabled></el-input>
        </el-form-item>
        <el-form-item label="新名称">
          <el-input v-model="editForm.newKbName" placeholder="请输入新的问答库名称" @keyup.enter="updateQABase"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            placeholder="请输入新的问答库描述"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateQABase">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="uploadDialogVisible"
      title="上传问答库文件"
      width="30%">
      <el-form :model="uploadForm" label-width="100px" @submit.prevent>
        <el-form-item label="问答库">
          <el-input v-model="uploadForm.kbName" disabled></el-input>
        </el-form-item>
        <el-form-item label="文件">
          <input
            type="file"
            ref="fileInput"
            @change="handleFileChange"
            accept=".json"
            multiple
            style="display: none">
          <el-button type="primary" @click="triggerFileInput">选择文件</el-button>
          <div v-if="uploadForm.files.length > 0" style="margin-top: 10px">
            <div v-for="(file, index) in uploadForm.files" :key="index" class="file-item">
              <span>{{ file.name }}</span>
              <el-button type="danger" link @click="removeFile(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleFileUpload" :disabled="uploadForm.files.length === 0">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Delete, Edit, Upload } from '@element-plus/icons-vue'
import { marked } from 'marked'

const router = useRouter()
const searchQuery = ref('')
const qaBases = ref([])
const fileListVisible = ref(false)
const currentKB = ref('')
const currentFile = ref('')
const currentFiles = ref([])
const currentKBItem = ref(null)
const dialogVisible = ref(false)
const qaHistory = ref([])
const loading = ref(false)
const createDialogVisible = ref(false)
const createForm = ref({
  kbName: '',
  description: ''
})

const editDialogVisible = ref(false)
const editForm = ref({
  kbName: '',
  newKbName: '',
  description: ''
})

const uploadDialogVisible = ref(false)
const uploadForm = ref({
  kbName: '',
  files: []
})

const fileInput = ref(null)
const fileCurrentPage = ref(1)
const filePageSize = ref(10)

marked.setOptions({
  highlight: function(code, lang) {
    return code;
  }
});

const formatDate = (date) => {
  if (!date) return "暂无更新";
  if (typeof date === 'string' && date.includes('-')) {
    return date;
  }
  const d = new Date(date);
  if (isNaN(d.getTime())) return "暂无更新";
  return d.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const filteredQABases = computed(() => {
  return qaBases.value.filter(item =>
    item.kbName.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const fetchQABases = async () => {
  try {
    const response = await fetch('/dev-api/list_qa')
    if (!response.ok) {
      throw new Error('获取问答库列表失败')
    }
    const data = await response.json()
    if (data.code === 0) {
      qaBases.value = data.data.kbList;
    } else {
      throw new Error(data.message || data.msg || '获取问答库列表失败')
    }
  } catch (error) {
    console.error('获取问答库列表失败:', error)
    ElMessage.error(error.message)
    qaBases.value = []
  }
}

const showCreateDialog = () => {
  createForm.value = {
    kbName: '',
    description: ''
  }
  createDialogVisible.value = true
}

const createQABase = async () => {
  if (!createForm.value.kbName) {
    ElMessage.warning('请输入问答库名称')
    return
  }

  try {
    const response = await fetch('/dev-api/create_qa', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createForm.value)
    })

    const data = await response.json()
    
    if (data.code === 0) {
      ElMessage.success('创建问答库成功')
      createDialogVisible.value = false
      fetchQABases() // 刷新列表
    } else {
      throw new Error(data.msg || '创建问答库失败')
    }
  } catch (error) {
    console.error('创建问答库失败:', error)
    ElMessage.error(error.message)
  }
}

const showFileList = (item) => {
  currentKB.value = item.kbName
  currentFiles.value = item.qaFiles
  currentKBItem.value = item
  fileListVisible.value = true
  fileCurrentPage.value = 1 // 重置到第一页
}

const viewQAHistory = async (item, file) => {
  currentKB.value = item.kbName
  currentFile.value = file
  fileListVisible.value = false
  dialogVisible.value = true
  loading.value = true

  try {
    const response = await fetch(
      `/dev-api/get_qa_history?kb_name=${encodeURIComponent(item.kbName)}&file_name=${encodeURIComponent(file)}`
    )

    const data = await response.json()
    
    if (data.code === 0) {
      const qaContent = data.data.qaPairs || []
      qaHistory.value = qaContent.map(qa => {
        // 检查是否包含</think>标签
        const hasThinkTag = qa.output && qa.output.includes('</think>')
        let formattedAnswer

        if (hasThinkTag) {
          // 如果包含</think>标签，先分离思考内容和最终内容
          const parts = qa.output.split('</think>')
          // 分别处理思考内容和最终内容，然后重新组合
          const thinkingPart = parts[0]
          const finalPart = parts.length > 1 ? parts[1] : ''
          
          formattedAnswer = `
            <div class="thinking-section">
              <details>
                <summary class="thinking-title">深度思考过程</summary>
                <div class="thinking-content">${marked(thinkingPart)}</div>
              </details>
            </div>
            <div class="final-content">${marked(finalPart)}</div>
          `
        } else {
          // 如果不包含</think>标签，正常处理整个输出
          formattedAnswer = marked(qa.output)
        }

        return {
          question: qa.instruction,
          input: qa.input,
          answer: formattedAnswer
        }
      })
    } else {
      throw new Error(data.msg || '获取对话历史失败')
    }
  } catch (error) {
    console.error('获取对话历史失败:', error)
    ElMessage.error(error.message)
    qaHistory.value = []
  } finally {
    loading.value = false
  }
}

const deleteQAFile = async (kbName, fileName) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${fileName}" 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(
      `/dev-api/delete_qa?kb_name=${encodeURIComponent(kbName)}&file_name=${encodeURIComponent(fileName)}`,
      {
        method: 'DELETE'
      }
    )

    const data = await response.json()
    
    if (data.code === 0) {
      ElMessage.success('删除成功')
      currentFiles.value = currentFiles.value.filter(file => file !== fileName)
      if (currentFile.value === fileName) {
        dialogVisible.value = false
        currentFile.value = ''
      }
      // 如果当前页没有文件了，且不是第一页，则跳转到上一页
      if (paginatedFiles.value.length === 0 && fileCurrentPage.value > 1) {
        fileCurrentPage.value--
      }
      fetchQABases() // 刷新问答库列表
    } else {
      throw new Error(data.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error(error.message)
    }
  }
}

const handleDialogClose = () => {
  qaHistory.value = []
  currentFile.value = ''
}

const search = () => {
  // 搜索功能已经在 computed 中实现
}

const close = () => {
  router.push('/ask')
}

// 跳转到问答对制作页面
const goToQAMaker = () => {
  window.open('http://localhost:1717/', '_blank')
}

const showEditDialog = (item) => {
  editForm.value = {
    kbName: item.kbName,
    newKbName: item.kbName,
    description: item.description
  }
  editDialogVisible.value = true
}

const updateQABase = async () => {
  if (!editForm.value.kbName) {
    ElMessage.warning('问答库名称不能为空')
    return
  }

  try {
    const response = await fetch('/dev-api/update_qa', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(editForm.value)
    })

    const data = await response.json()
    
    if (data.code === 0) {
      ElMessage.success('更新问答库成功')
      editDialogVisible.value = false
      fetchQABases() // 刷新列表
    } else {
      throw new Error(data.msg || '更新问答库失败')
    }
  } catch (error) {
    console.error('更新问答库失败:', error)
    ElMessage.error(error.message)
  }
}

const showUploadDialog = (item) => {
  uploadForm.value = {
    kbName: item.kbName,
    files: []
  }
  uploadDialogVisible.value = true
}

const triggerFileInput = () => {
  fileInput.value.click()
}

const handleFileChange = (event) => {
  const files = Array.from(event.target.files)
  const validFiles = files.filter(file => file.name.endsWith('.json'))
  
  if (validFiles.length !== files.length) {
    ElMessage.warning('只能上传JSON格式的文件')
  }
  
  uploadForm.value.files = [...uploadForm.value.files, ...validFiles]
  // 清空文件输入框，以便可以重复选择相同的文件
  event.target.value = ''
}

const removeFile = (index) => {
  uploadForm.value.files.splice(index, 1)
}

const handleFileUpload = async () => {
  if (uploadForm.value.files.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  try {
    // 删除文件存在检查，直接在saveChat中处理重复文档

    // 上传文件
    for (const file of uploadForm.value.files) {
      // 读取文件内容，不进行解析验证，直接存储
      const fileContent = await file.text()

      // 构建请求数据
      const requestData = {
        content: fileContent,
        kbName: uploadForm.value.kbName,
        datasetName: file.name.replace('.json', ''),
        description: `从文件 ${file.name} 导入的问答对`
      }

      const response = await fetch('/dev-api/save_chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      const data = await response.json()

      if (data.code !== 0) {
        throw new Error(`文件 ${file.name} 上传失败: ${data.msg || '未知错误'}`)
      }
    }

    ElMessage.success('所有文件上传成功')
    uploadDialogVisible.value = false
    uploadForm.value.files = []
    fetchQABases() // 刷新列表
  } catch (error) {
    console.error('上传文件失败:', error)
    ElMessage.error(error.message)
  }
}

const deleteQABase = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除问答库 "${item.kbName}" 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch('/dev-api/delete_qa_base', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        kbName: item.kbName
      })
    })

    const data = await response.json()
    
    if (data.code === 0) {
      ElMessage.success('删除成功')
      fetchQABases() // 刷新列表
    } else {
      throw new Error(data.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除问答库失败:', error)
      ElMessage.error(error.message)
    }
  }
}

// 计算当前页的文件列表
const paginatedFiles = computed(() => {
  const start = (fileCurrentPage.value - 1) * filePageSize.value
  const end = start + filePageSize.value
  return currentFiles.value.slice(start, end)
})

// 处理每页显示数量变化
const handleFileSizeChange = (val) => {
  filePageSize.value = val
  fileCurrentPage.value = 1 // 重置到第一页
}

// 处理页码变化
const handleFileCurrentChange = (val) => {
  fileCurrentPage.value = val
}

onMounted(() => {
  fetchQABases()
})
</script>

<style scoped>
.btns {
  display: inline-block;
  width: 140px;
  height: 30px;
  text-align: center;
  background-image: url("/assets/images/button/主页按钮-正常.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: #b1c5da;
  font-size: 14px;
  line-height: 30px;
  margin-right: 10px;
}

.btns:hover {
  cursor: pointer;
  filter: contrast(150%) brightness(120%);
}

.items {
  background-image: url("/assets/images/知识库底图.png");
  width: 502px;
  height: 309px;
  position: relative;
  margin: 10px;
}

.kb-header {
  position: absolute;
  left: 100px;
  top: 20px;
  color: #fff;
}

.kb-name {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.kb-date {
  margin-top: 10px;
  font-size: 14px;
}

.kb-description {
  position: absolute;
  left: 40px;
  top: 100px;
  height: 100px;
  width: 440px;
  color: #0084ff;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.kb-files {
  position: absolute;
  left: 20px;
  top: 200px;
  display: flex;
  align-items: center;
  color: #0084ff;
  font-size: 16px;
}

.kb-files img {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.kb-actions {
  position: absolute;
  bottom: 25px;
  right: 40px;
  color: #0084ff;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fixed-nav {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.scrollable-content {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  margin: 5px 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.file-name {
  color: #b1c5da;
  font-size: 14px;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.qa-item {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  background-color: #163879;
}

.question, .answer {
  margin: 10px 0;
}

.label {
  font-weight: bold;
  color: #b1c5da;
  margin-right: 10px;
}

.question span:last-child {
  color: #fff;
}

.answer span:last-child {
  color: #0084ff;
}

.answer :deep(pre) {
  background-color: #1e1e1e;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1em 0;
}

.answer :deep(code) {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  color: #d4d4d4;
}

.answer :deep(p) {
  margin: 1em 0;
}

.answer :deep(pre code) {
  display: block;
  padding: 1em;
  overflow-x: auto;
  background: #1e1e1e;
  color: #d4d4d4;
  border-radius: 5px;
}

/* 思考过程样式 */
.thinking-section {
  margin-bottom: 16px;
  border-radius: 6px;
  background-color: rgba(19, 255, 243, 0.05);
  padding: 5px;
}

.thinking-title {
  color: #13fff3;
  font-size: 14px;
  cursor: pointer;
  padding: 8px;
  display: inline-block;
}

.thinking-title:hover {
  color: #00e6da;
}

.thinking-content {
  padding: 12px;
  background-color: rgba(19, 255, 243, 0.08);
  border-radius: 6px;
  margin-top: 8px;
  color: #b1c5da;
}

.thinking-section details {
  border-radius: 6px;
  overflow: hidden;
}

.final-content {
  margin-top: 15px;
}

/* 确保代码块样式依然有效 */
.answer :deep(.thinking-content pre),
.answer :deep(.final-content pre) {
  background-color: #1e1e1e;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1em 0;
}

.answer :deep(.thinking-content code),
.answer :deep(.final-content code) {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  color: #d4d4d4;
}

.file-list {
  min-height: 200px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #b1c5da;
  --el-pagination-hover-color: #0084ff;
  --el-pagination-bg-color: #163879;
  --el-pagination-text-color: #b1c5da;
}

:deep(.el-pagination .el-pagination__total),
:deep(.el-pagination .el-pagination__jump) {
  color: #b1c5da;
}

:deep(.el-pagination .el-pagination__sizes .el-input__wrapper) {
  background-color: #163879;
  border-color: #0084ff;
}

:deep(.el-pagination .el-pagination__sizes .el-input__inner) {
  color: #b1c5da;
}

:deep(.el-pagination .el-pager li) {
  background-color: #163879;
  color: #b1c5da;
}

:deep(.el-pagination .el-pager li.active) {
  background-color: #0084ff;
  color: #fff;
}

:deep(.el-pagination .el-pager li:hover) {
  color: #0084ff;
}
</style>

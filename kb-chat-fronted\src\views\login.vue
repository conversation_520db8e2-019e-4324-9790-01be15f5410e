<template>
  <div class="login">
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form">
      <h3 class="title">系统登录</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="账号">
          <template #prefix
            ><svg-icon
              icon-class="user"
              class="el-input__icon input-icon"
          /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter="handleLogin">
          <template #prefix
            ><svg-icon
              icon-class="password"
              class="el-input__icon input-icon"
          /></template>
        </el-input>
      </el-form-item>

      <el-checkbox
        v-model="loginForm.rememberMe"
        style="margin: 0px 0px 25px 0px"
        >记住密码</el-checkbox
      >
      <el-form-item style="width: 100%">
        <button
          :loading="loading"
          size="large"
          type="primary"
          class="login-btn"
          @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </button>
        <div
          style="float: right"
          v-if="register">
          <router-link
            class="link-type"
            :to="'/register'"
            >立即注册</router-link
          >
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer"></div>
  </div>
</template>

<script setup>
  import Cookies from "js-cookie";
  import { encrypt, decrypt } from "@/utils/jsencrypt";
  import useUserStore from "@/store/modules/user";

  const userStore = useUserStore();
  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const loginForm = ref({
    username: "",
    password: "",
    rememberMe: false,
  });

  const loginRules = {
    username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
    password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  };

  const loading = ref(false);
  // 注册开关
  const register = ref(false);
  const redirect = ref(undefined);

  function handleLogin() {
    proxy.$refs.loginRef.validate((valid) => {
      if (valid) {
        loading.value = true;
        // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
        if (loginForm.value.rememberMe) {
          Cookies.set("username", loginForm.value.username, { expires: 30 });
          Cookies.set("password", encrypt(loginForm.value.password), {
            expires: 30,
          });
          Cookies.set("rememberMe", loginForm.value.rememberMe, {
            expires: 30,
          });
        } else {
          // 否则移除
          Cookies.remove("username");
          Cookies.remove("password");
          Cookies.remove("rememberMe");
        }
        // 调用action的登录方法
        userStore
          .login(loginForm.value)
          .then(() => {
            router.push({ path: redirect.value || "/" });
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });
  }



  function getCookie() {
    const username = Cookies.get("username");
    const password = Cookies.get("password");
    const rememberMe = Cookies.get("rememberMe");
    loginForm.value = {
      username: username === undefined ? loginForm.value.username : username,
      password:
        password === undefined ? loginForm.value.password : decrypt(password),
      rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
    };
  }

  getCookie();
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 100px);
  }
  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #b6caff;
    font-size: 48px;
    font-family: YouShe, serif;
  }

  .login-form {
    border-radius: 6px;
    background: url("@/assets/images/login-bg.png");
    max-width: 970px;
    min-width: 400px;
    width: 50%;
    padding: 40px 40px 20px 40px;
    .el-input {
      height: 40px;
      input {
        height: 40px;
      }
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
    }
  }
  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }
  .login-code {
    width: 33%;
    height: 40px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .login-btn {
    border: none;
    width: 200px;
    height: 44px;
    margin: 0 auto;
    border-radius: 22px;
    background: #9ae1ff;
    color: #213896;
    font-size: 23px;
    font-family: YouShe, serif;
    cursor: pointer;
  }

  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-size: 12px;
    letter-spacing: 1px;
  }
  .login-code-img {
    height: 40px;
    padding-left: 12px;
  }
</style>

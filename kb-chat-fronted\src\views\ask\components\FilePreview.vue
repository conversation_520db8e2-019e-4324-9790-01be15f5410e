<template>
  <div v-if="modelValue || show" class="file-preview-container">
    <div class="file-content">
      <div
        ref="word"
        id="fileShow"
        class="words">
      </div>
    </div>
    <!-- 加载状态显示 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">加载中...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { renderAsync } from 'docx-preview';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show', 'update:modelValue', 'fileUrlChange']);

const activeFileUrl = ref('');
// 加载状态变量
const loading = ref(false);
// 添加高亮位置追踪
const currentHighlightIndex = ref(0);
const totalHighlights = ref(0);

// 监听show属性变化
watch(() => props.show, (newVal) => {
  if (!newVal) {
    clearPreview();
  }
});

// 监听modelValue属性变化
watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    clearPreview();
  }
});

// 清除预览内容和状态
const clearPreview = () => {
  const previewContainer = document.getElementById('fileShow');
  if (previewContainer) previewContainer.innerHTML = '';
  activeFileUrl.value = '';
  currentHighlightIndex.value = 0;
  totalHighlights.value = 0;
};

// 处理文件预览
const handleSourceClick = async (source) => {
  if (!source.file_source) {
    ElMessage.warning('未找到源文件路径');
    return;
  }
  
  // 先清除上一次的预览内容
  clearPreview();
  
  // 设置加载状态
  loading.value = true;
  
  // 检查是否有相同文件的多个内容
  console.log('文件来源:', source.title);
  console.log('文件路径:', source.file_source);
  
  try {
    // 步骤1: 先处理单个源文件的加载
    await initWordFile(source.file_source);
    
    // 步骤2: 获取所有具有相同file_source的源
    // 这需要从ChatWindow获取所有sources，或者在这里进行额外处理
    // 由于我们目前只能获取到单个source，需要从它的关联信息获取其他内容
    
    // 获取传入的内容或内容列表
    const directContents = source.contents || (source.content ? [source.content] : []);
    
    // 检查是否有额外的sourcesCollection属性，这可能由调用者传入
    // 如果没有，则只处理当前source
    const allContents = (source.sourcesCollection && Array.isArray(source.sourcesCollection))
      ? source.sourcesCollection.filter(item => 
          item.file_source === source.file_source && item.content
        ).map(item => item.content)
      : directContents;
    
    console.log('内容数量:', allContents.length);
    console.log('所有内容:', allContents);
    
    // 构建适合高亮处理的内容对象
    const contentForHighlight = {
      sources: [{
        title: source.title,
        content: allContents.join('\n'), // 将所有内容合并
        contents: allContents,
        file_source: source.file_source
      }]
    };
    
    // 步骤3: 高亮处理所有内容
    await highlightAllContents(contentForHighlight);
    
    // 更新当前文件URL
    activeFileUrl.value = source.file_source;
    emit('fileUrlChange', source.file_source);
  } catch (error) {
    console.error('文件处理失败:', error);
    ElMessage.error('文档加载失败');
  } finally {
    // 处理完成后取消加载状态
    loading.value = false;
  }
};

// 只加载文件，不处理高亮
const initWordFile = async (wordUrl) => {
  console.log('initWordFile 被调用:', wordUrl);
  
  // 清除可能的旧内容
  const previewContainer = document.getElementById('fileShow');
  if (previewContainer) previewContainer.innerHTML = '';
  
  const match = wordUrl.match(/knowledge_base\/(.+)/);
  if (!match) {
    ElMessage.error('无效的文件路径');
    return;
  }
  
  const relativePath = match[1].replace(/\\/g, '/');
  const encodedPath = encodeURIComponent(relativePath);
  
  try {
    // 添加时间戳避免缓存
    const timestamp = new Date().getTime();
    const response = await axios.get(`/dev-api/files/${encodedPath}?t=${timestamp}`, { 
      responseType: 'blob',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    
    console.log('文档加载成功，准备渲染');
    await renderAsync(new Blob([response.data]), previewContainer);
    
    // 等待文档完全渲染
    await new Promise(resolve => setTimeout(resolve, 1000));
    return true;
  } catch (error) {
    console.error('文档处理失败:', error);
    throw error;
  }
};

// 仅高亮处理内容
const highlightAllContents = async (content) => {
  console.log('开始高亮所有内容');
  // 重置高亮索引
  currentHighlightIndex.value = 0;
  totalHighlights.value = 0;
  
  if (content?.sources?.length) {
    console.log('处理高亮，sources:', content.sources);
    await highlightContent(content.sources);
  } else {
    console.log('没有有效的内容来源进行高亮');
  }
};

// 之前的initWord方法拆分为上面两个方法，保留原方法以兼容
const initWord = async (wordUrl, content) => {
  console.log('initWord 被调用，参数:', { wordUrl, content });
  
  try {
    // 先加载文件
    await initWordFile(wordUrl);
    
    // 然后处理高亮
    await highlightAllContents(content);
    
    activeFileUrl.value = wordUrl;
    emit('fileUrlChange', wordUrl);
  } catch (error) {
    console.error('文档处理失败:', error);
    ElMessage.error('文档加载或处理失败');
    throw error;
  }
};

const highlightContent = async (sources) => {
  const container = document.getElementById('fileShow');
  if (!container) {
    console.error('预览容器不存在');
    return;
  }
  
  console.log('开始高亮处理');
  
  try {
    if (!sources?.length) return;
    
    // 创建一个简单的文本索引
    const textContent = container.textContent;
    console.log(`文档总文本长度: ${textContent.length}`);
    
    // 初始化高亮计数
    let successfulHighlights = 0;
    
    // 处理每个源
    for (const source of sources) {
      // 首先尝试使用 contents 数组中的所有内容
      if (source.contents && Array.isArray(source.contents) && source.contents.length > 0) {
        console.log(`处理source中的${source.contents.length}个内容片段`);
        
        // 依次处理每个内容片段
        for (const contentItem of source.contents) {
          if (!contentItem || typeof contentItem !== 'string') {
            console.log('content片段无效内容，跳过');
            continue;
          }
          
          console.log('处理content片段:', contentItem.substring(0, 100) + '...');
          const highlightResults = await processContentForHighlight(contentItem, textContent, container);
          successfulHighlights += highlightResults.successful;
        }
      } 
      // 如果没有 contents 数组或为空，尝试使用 content 字段
      else if (source.content && typeof source.content === 'string') {
        console.log('处理单个source.content');
        console.log('处理source:', source.content.substring(0, 100) + '...');
        const highlightResults = await processContentForHighlight(source.content, textContent, container);
        successfulHighlights += highlightResults.successful;
      } else {
        console.log('source没有有效内容，跳过');
      }
    }
    
    // 更新总高亮数量
    totalHighlights.value = successfulHighlights;
    
    // 如果有成功的高亮，自动滚动到第一个
    if (successfulHighlights > 0) {
      console.log(`总共完成了 ${successfulHighlights} 处高亮，自动滚动到第一个`);
      setTimeout(() => {
        currentHighlightIndex.value = 0; // 确保从第一个开始
        scrollToHighlight('first');
      }, 500);
    } else {
      console.log('没有成功创建任何高亮');
      // 尝试直接搜索并高亮关键词
      await fallbackKeywordHighlight(container, sources);
    }
  } catch (error) {
    console.error('高亮处理出错:', error);
  }
};

// 添加备用关键词高亮方法
async function fallbackKeywordHighlight(container, sources) {
  console.log('尝试使用备用关键词高亮方法');
  
  const textContent = container.textContent;
  let successfulHighlights = 0;
  
  // 提取所有内容中的关键词
  const allKeywords = new Set();
  
  // 从所有来源中提取关键词
  for (const source of sources) {
    // 处理内容数组
    if (source.contents && Array.isArray(source.contents)) {
      for (const content of source.contents) {
        if (content && typeof content === 'string') {
          // 提取2-4个字的关键词
          const keywords = extractKeywords(content);
          keywords.forEach(keyword => allKeywords.add(keyword));
        }
      }
    }
    
    // 处理单个内容
    if (source.content && typeof source.content === 'string') {
      const keywords = extractKeywords(source.content);
      keywords.forEach(keyword => allKeywords.add(keyword));
    }
  }
  
  console.log('提取到的关键词:', [...allKeywords]);
  
  // 高亮关键词
  for (const keyword of allKeywords) {
    // 跳过过短的关键词
    if (keyword.length < 2) continue;
    
    let pos = 0;
    while ((pos = textContent.indexOf(keyword, pos)) !== -1) {
      // 尝试高亮这个关键词
      const success = highlightTextRange(container, pos, pos + keyword.length);
      if (success) {
        successfulHighlights++;
      }
      pos += keyword.length;
    }
  }
  
  // 更新总高亮数量
  totalHighlights.value = successfulHighlights;
  
  // 如果有成功的高亮，自动滚动到第一个
  if (successfulHighlights > 0) {
    console.log(`关键词方法完成了 ${successfulHighlights} 处高亮，自动滚动到第一个`);
    setTimeout(() => {
      currentHighlightIndex.value = 0; // 确保从第一个开始
      scrollToHighlight('first');
    }, 500);
  } else {
    console.log('备用方法也没有创建任何高亮');
  }
}

// 提取关键词的辅助函数
function extractKeywords(text) {
  if (!text) return [];
  
  // 分割为单词/字符
  const words = text.split(/\s+/);
  const keywords = new Set();
  
  // 提取2-4个字的词语
  for (const word of words) {
    if (word.length >= 2 && word.length <= 6) {
      keywords.add(word);
    }
  }
  
  // 如果是中文，尝试提取2-4字词组
  const chineseMatches = text.match(/[\u4e00-\u9fa5]{2,6}/g);
  if (chineseMatches) {
    chineseMatches.forEach(match => keywords.add(match));
  }
  
  return [...keywords];
}

// 将内容处理逻辑抽取为单独的函数，修改返回值以跟踪成功数量
async function processContentForHighlight(sourceContent, textContent, container) {
  const sourceText = sourceContent.trim();
  let attempted = 0;
  let successful = 0;
  
  // 提取关键句子，降低长度限制为5个字符
  const sentences = sourceText
    .split(/[。！？\n]/)
    .map(s => s.trim())
    .filter(s => s.length >= 5);
  
  console.log(`提取了 ${sentences.length} 个句子`);
  
  // 合并连续的句子（如果两个句子在文档中是连续的，则认为它们是一个高亮区域）
  const mergedRanges = [];
  let currentRange = null;
  
  for (const sentence of sentences) {
    if (sentence.length < 5) continue; // 降低长度限制
    
    // 尝试多种匹配方式，增加匹配成功率
    let index = -1;
    
    // 1. 首先尝试精确匹配
    index = textContent.indexOf(sentence);
    
    // 2. 如果精确匹配失败且文本较短（<=15字符），尝试去除空格后匹配
    if (index === -1 && sentence.length <= 15) {
      const noSpaceSentence = sentence.replace(/\s+/g, '');
      const noSpaceContent = textContent.replace(/\s+/g, '');
      if (noSpaceSentence.length >= 5) {
        const noSpaceIndex = noSpaceContent.indexOf(noSpaceSentence);
        if (noSpaceIndex !== -1) {
          // 找到了无空格匹配，尝试映射回原始文本位置
          // 这是一个近似值，可能不完全准确
          index = findApproximatePosition(textContent, sentence, noSpaceIndex);
        }
      }
    }
    
    // 3. 对于短文本，尝试部分匹配（只要文本有一定重叠就认为匹配成功）
    if (index === -1 && sentence.length <= 20) {
      // 对于特别短的文本，考虑分词匹配
      const words = sentence.split(/\s+/).filter(w => w.length >= 3);
      for (const word of words) {
        const wordIndex = textContent.indexOf(word);
        if (wordIndex !== -1) {
          // 找到一个关键词匹配，使用该位置作为近似位置
          index = wordIndex;
          console.log(`使用关键词匹配 "${word}" 在位置 ${index}`);
          break;
        }
      }
    }
    
    // 如果所有匹配方法都失败，跳过这个句子
    if (index === -1) continue;
    
    console.log(`找到句子: "${sentence.substring(0, 30)}..." 在位置 ${index}`);
    
    const endIndex = index + sentence.length;
    
    // 如果当前没有范围，创建一个新范围
    if (!currentRange) {
      currentRange = { start: index, end: endIndex };
      continue;
    }
    
    // 如果当前句子与上一个范围连续或重叠（允许较大间隔），则合并范围
    const maxGap = 200; // 增加最大容许的间隔字符数，使更多文本合并为一处
    if (index <= currentRange.end + maxGap) {
      // 更新当前范围的结束位置
      currentRange.end = Math.max(currentRange.end, endIndex);
    } else {
      // 添加当前范围并开始一个新范围
      mergedRanges.push(currentRange);
      currentRange = { start: index, end: endIndex };
    }
  }
  
  // 添加最后一个范围（如果存在）
  if (currentRange) {
    mergedRanges.push(currentRange);
  }
  
  console.log(`合并后共有 ${mergedRanges.length} 个高亮区域`);
  
  // 为每个合并后的范围创建高亮
  for (const range of mergedRanges) {
    attempted++;
    try {
      // 使用 Range API 创建高亮
      const success = highlightTextRange(container, range.start, range.end);
      if (success) {
        successful++;
      }
    } catch (error) {
      console.error('高亮区域失败:', error);
    }
  }
  
  return { attempted, successful };
}

// 添加辅助函数，在无空格文本中找到的位置映射回原始文本的近似位置
function findApproximatePosition(originalText, originalPattern, noSpaceMatchIndex) {
  // 计算无空格模式前有多少个字符
  let charCount = 0;
  let spaceCount = 0;
  
  for (let i = 0; i < originalText.length && charCount < noSpaceMatchIndex; i++) {
    if (originalText[i].trim() === '') {
      spaceCount++;
    } else {
      charCount++;
    }
  }
  
  // 返回近似位置（非空字符数加上之前的空格数）
  return noSpaceMatchIndex + spaceCount;
}

// 使用 Range API 高亮文本范围
function highlightTextRange(container, startIndex, endIndex) {
  // 创建范围
  const range = document.createRange();
  const startInfo = findNodeAndOffsetAtIndex(container, startIndex);
  const endInfo = findNodeAndOffsetAtIndex(container, endIndex);
  
  if (!startInfo || !endInfo) {
    console.log('无法找到文本位置');
    return false;
  }
  
  try {
    // 设置范围边界
    range.setStart(startInfo.node, startInfo.offset);
    range.setEnd(endInfo.node, endInfo.offset);
    
    // 获取范围内的文本
    const text = range.toString();
    
    // 如果文本为空，跳过
    if (!text || text.trim() === '') {
      console.log('高亮范围内没有文本，跳过');
      return false;
    }
    
    // 输出调试信息
    console.log('尝试高亮文本:', text.substring(0, 30) + (text.length > 30 ? '...' : ''));
    
    // 直接创建并插入元素，而不是使用surroundContents方法
    // 这种方法在复杂DOM结构中更可靠
    const highlightSpan = document.createElement('mark'); // 使用mark标签而不是span
    highlightSpan.className = 'highlight-text';
    
    // 如果是短文本，添加额外的CSS类
    if (text.length < 15) {
      highlightSpan.className += ' short-text';
    }
    
    try {
      // 尝试使用surroundContents方法（简单情况）
      range.surroundContents(highlightSpan);
      console.log('使用surroundContents成功高亮文本');
      return true;
    } catch (surroundError) {
      console.warn('surroundContents方法失败，尝试替代方法:', surroundError);
      
      // 替代方法1: 使用extractContents和insertNode
      try {
        const fragment = range.extractContents();
        highlightSpan.appendChild(fragment);
        range.insertNode(highlightSpan);
        console.log('使用extractContents和insertNode成功高亮文本');
        return true;
      } catch (extractError) {
        console.warn('extractContents方法失败，尝试最终备选方法:', extractError);
        
        // 替代方法2: 完全重新创建DOM元素
        try {
          // 删除原始内容
          range.deleteContents();
          
          // 创建新元素
          highlightSpan.textContent = text;
          
          // 插入新元素
          range.insertNode(highlightSpan);
          console.log('使用deleteContents和insertNode成功高亮文本');
          return true;
        } catch (finalError) {
          console.error('所有高亮方法都失败:', finalError);
          return false;
        }
      }
    }
  } catch (error) {
    console.error('Range API 高亮失败:', error);
    return false;
  }
}

// 在容器中找到指定索引位置的节点和偏移量
function findNodeAndOffsetAtIndex(container, targetIndex) {
  // 使用树遍历找到指定索引的文本节点
  let currentIndex = 0;
  
  // 递归函数
  function findNodeRecursive(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      const nodeLength = node.textContent.length;
      
      // 检查目标索引是否在当前节点内
      if (currentIndex <= targetIndex && targetIndex < currentIndex + nodeLength) {
        return {
          node: node,
          offset: targetIndex - currentIndex
        };
      }
      
      currentIndex += nodeLength;
    } else {
      // 遍历子节点
      for (let i = 0; i < node.childNodes.length; i++) {
        const result = findNodeRecursive(node.childNodes[i]);
        if (result) return result;
      }
    }
    
    return null;
  }
  
  return findNodeRecursive(container);
}

// 关闭预览方法，同时更新show和modelValue
const closePreview = () => {
  clearPreview();
  emit('update:show', false);
  emit('update:modelValue', false);
  emit('fileUrlChange', '');
};

// 添加文献引用处理方法
const formatSourceTitle = (title) => {
  if (!title) return '';
  return title.replace('来源文件：', '');
};

// 修改滚动到高亮位置的方法，修复Word文档滚动问题
const scrollToHighlight = (direction = 'next') => {
  console.log('滚动到高亮位置，方向:', direction);
  
  // 获取所有高亮元素
  const highlights = document.getElementsByClassName('highlight-text');
  totalHighlights.value = highlights.length;
  
  if (!highlights || highlights.length === 0) {
    console.log('未找到高亮元素');
    return false;
  }
  
  // 确保当前索引在有效范围内
  if (currentHighlightIndex.value < 0 || currentHighlightIndex.value >= highlights.length) {
    currentHighlightIndex.value = 0;
  }
  
  // 根据方向更新当前索引
  let previousIndex = currentHighlightIndex.value;
  if (direction === 'next') {
    currentHighlightIndex.value = (currentHighlightIndex.value + 1) % highlights.length;
  } else if (direction === 'prev') {
    currentHighlightIndex.value = (currentHighlightIndex.value - 1 + highlights.length) % highlights.length;
  } else if (direction === 'first') {
    currentHighlightIndex.value = 0;
  } 
  // 保持当前索引不变，用于重新定位
  else if (direction === 'current') {
    // 索引保持不变
  }
  
  // 如果是"current"模式且索引没变，检查元素是否已经可见
  if (direction === 'current' && currentHighlightIndex.value === previousIndex) {
    const currentHighlight = highlights[currentHighlightIndex.value];
    if (!currentHighlight) return false;
    
    // 获取元素位置信息
    const highlightRect = currentHighlight.getBoundingClientRect();
    const containerRect = document.querySelector('.words')?.getBoundingClientRect() || 
                          document.querySelector('.file-content')?.getBoundingClientRect();
    
    // 如果元素已经在视口中间位置附近，则不需要重新滚动
    if (containerRect && 
        highlightRect.top > containerRect.top + 50 && 
        highlightRect.bottom < containerRect.bottom - 50) {
      console.log('当前高亮元素已经在视口中，不需要重新滚动');
      return true;
    }
  }
  
  // 输出当前高亮位置信息
  console.log(`当前高亮: ${currentHighlightIndex.value + 1}/${highlights.length}`);
  
  // 移除所有元素的active类和内联样式
  for (let i = 0; i < highlights.length; i++) {
    if (i !== currentHighlightIndex.value) { // 只处理非当前索引的元素
      highlights[i].classList.remove('active');
      highlights[i].style.backgroundColor = '';
      highlights[i].style.boxShadow = '';
      highlights[i].style.border = '';
    }
  }
  
  // 获取当前高亮元素
  const currentHighlight = highlights[currentHighlightIndex.value];
  
  if (!currentHighlight) {
    console.log('未找到指定索引的高亮元素');
    return false;
  }
  
  // 添加active类
  if (!currentHighlight.classList.contains('active')) {
    currentHighlight.classList.add('active');
  }
  
  // 短文本仍保留额外的视觉效果
  if (currentHighlight.textContent.length < 15 && !currentHighlight.classList.contains('short-text')) {
    currentHighlight.classList.add('short-text');
  }
  
  try {
    // 保存页面的原始滚动位置
    const originalPageScrollY = window.scrollY;
    const originalPageScrollX = window.scrollX;
    
    // 确定正确的滚动容器 - 明确指定文件预览容器
    const fileContent = document.querySelector('.file-content');
    const wordsContainer = document.querySelector('.words');
    
    // 首选滚动容器
    const scrollContainer = wordsContainer || fileContent || document.getElementById('fileShow');
    
    if (!scrollContainer) {
      console.error('未找到有效的滚动容器');
      return false;
    }
    
    // 获取准确的位置信息
    const highlightRect = currentHighlight.getBoundingClientRect();
    const containerRect = scrollContainer.getBoundingClientRect();
    
    // 精确计算高亮元素在文档中的相对位置
    // 使用offsetTop代替其他计算方式，获取更准确的位置
    let targetScrollTop;
    
    // 如果高亮元素不可见或在容器边缘，进行更精确的定位
    const isFullyVisible = 
      highlightRect.top >= containerRect.top + 50 && 
      highlightRect.bottom <= containerRect.bottom - 50;
      
    if (!isFullyVisible) {
      // 计算元素相对于容器的偏移量 - 使用更精确的计算方法
      let offsetFromTop = 0;
      let tmpElement = currentHighlight;
      
      // 计算元素相对于滚动容器的确切偏移
      while (tmpElement && tmpElement !== scrollContainer) {
        offsetFromTop += tmpElement.offsetTop;
        tmpElement = tmpElement.offsetParent;
      }
      
      const highlightHeight = highlightRect.height;
      const containerHeight = scrollContainer.clientHeight;
      
      // 将高亮元素居中显示在容器中
      targetScrollTop = offsetFromTop - (containerHeight / 2) + (highlightHeight / 2);
      
      // 确保滚动位置不超出范围
      targetScrollTop = Math.max(0, Math.min(targetScrollTop, scrollContainer.scrollHeight - containerHeight));
      
      // 直接设置滚动位置，不使用平滑滚动以确保精确定位
      scrollContainer.scrollTop = targetScrollTop;
    }
    
    // 添加明显的闪烁效果来增强视觉反馈
    enhanceHighlightVisibility(currentHighlight);
    
    // 恢复页面的原始滚动位置，防止整页滚动
    setTimeout(() => {
      window.scrollTo(originalPageScrollX, originalPageScrollY);
    }, 50);
    
    return true;
  } catch (error) {
    console.error('滚动到高亮位置失败:', error);
    
    // 备用方法
    try {
      // 最基本的滚动方法
      currentHighlight.scrollIntoView({
        behavior: 'auto',
        block: 'center'
      });
      
      // 应用最强的视觉突出显示
      enhanceHighlightVisibility(currentHighlight, true);
      return true;
    } catch (e) {
      console.error('所有方法都失败:', e);
      return false;
    }
  }
};

// 辅助函数：增强高亮可见性 - 保持原有实现，不添加新的样式效果
function enhanceHighlightVisibility(highlight, isEmergency = false) {
  // 添加防抖控制 - 如果元素在最近1秒内已经闪烁过，则不再闪烁
  if (highlight._lastFlashTime && Date.now() - highlight._lastFlashTime < 1000) {
    return;
  }
  
  // 记录最后闪烁时间
  highlight._lastFlashTime = Date.now();
  
  // 基本闪烁序列 - 减少为只有两步
  const flashSequence = [
    { bg: '#FFEB3B', shadow: '0 0 10px rgba(255, 235, 59, 0.7)', scale: '1.03' },
    { bg: '#FF9800', shadow: '0 0 8px rgba(255, 152, 0, 0.7)', scale: '1' }
  ];
  
  // 如果是紧急模式，只添加一个额外的闪烁效果
  if (isEmergency) {
    flashSequence.push({ bg: '#FF5722', shadow: '0 0 10px rgba(255, 87, 34, 0.7)', scale: '1.05' });
  }
  
  highlight.style.transition = 'all 0.2s ease';
  highlight.style.zIndex = '100';
  
  // 执行简化的闪烁动画
  let step = 0;
  
  // 清除之前可能存在的动画定时器
  if (highlight._flashTimer) {
    clearTimeout(highlight._flashTimer);
  }
  
  const flash = () => {
    if (step < flashSequence.length) {
      const { bg, shadow, scale } = flashSequence[step];
      highlight.style.backgroundColor = bg;
      highlight.style.boxShadow = shadow;
      highlight.style.transform = scale ? `scale(${scale})` : '';
      
      step++;
      // 使用更短的间隔
      highlight._flashTimer = setTimeout(flash, 150);
    } else {
      // 动画结束，设置最终样式
      highlight.style.backgroundColor = '#FF9800';
      highlight.style.boxShadow = '0 0 6px rgba(255, 152, 0, 0.6)';
      highlight.style.transform = 'scale(1)';
      
      // 不再设置animation属性，避免持续的动画效果
      highlight.style.animation = '';
    }
  };
  
  // 开始闪烁动画
  flash();
}

// 新增：监听文档尺寸变化，确保高亮元素的定位准确性
onMounted(() => {
  // 添加防抖变量，避免频繁触发
  let debounceTimer = null;
  let isProcessingScroll = false;
  
  // 创建一个MutationObserver来监听文档内容变化
  const documentObserver = new MutationObserver(mutations => {
    // 如果正在处理滚动，则不触发新的滚动请求
    if (isProcessingScroll) return;
    
    // 检查mutations是否包含我们关心的变化
    const shouldReposition = mutations.some(mutation => {
      // 只关注影响布局的变化：新增/删除节点或者属性变化（如果是样式属性）
      if (mutation.type === 'childList' && (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) {
        return true;
      }
      
      // 对于属性变化，只关心会影响元素大小/位置的属性
      if (mutation.type === 'attributes') {
        const layoutAttributes = ['style', 'class', 'width', 'height'];
        return layoutAttributes.includes(mutation.attributeName);
      }
      
      return false;
    });
    
    // 如果没有需要重新定位的变化，直接返回
    if (!shouldReposition) return;
    
    // 如果当前有高亮元素，使用防抖机制重新定位到当前高亮
    if (totalHighlights.value > 0 && !loading.value) {
      // 清除之前的定时器
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      
      // 设置新的定时器，延迟执行滚动
      debounceTimer = setTimeout(() => {
        const highlights = document.getElementsByClassName('highlight-text');
        if (highlights.length > 0 && currentHighlightIndex.value < highlights.length) {
          isProcessingScroll = true; // 标记正在处理滚动
          scrollToHighlight('current'); // 添加一个新的'current'选项来保持当前索引不变
          
          // 滚动处理完成后重置标记
          setTimeout(() => {
            isProcessingScroll = false;
          }, 100);
        }
      }, 500); // 延迟确保文档已完全渲染
    }
  });
  
  // 监听整个预览容器的内容变化
  const previewContainer = document.getElementById('fileShow');
  if (previewContainer) {
    documentObserver.observe(previewContainer, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class'] // 只监听style和class属性的变化
    });
  }
  
  // 在组件卸载时停止监听
  onUnmounted(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    documentObserver.disconnect();
  });
});

// 添加获取高亮统计信息的方法
const getHighlightStats = () => {
  const highlights = document.getElementsByClassName('highlight-text');
  totalHighlights.value = highlights.length;
  
  return {
    currentIndex: currentHighlightIndex.value,
    total: totalHighlights.value
  };
};

const hasValidSources = (sources) => {
  if (!sources || !Array.isArray(sources) || sources.length === 0)
    return false;
  return sources.some(
    (source) =>
      source && source.title && formatSourceTitle(source.title).trim() !== ''
  );
};

const getUniqueSources = (sources) => {
  if (!hasValidSources(sources)) return [];

  const sourceMap = new Map();
  
  sources.forEach(source => {
    if (!source || !source.title || !source.file_source) return;
    
    if (sourceMap.has(source.file_source)) {
      const existingSource = sourceMap.get(source.file_source);
      if (source.content && !existingSource.contents.includes(source.content)) {
        existingSource.contents.push(source.content);
      }
    } else {
      sourceMap.set(source.file_source, {
        title: source.title,
        contents: source.content ? [source.content] : [],
        content: source.content || '',
        file_source: source.file_source
      });
    }
  });

  return Array.from(sourceMap.values());
};

// 导出这些方法供父组件使用
defineExpose({
  handleSourceClick,
  formatSourceTitle,
  hasValidSources,
  getUniqueSources,
  scrollToHighlight,
  getHighlightStats
});
</script>

<style scoped>
.file-preview-container {
  height: 100%;
  width: 100%; /* 使用父容器的宽度 */
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* 靠右对齐 */
}

.file-content {
  border: 1px solid #3f63a8;
  overflow: auto;
  margin: 10px;
  height: calc(100% - 20px);
  width: calc(100% - 20px);
}

.words {
  width: 100%; 
  height: 80vh !important;
  overflow: auto;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.loading-spinner {
  color: #fff;
  padding: 15px 30px;
  border-radius: 4px;
  background-color: rgba(9, 39, 98, 0.8);
  font-size: 18px;
}
</style>

<style>
.docx-wrapper {
  background: none !important;
}

.highlight-text {
  background-color: #FFEB3B !important;
  color: #000 !important;
  display: inline !important;
  font-size: inherit !important;
  font-family: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
  border-radius: 2px;
  box-shadow: 0 0 2px #FF9800;
  padding: 0 2px;
  margin: 0 1px;
  border: 1px solid #FF9800;
  position: relative;
  z-index: 10;
}

/* 添加边框脉冲动画 */
@keyframes highlight-border-pulse {
  0% { border-color: #FF9800; box-shadow: 0 0 5px rgba(255, 152, 0, 0.7); }
  50% { border-color: #FF5722; box-shadow: 0 0 15px rgba(255, 87, 34, 0.9); }
  100% { border-color: #FF9800; box-shadow: 0 0 5px rgba(255, 152, 0, 0.7); }
}

/* 短文本高亮的样式增强 */
.highlight-text.short-text {
  border-radius: 3px;
  border: 2px solid #FF5722;
  margin: 0 2px;
  font-weight: bold !important;
  box-shadow: 0 0 5px rgba(255, 87, 34, 0.7);
  background-color: #FFF176 !important;
}

/* 激活状态的高亮元素 */
.highlight-text.active {
  background-color: #FF9800 !important;
  box-shadow: 0 0 12px rgba(255, 152, 0, 0.8);
  z-index: 100;
  position: relative;
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 文本高亮悬停效果 */
.highlight-text:hover {
  background-color: #FFC107 !important;
  cursor: pointer;
  box-shadow: 0 0 8px rgba(255, 193, 7, 0.8);
}

/* 样式适配文档结构 */
mark.highlight-text {
  display: inline-block !important;
  background-color: #FFEB3B !important;
}

/* 确保在Word文档预览中生效 */
.docx-wrapper mark.highlight-text, 
.docx-wrapper span.highlight-text,
.docx-wrapper .highlight-text {
  background-color: #FFEB3B !important;
  color: #000 !important;
  padding: 0 2px !important;
  border: 1px solid #FF9800 !important;
  margin: 0 1px !important;
}
</style> 
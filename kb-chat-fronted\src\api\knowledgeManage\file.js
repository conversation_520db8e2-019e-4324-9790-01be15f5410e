import request from "/src/utils/request";

//添加文件到知识库
export const fileToKb = (data) => {
  return request({
    url: "/add_file_to_kb",
    method: "post",
    data,
  });
};

//移除文件
export const delFileToKb = (data) => {
  return request({
    url: "/remove_file_from_kb",
    method: "delete",
    data,
  });
};

//处理文档
export const processDocs = (data) => {
  return request({
    url: "/process_documents",
    method: "post",
    data,
  });
};

//获取处理进度
export const processRate = (data) => {
  return request({
    url: "/process_progress",
  });
};

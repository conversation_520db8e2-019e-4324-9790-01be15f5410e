import { defineStore } from "pinia";
import { ref } from "vue";

export const useDialogStore = defineStore("dialog", () => {
  //新增知识管理弹窗
  const addKnowledgeShow = ref(false);
  //知识库弹窗类型
  const addKnowledgeType = ref("add");
  //知识库弹窗表单
  const editKnowledgeForm = ref({});
  //文档处理弹窗
  const fileProcessShow = ref(false);
  //文档处理弹窗表单
  const fileProcessForm = ref({});
  //上传文件后更新表单
  const loadedFileList = ref({});
  //选择的知识库
  const selectedkb = ref("");
  //添加新版本弹窗
  const addVersionShow = ref(false);
  //问答跳转的知识库页面
  const AskPushToKnowledge = ref(false);
  //当前使用的模型名称
  const currentModelName = ref("加载中...");
  //知识库谱系化弹窗
  const lineageDialogShow = ref(false);

  return {
    addKnowledgeShow,
    addKnowledgeType,
    editKnowledgeForm,
    fileProcessShow,
    fileProcessForm,
    loadedFileList,
    selectedkb,
    addVersionShow,
    AskPushToKnowledge,
    currentModelName,
    lineageDialogShow,
  };
});

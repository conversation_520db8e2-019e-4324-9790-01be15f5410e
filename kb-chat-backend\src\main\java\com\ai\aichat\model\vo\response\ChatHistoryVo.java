package com.ai.aichat.model.vo.response;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 聊天历史记录VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "聊天历史记录")
public class ChatHistoryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @Schema(description = "消息ID", example = "1")
    private Long id;

    /**
     * 消息角色
     */
    @Schema(description = "消息角色", example = "user")
    private String role;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容", example = "你好，请介绍一下自己")
    private String content;

    /**
     * 消息来源列表（知识库检索时使用）
     */
    @Schema(description = "消息来源列表")
    private List<SourceVo> sources;

    /**
     * 知识图谱数据
     */
    @Schema(description = "知识图谱数据")
    private String graph_data;

    /**
     * 问答对数据
     */
    @Schema(description = "问答对数据")
    private String qa_pair_data;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date create_time;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "chat_123456")
    private String session_id;

    /**
     * 聊天类型
     */
    @Schema(description = "聊天类型", example = "chat")
    private String chat_type;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", example = "default")
    private String kb_name;

    /**
     * 消息来源VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "消息来源")
    public static class SourceVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 来源文档名称
         */
        @Schema(description = "来源文档名称", example = "document.pdf")
        private String filename;

        /**
         * 来源内容片段
         */
        @Schema(description = "来源内容片段", example = "这是相关的文档内容...")
        private String content;

        /**
         * 相似度分数
         */
        @Schema(description = "相似度分数", example = "0.85")
        private Double score;

        /**
         * 页码或段落号
         */
        @Schema(description = "页码或段落号", example = "1")
        private Integer page;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.aichat.mapper.ConversationMapper">

    <resultMap id="BaseResultMap" type="com.ai.aichat.model.entity.Conversation">
            <id property="id" column="id" />
            <result property="sessionId" column="session_id" />
            <result property="title" column="title" />
            <result property="chatType" column="chat_type" />
            <result property="kbName" column="kb_name" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="messageCount" column="message_count" />
            <result property="userId" column="user_id" />
            <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="Base_Column_List">
        id,session_id,title,chat_type,kb_name,create_time,
        update_time,message_count,user_id,is_delete
    </sql>
</mapper>

package com.ai.aichat.model.dto.qa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 创建问答库请求DTO
 */
@Data
@Schema(description = "创建问答库请求")
public class QaBaseCreateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问答库名称
     */
    @NotBlank(message = "问答库名称不能为空")
    @Size(max = 100, message = "问答库名称长度不能超过100个字符")
    @Schema(description = "问答库名称", example = "技术问答库", required = true)
    private String kbName;

    /**
     * 问答库描述
     */
    @Size(max = 1000, message = "问答库描述长度不能超过1000个字符")
    @Schema(description = "问答库描述", example = "存储技术相关的问答对")
    private String description;
}

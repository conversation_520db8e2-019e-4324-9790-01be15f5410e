package com.ai.aichat.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import com.ai.aichat.websocket.DocumentProcessWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * WebSocket配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private DocumentProcessWebSocketHandler documentProcessWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册文档处理进度WebSocket处理器
        registry.addHandler(documentProcessWebSocketHandler, "/ws/document-process")
                .setAllowedOrigins("*"); // 在生产环境中应该设置具体的域名
    }
}

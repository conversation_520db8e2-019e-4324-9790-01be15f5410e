package com.ai.aichat.service;

import com.ai.aichat.websocket.DocumentProcessWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 文档处理进度服务
 */
@Slf4j
@Service
public class DocumentProcessProgressService {

    @Autowired
    private DocumentProcessWebSocketHandler webSocketHandler;

    /**
     * 发送处理开始消息
     */
    public void sendProcessStart(Long kbId, int totalFiles) {
        Map<String, Object> message = Map.of(
            "type", "process_start",
            "kbId", kbId,
            "totalFiles", totalFiles,
            "message", "开始处理文档",
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送处理开始消息: kbId={}, totalFiles={}", kbId, totalFiles);
    }

    /**
     * 发送文件处理开始消息
     */
    public void sendFileProcessStart(Long kbId, String fileName, int currentIndex, int totalFiles) {
        // 简单按文件数量计算进度：开始处理第N个文件
        int progress = (int) ((double) currentIndex / totalFiles * 100);

        Map<String, Object> message = Map.of(
            "type", "file_process_start",
            "kbId", kbId,
            "fileName", fileName,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "开始处理文件: " + fileName + " (" + (currentIndex + 1) + "/" + totalFiles + ")",
            "timestamp", System.currentTimeMillis()
        );

        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送文件处理开始消息: kbId={}, fileName={}, progress={}% ({}/{})",
                kbId, fileName, progress, currentIndex + 1, totalFiles);
    }

    /**
     * 发送文件解析完成消息
     */
    public void sendFileParseComplete(Long kbId, String fileName, int chunkCount, int currentIndex, int totalFiles) {
        // 简单按文件数量计算进度：解析完成后进度为 (当前文件索引 + 0.3) / 总文件数 * 100
        int progress = (int) ((double) (currentIndex + 0.3) / totalFiles * 100);

        Map<String, Object> message = Map.of(
            "type", "file_parse_complete",
            "kbId", kbId,
            "fileName", fileName,
            "chunkCount", chunkCount,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "文件解析完成，生成 " + chunkCount + " 个片段",
            "timestamp", System.currentTimeMillis()
        );

        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送文件解析完成消息: kbId={}, fileName={}, chunkCount={}, progress={}%",
                kbId, fileName, chunkCount, progress);
    }

    /**
     * 发送向量化开始消息
     */
    public void sendVectorizationStart(Long kbId, String fileName, int totalChunks, int currentIndex, int totalFiles) {
        // 向量化开始时进度为：(当前文件索引 + 0.5) / 总文件数 * 100
        int progress = (int) ((double) (currentIndex + 0.5) / totalFiles * 100);

        Map<String, Object> message = Map.of(
            "type", "vectorization_start",
            "kbId", kbId,
            "fileName", fileName,
            "totalChunks", totalChunks,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "开始向量化处理 " + totalChunks + " 个片段...",
            "timestamp", System.currentTimeMillis()
        );

        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送向量化开始消息: kbId={}, fileName={}, totalChunks={}, progress={}%",
                kbId, fileName, totalChunks, progress);
    }

    /**
     * 发送向量化进度消息
     */
    public void sendVectorizationProgress(Long kbId, String fileName, int processedChunks, int totalChunks,
                                        int currentIndex, int totalFiles) {
        int fileProgress = (int) ((double) processedChunks / totalChunks * 100);
        // 向量化进度：基础进度 + 当前文件向量化进度的30%
        int baseProgress = (int) ((double) (currentIndex + 0.5) / totalFiles * 100);
        int vectorizationProgress = (int) ((double) processedChunks / totalChunks * 30); // 向量化占当前文件30%的进度
        int overallProgress = Math.min(baseProgress + vectorizationProgress / totalFiles, 100);

        Map<String, Object> message = new HashMap<>();
        message.put("type", "vectorization_progress");
        message.put("kbId", kbId);
        message.put("fileName", fileName);
        message.put("processedChunks", processedChunks);
        message.put("totalChunks", totalChunks);
        message.put("fileProgress", fileProgress);
        message.put("currentIndex", currentIndex);
        message.put("totalFiles", totalFiles);
        message.put("progress", overallProgress);
        message.put("message", String.format("向量化进度: %d/%d 片段 (%d%%)", processedChunks, totalChunks, fileProgress));
        message.put("timestamp", System.currentTimeMillis());

        webSocketHandler.sendProgressToKb(kbId, message);
        log.debug("发送向量化进度消息: kbId={}, fileName={}, progress={}/{} ({}%)",
                kbId, fileName, processedChunks, totalChunks, fileProgress);
    }

    /**
     * 发送文件处理完成消息
     */
    public void sendFileProcessComplete(Long kbId, String fileName, int chunkCount, int currentIndex, int totalFiles) {
        // 文件完全处理完成，进度为：(当前文件索引 + 1) / 总文件数 * 100
        int progress = (int) ((double) (currentIndex + 1) / totalFiles * 100);

        Map<String, Object> message = Map.of(
            "type", "file_process_complete",
            "kbId", kbId,
            "fileName", fileName,
            "chunkCount", chunkCount,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "progress", progress,
            "message", "文件处理完成: " + fileName + " (" + (currentIndex + 1) + "/" + totalFiles + ")",
            "timestamp", System.currentTimeMillis()
        );

        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送文件处理完成消息: kbId={}, fileName={}, chunkCount={}, progress={}% ({}/{})",
                kbId, fileName, chunkCount, progress, currentIndex + 1, totalFiles);
    }

    /**
     * 发送处理完成消息
     */
    public void sendProcessComplete(Long kbId, int totalFiles, int totalChunks) {
        Map<String, Object> message = Map.of(
            "type", "process_complete",
            "kbId", kbId,
            "totalFiles", totalFiles,
            "totalChunks", totalChunks,
            "progress", 100,
            "message", "文档处理完成！共处理 " + totalFiles + " 个文件，生成 " + totalChunks + " 个片段",
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.info("发送处理完成消息: kbId={}, totalFiles={}, totalChunks={}", kbId, totalFiles, totalChunks);
    }

    /**
     * 发送处理失败消息
     */
    public void sendProcessError(Long kbId, String fileName, String errorMessage, int currentIndex, int totalFiles) {
        Map<String, Object> message = Map.of(
            "type", "process_error",
            "kbId", kbId,
            "fileName", fileName,
            "errorMessage", errorMessage,
            "currentIndex", currentIndex,
            "totalFiles", totalFiles,
            "message", "处理文件失败: " + fileName + " - " + errorMessage,
            "timestamp", System.currentTimeMillis()
        );
        
        webSocketHandler.sendProgressToKb(kbId, message);
        log.error("发送处理失败消息: kbId={}, fileName={}, error={}", kbId, fileName, errorMessage);
    }
}
